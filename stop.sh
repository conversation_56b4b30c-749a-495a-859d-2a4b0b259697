#!/bin/bash

# AIQuant7 系统停止脚本
# 用于安全停止前端和后端服务

set -e

echo "🛑 停止 AIQuant7 系统..."

# 停止服务的函数
stop_service() {
    local pid_file=$1
    local service_name=$2
    local process_pattern=$3
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            echo "🔧 停止 $service_name (PID: $pid)..."
            kill $pid
            
            # 等待进程结束
            for i in {1..10}; do
                if ! ps -p $pid > /dev/null 2>&1; then
                    echo "✅ $service_name 已停止"
                    break
                fi
                if [ $i -eq 10 ]; then
                    echo "⚠️  强制停止 $service_name..."
                    kill -9 $pid 2>/dev/null || true
                fi
                sleep 1
            done
        else
            echo "⚠️  $service_name PID文件存在但进程不存在"
        fi
        rm -f "$pid_file"
    else
        echo "🔍 查找 $service_name 进程..."
        local pids=$(pgrep -f "$process_pattern" 2>/dev/null || true)
        if [ -n "$pids" ]; then
            echo "🔧 停止 $service_name 进程: $pids"
            echo "$pids" | xargs kill 2>/dev/null || true
            sleep 2
            
            # 检查是否还有残留进程
            local remaining_pids=$(pgrep -f "$process_pattern" 2>/dev/null || true)
            if [ -n "$remaining_pids" ]; then
                echo "⚠️  强制停止残留 $service_name 进程: $remaining_pids"
                echo "$remaining_pids" | xargs kill -9 2>/dev/null || true
            fi
            echo "✅ $service_name 已停止"
        else
            echo "ℹ️  $service_name 未运行"
        fi
    fi
}

# 停止后端服务
stop_service ".backend.pid" "后端服务" "uvicorn.*backend.main:app"

# 停止前端服务
stop_service ".frontend.pid" "前端服务" "react-scripts start"

# 清理其他可能的进程
echo "🧹 清理相关进程..."

# 清理可能的Node.js进程
node_pids=$(pgrep -f "node.*start" 2>/dev/null || true)
if [ -n "$node_pids" ]; then
    echo "🔧 停止Node.js相关进程: $node_pids"
    echo "$node_pids" | xargs kill 2>/dev/null || true
fi

# 清理可能的Python进程
python_pids=$(pgrep -f "python.*uvicorn" 2>/dev/null || true)
if [ -n "$python_pids" ]; then
    echo "🔧 停止Python相关进程: $python_pids"
    echo "$python_pids" | xargs kill 2>/dev/null || true
fi

# 等待进程完全停止
sleep 2

# 检查端口是否已释放
check_port_free() {
    local port=$1
    local service=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  端口 $port ($service) 仍被占用"
        local pid=$(lsof -Pi :$port -sTCP:LISTEN -t)
        echo "   占用进程 PID: $pid"
        echo "   如需强制释放，请运行: kill -9 $pid"
        return 1
    else
        echo "✅ 端口 $port ($service) 已释放"
        return 0
    fi
}

echo ""
echo "🔍 检查端口状态..."
check_port_free 8080 "后端服务"
check_port_free 3000 "前端服务"

# 清理临时文件
echo ""
echo "🧹 清理临时文件..."
rm -f .backend.pid .frontend.pid

echo ""
echo "🎉 AIQuant7 系统已完全停止！"
echo ""
echo "💡 提示:"
echo "   🚀 重新启动: ./start.sh"
echo "   📊 查看状态: ./status.sh"
echo "   📝 查看日志: tail -f logs/backend.log 或 tail -f logs/frontend.log"
