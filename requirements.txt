# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 量化交易框架
vnpy==3.9.0
vnpy-ctp==*******
vnpy-ctastrategy==1.0.6
vnpy-ctabacktester==1.0.4
vnpy-spreadtrading==1.0.3
vnpy-algotrading==1.0.3
vnpy-optionmaster==1.0.3
vnpy-portfoliostrategy==1.0.3
vnpy-scripttrader==1.0.3
vnpy-chartwizard==1.0.2
vnpy-rpcservice==1.0.2
vnpy-excelrtd==1.0.2
vnpy-datamanager==1.0.3
vnpy-datarecorder==1.0.3
vnpy-riskmanager==1.0.3
vnpy-webtrader==1.0.2
vnpy-portfoliomanager==1.0.2

# 数据源
akshare==1.12.72
tushare==1.2.89
yfinance==0.2.28
ccxt==4.1.49

# 数据科学和机器学习
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4
scikit-learn==1.3.2
talib-binary==0.4.26
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# 自然语言处理
jieba==0.42.1
gensim==4.3.2
transformers==4.36.2
torch==2.1.2
sentence-transformers==2.2.2

# 数据存储 (文件格式)
pyarrow==14.0.2
feather-format==0.4.1
redis==5.0.1
h5py==3.10.0
tables==3.9.2

# 消息队列
kafka-python==2.0.2
aiokafka==0.10.0

# 网络请求
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0
websockets==12.0

# 工具库
loguru==0.7.2
python-dotenv==1.0.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
celery==5.3.4
schedule==1.2.1

# 监控和性能
prometheus-client==0.19.0
psutil==5.9.6
memory-profiler==0.61.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# 开发工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# 部署
gunicorn==21.2.0
docker==6.1.3
kubernetes==28.1.0

# 其他
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7
rich==13.7.0
typer==0.9.0
