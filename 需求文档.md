# 游资超短线交易策略系统需求文档

## 1. 项目概述

### 1.1 项目背景
本项目旨在将经典的游资超短线交易策略数字化，通过"三维共振系统"实现对市场情绪、热点题材和个股机会的智能化分析和监控。

### 1.2 项目目标
- 构建实时市场情绪监控系统
- 实现热点题材自动识别和追踪
- 提供精准的个股技术分析
- 建立完善的风险控制体系
- 支持策略回测和优化

### 1.3 核心策略原理
**市场情绪计算公式：**
```
情绪值 = (涨停股数 × 2) - (跌停股数 × 3) + (连板高度 × 5)
```

**五大情绪周期：**
- 冰点期 (< 20)：超跌反弹机会
- 回暖期 (20-50)：题材萌芽阶段
- 高潮期 (> 80)：追涨杀跌时期
- 退潮期：分化调整阶段
- 混沌期：方向不明阶段

## 2. 核心交易策略需求

### 2.1 三维共振系统架构

#### 2.1.1 第一维度：市场情绪周期策略
- **STR-001**: 系统应根据情绪值自动判断当前市场周期状态
- **STR-002**: 冰点期策略（情绪值 < 20）：
  - 重点关注超跌反弹机会
  - 筛选跌幅超过20%且基本面良好的个股
  - 采用小仓位试探性建仓（单票不超过5%）
  - 快进快出，反弹3-5%即止盈
- **STR-003**: 回暖期策略（情绪值 20-50）：
  - 重点挖掘题材萌芽机会
  - 关注政策风向和行业催化事件
  - 提前布局潜在热点，仓位逐步加大
  - 单票仓位可达10-15%
- **STR-004**: 高潮期策略（情绪值 > 80）：
  - 严格控制风险，避免追高
  - 重点关注龙头股分歧点机会
  - 采用"打板"策略，当日涨停次日开盘卖出
  - 单票仓位控制在5%以内
- **STR-005**: 退潮期策略：
  - 快速减仓，保留核心持仓
  - 重点关注真正有业绩支撑的个股
  - 避免新开仓位
- **STR-006**: 混沌期策略：
  - 空仓观望，等待明确方向
  - 加强风险控制，停止所有交易

#### 2.1.2 第二维度：热点题材筛选策略
- **STR-007**: 题材优先级排序算法：
  ```
  题材评分 = 政策权重×4 + 行业权重×3 + 事件权重×2 + 主题权重×1
  ```
- **STR-008**: 政策驱动型题材识别：
  - 监控国家政策发布和解读
  - 自动关联相关概念股
  - 计算政策影响度和持续性
  - 优先级最高，评分权重×4
- **STR-009**: 行业拐点型题材识别：
  - 监控行业数据和业绩拐点
  - 识别供需关系变化
  - 关注技术突破和商业化进程
  - 优先级次高，评分权重×3
- **STR-010**: 事件催化型题材识别：
  - 监控突发事件和重大新闻
  - 识别并购重组、业绩超预期等事件
  - 评估事件影响范围和持续时间
  - 中等优先级，评分权重×2
- **STR-011**: 主题炒作型题材识别：
  - 监控市场热点和资金流向
  - 识别纯概念炒作机会
  - 最低优先级，评分权重×1
- **STR-012**: 题材生命周期管理：
  - 萌芽期：提前布局，小仓位试探
  - 发酵期：逐步加仓，重点关注龙头
  - 高潮期：获利了结，避免追高
  - 分化期：精选个股，淘汰跟风股
  - 衰退期：全面撤离

#### 2.1.3 第三维度：个股技术分析策略
- **STR-013**: 龙头股识别算法：
  ```
  龙头评分 = (涨幅排名×0.3) + (成交额排名×0.25) + (连板高度×0.2) + (概念纯正度×0.15) + (流通市值适中度×0.1)
  ```
- **STR-014**: 技术形态要求：
  - 突破平台整理形态
  - 成交量有效放大（量比 > 2）
  - MACD金叉或即将金叉
  - RSI处于50-70区间（避免超买）
  - 布林带中轨之上运行
- **STR-015**: 资金流向分析：
  - 主力资金净流入连续3日为正
  - 大单成交占比 > 40%
  - 机构席位增持明显
  - 北向资金流入（如适用）
- **STR-016**: 量价关系验证：
  - 价涨量增：健康上涨形态
  - 价涨量缩：警惕后续乏力
  - 价跌量增：关注反弹机会
  - 价跌量缩：可能企稳信号

### 2.2 交易信号生成策略

#### 2.2.1 买入信号生成
- **STR-017**: 一级买入信号（必备条件）：
  - 市场情绪处于回暖期或高潮期初期
  - 所属题材评分 > 80分
  - 个股为该题材龙头或准龙头
  - 技术形态满足突破条件
- **STR-018**: 二级买入信号（加分条件）：
  - 政策催化明确且持续性强
  - 基本面有实际改善
  - 机构研报推荐或上调评级
  - 业绩预告超预期
- **STR-019**: 买入时机选择：
  - 优先选择分时图回调至均价线附近
  - 避免追涨停板（高潮期除外）
  - 开盘15分钟内或尾盘30分钟执行
  - 成交量温和放大时介入

#### 2.2.2 卖出信号生成
- **STR-020**: 止盈信号：
  - 个股涨幅达到15-20%（短线目标）
  - 技术指标出现背离信号
  - 市场情绪转入退潮期
  - 题材开始分化，个股跟不上龙头
- **STR-021**: 止损信号：
  - 固定止损：跌破买入价5%
  - 技术止损：跌破关键支撑位
  - 时间止损：持仓超过5个交易日未盈利
  - 基本面止损：重大利空消息
- **STR-022**: 减仓信号：
  - 单票盈利超过10%，减仓1/3
  - 市场情绪达到高潮期顶部，减仓1/2
  - 题材出现分化迹象，逐步减仓
  - 个股量价关系恶化，立即减仓

### 2.3 仓位管理策略

#### 2.3.1 整体仓位控制
- **STR-023**: 基于市场情绪的仓位策略：
  - 冰点期：总仓位 ≤ 30%
  - 回暖期：总仓位 50-70%
  - 高潮期：总仓位 ≤ 40%（防风险）
  - 退潮期：总仓位 ≤ 20%
  - 混沌期：总仓位 = 0%（空仓）
- **STR-024**: 单票仓位限制：
  - 个股最大仓位不超过20%
  - 同一题材总仓位不超过40%
  - 新开仓位单票不超过10%
  - 加仓后单票最高不超过15%

#### 2.3.2 资金分配策略
- **STR-025**: 核心-卫星配置：
  - 60%资金投资核心题材龙头股
  - 30%资金投资题材内优质个股
  - 10%资金用于机动操作和新机会
- **STR-026**: 分批建仓策略：
  - 首次建仓：计划仓位的40%
  - 确认趋势后加仓：计划仓位的30%
  - 突破关键位置再加仓：计划仓位的30%

### 2.4 风险控制策略

#### 2.4.1 系统性风险控制
- **STR-027**: 市场风险预警：
  - 连续3日跌停股数 > 涨停股数，降低仓位
  - 指数跌破重要支撑位，减仓观望
  - VIX恐慌指数超过警戒线，空仓避险
  - 政策环境发生重大变化，暂停交易
- **STR-028**: 流动性风险控制：
  - 避免投资流通市值过小股票（< 50亿）
  - 避免投资成交额过低股票（日均 < 5亿）
  - 单票持仓不超过该股日均成交额的5%
  - 预留足够资金应对赎回需求

#### 2.4.2 个股风险控制
- **STR-029**: 基本面风险控制：
  - 避免ST、*ST等风险警示股票
  - 避免连续亏损或业绩大幅下滑股票
  - 关注商誉减值、债务违约等风险
  - 监控大股东质押比例和减持计划
- **STR-030**: 技术面风险控制：
  - 避免投资长期下跌趋势股票
  - 关注技术指标背离和见顶信号
  - 避免追高买入涨幅过大股票
  - 重视成交量萎缩和量价背离

### 2.5 策略优化和调整

#### 2.5.1 参数动态调整
- **STR-031**: 情绪阈值调整：
  - 根据市场环境调整情绪周期阈值
  - 牛市提高高潮期阈值，熊市降低冰点期阈值
  - 季节性调整（如年底年初效应）
  - 重大事件期间的特殊调整
- **STR-032**: 仓位策略调整：
  - 根据账户规模调整仓位上限
  - 根据历史业绩调整风险偏好
  - 根据市场波动率调整仓位
  - 根据资金性质调整持仓周期

#### 2.5.2 策略有效性评估
- **STR-033**: 绩效评估指标：
  - 年化收益率 > 市场基准20%
  - 最大回撤 < 15%
  - 夏普比率 > 1.5
  - 胜率 > 60%
- **STR-034**: 策略迭代优化：
  - 每月进行策略效果回顾
  - 每季度进行参数优化调整
  - 每年进行策略逻辑升级
  - 持续学习市场新特征和新规律

## 3. 功能需求

### 3.1 市场情绪监控模块

#### 3.1.1 实时情绪计算
- **FR-001**: 系统应实时计算市场情绪值
- **FR-002**: 支持自定义情绪计算参数
- **FR-003**: 提供历史情绪数据查询和对比
- **FR-004**: 自动识别当前市场所处情绪周期

#### 3.1.2 涨跌停分析
- **FR-005**: 实时统计涨停、跌停股票数量
- **FR-006**: 分析涨停股的板块分布和连板情况
- **FR-007**: 监控跌停股的行业集中度
- **FR-008**: 提供涨跌停股票详细列表

#### 3.1.3 连板梯队跟踪
- **FR-009**: 实时更新连板股票梯队
- **FR-010**: 分析连板股的持续性和成功率
- **FR-011**: 预警连板断板风险
- **FR-012**: 提供连板股票历史表现统计

### 3.2 热点题材筛选模块

#### 3.2.1 题材识别与分类
- **FR-013**: 自动识别当日热点题材
- **FR-014**: 按优先级分类：政策驱动 > 行业拐点 > 事件催化 > 主题炒作
- **FR-015**: 跟踪题材生命周期（萌芽→发酵→高潮→分化→衰退）
- **FR-016**: 提供题材新闻事件关联分析

#### 3.2.2 龙头股识别
- **FR-017**: 智能识别各题材龙头股
- **FR-018**: 分析龙头股市场表现和资金流向
- **FR-019**: 监控龙头股换手率和量价关系
- **FR-020**: 预警龙头股见顶信号

#### 3.2.3 题材强度评估
- **FR-021**: 计算题材整体涨幅和成交额
- **FR-022**: 评估题材持续性和爆发力
- **FR-023**: 对比不同题材的相对强弱
- **FR-024**: 提供题材投资价值评分

### 3.3 个股技术分析模块

#### 3.3.1 技术指标计算
- **FR-025**: 支持主流技术指标：MACD、RSI、KDJ、布林带等
- **FR-026**: 提供自定义技术指标配置
- **FR-027**: 多时间周期技术分析
- **FR-028**: 技术指标背离识别

#### 3.3.2 K线形态识别
- **FR-029**: 自动识别经典K线形态
- **FR-030**: 支撑阻力位自动标注
- **FR-031**: 突破形态实时预警
- **FR-032**: 形态成功率统计分析

#### 3.3.3 量价分析
- **FR-033**: 成交量异常监控
- **FR-034**: 量价背离识别
- **FR-035**: 资金流向分析
- **FR-036**: 主力资金动向跟踪

### 3.4 风险控制模块

#### 3.4.1 止损管理
- **FR-037**: 固定比例止损（默认5%）
- **FR-038**: 技术止损（破位、形态）
- **FR-039**: 时间止损（持仓时间限制）
- **FR-040**: 动态止损（追踪止损）

#### 3.4.2 仓位管理
- **FR-041**: 基于市场情绪的仓位控制
- **FR-042**: 单票仓位限制
- **FR-043**: 板块集中度控制
- **FR-044**: 资金使用效率监控

#### 3.4.3 风险预警
- **FR-045**: 市场系统性风险预警
- **FR-046**: 个股异常波动预警
- **FR-047**: 流动性风险监控
- **FR-048**: 黑天鹅事件应急机制

### 3.5 交易执行模块

#### 3.5.1 信号生成
- **FR-049**: 综合技术分析生成买卖信号
- **FR-050**: 基于情绪周期的择时信号
- **FR-051**: 题材轮动信号识别
- **FR-052**: 信号强度评级和过滤

#### 3.5.2 交易管理
- **FR-053**: 交易订单管理和跟踪
- **FR-054**: 成交记录和统计分析
- **FR-055**: 交易成本计算
- **FR-056**: 业绩归因分析

#### 3.5.3 策略回测
- **FR-057**: 历史数据回测功能
- **FR-058**: 策略参数优化
- **FR-059**: 回测结果可视化
- **FR-060**: 策略有效性评估

## 4. 非功能性需求

### 4.1 性能需求
- **NFR-001**: 市场数据处理延迟 < 100ms
- **NFR-002**: 系统响应时间 < 2秒
- **NFR-003**: 支持1000+并发用户
- **NFR-004**: 数据存储容量 > 10TB

### 4.2 可靠性需求
- **NFR-005**: 系统可用性 ≥ 99.9%
- **NFR-006**: 数据备份恢复 < 1小时
- **NFR-007**: 故障自动切换 < 30秒
- **NFR-008**: 数据一致性保证

### 4.3 安全性需求
- **NFR-009**: 用户认证和权限管理
- **NFR-010**: 数据传输加密
- **NFR-011**: 交易操作日志记录
- **NFR-012**: 敏感数据脱敏处理

### 4.4 可扩展性需求
- **NFR-013**: 支持多市场扩展（A股、港股、美股）
- **NFR-014**: 微服务架构，模块独立部署
- **NFR-015**: 支持云原生部署
- **NFR-016**: 插件化策略扩展

### 4.5 易用性需求
- **NFR-017**: 响应式设计，支持多终端
- **NFR-018**: 操作界面直观易懂
- **NFR-019**: 个性化配置和定制
- **NFR-020**: 多语言支持

## 5. 数据需求

### 5.1 实时行情数据
- **DR-001**: Level-1行情数据（价格、成交量、买卖盘）
- **DR-002**: 分笔成交数据
- **DR-003**: 委托队列数据
- **DR-004**: 指数和板块数据

### 5.2 基础数据
- **DR-005**: 股票基本信息
- **DR-006**: 财务数据
- **DR-007**: 公司公告
- **DR-008**: 行业分类数据

### 5.3 市场数据
- **DR-009**: 涨跌停统计
- **DR-010**: 连板统计
- **DR-011**: 资金流向数据
- **DR-012**: 龙虎榜数据

### 5.4 新闻舆情数据
- **DR-013**: 财经新闻
- **DR-014**: 政策公告
- **DR-015**: 研报信息
- **DR-016**: 社交媒体情绪

## 6. 用户角色和权限

### 6.1 用户角色定义
- **管理员**: 系统配置、用户管理、数据维护
- **策略师**: 策略开发、参数调优、回测分析
- **交易员**: 信号接收、交易执行、风险控制
- **普通用户**: 行情查看、基础分析功能

### 6.2 权限矩阵
- **数据访问权限**: 基于用户级别分配数据访问范围
- **功能权限**: 不同角色可使用的功能模块
- **操作权限**: 交易执行、参数修改等敏感操作权限
- **时间权限**: 特定时间段的系统访问权限

## 7. 接口需求

### 7.1 数据接口
- **API-001**: 证券数据供应商接口（如Wind、同花顺）
- **API-002**: 新闻数据接口
- **API-003**: 宏观经济数据接口
- **API-004**: 第三方技术分析接口

### 7.2 交易接口
- **API-005**: 券商交易接口
- **API-006**: 模拟交易接口
- **API-007**: 风险管理接口
- **API-008**: 清算结算接口

### 7.3 消息接口
- **API-009**: 短信通知接口
- **API-010**: 邮件推送接口
- **API-011**: 移动推送接口
- **API-012**: WebSocket实时通信接口

## 8. 部署需求

### 8.1 硬件需求
- **服务器配置**: CPU 16核+, 内存 64GB+, 存储 2TB+ SSD
- **网络带宽**: 1Gbps专线接入
- **机房要求**: 双路供电、恒温恒湿、7×24监控

### 8.2 软件环境
- **操作系统**: Linux CentOS 7.0+
- **数据库**: PostgreSQL 13+, Redis 6.0+
- **消息队列**: Apache Kafka 2.8+
- **容器化**: Docker + Kubernetes

### 8.3 监控运维
- **性能监控**: CPU、内存、磁盘、网络监控
- **业务监控**: 交易量、延迟、成功率监控
- **日志管理**: 集中化日志收集和分析
- **备份策略**: 自动备份、异地容灾

## 9. 验收标准

### 9.1 功能验收
- 所有功能需求100%实现
- 关键业务流程端到端测试通过
- 用户接受度测试满意度 > 85%

### 9.2 性能验收
- 系统响应时间满足性能需求
- 并发用户数达到设计指标
- 7×24小时稳定运行测试通过

### 9.3 安全验收
- 安全渗透测试通过
- 数据加密和权限控制验证
- 业务连续性测试通过

## 10. 项目里程碑

### 10.1 第一阶段（1-2个月）
- 核心架构搭建
- 数据接入和处理
- 基础UI框架

### 10.2 第二阶段（3-4个月）
- 市场情绪监控功能
- 热点题材筛选功能
- 个股技术分析功能

### 10.3 第三阶段（5-6个月）
- 风险控制模块
- 交易执行模块
- 策略回测功能

### 10.4 第四阶段（7-8个月）
- 系统优化和测试
- 用户培训和部署
- 上线运行和维护

## 11. 风险评估

### 11.1 技术风险
- 数据接口稳定性风险
- 高并发性能风险
- 技术选型风险

### 11.2 业务风险
- 策略有效性风险
- 市场环境变化风险
- 监管政策风险

### 11.3 项目风险
- 开发进度风险
- 人员流动风险
- 需求变更风险

### 11.4 风险应对措施
- 技术预研和原型验证
- 分阶段迭代开发
- 多供应商备选方案
- 完善的测试和监控体系 