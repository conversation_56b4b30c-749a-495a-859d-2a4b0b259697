# AIQuant7 - 智能量化交易系统

<div align="center">

![AIQuant7 Logo](https://img.shields.io/badge/AIQuant7-v1.0.0-blue.svg)
![Python](https://img.shields.io/badge/Python-3.8+-green.svg)
![React](https://img.shields.io/badge/React-18+-blue.svg)
![FastAPI](https://img.shields.io/badge/FastAPI-Latest-red.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

**基于人工智能的现代化量化交易系统**

[功能特性](#-功能特性) • [快速开始](#-快速开始) • [系统架构](#-系统架构) • [API文档](#-api文档) • [部署指南](#-部署指南)

</div>

## 🎯 项目简介

AIQuant7 是一个集成了人工智能技术的量化交易系统，专注于A股市场的实时数据分析、情绪计算、题材识别、龙头股挖掘和交易信号生成。系统采用现代化的技术栈，提供直观的Web界面和强大的API服务。

### 🌟 核心亮点

- **🧠 智能分析**: 基于AI的市场情绪分析和题材识别
- **⚡ 实时数据**: 毫秒级实时市场数据获取和处理
- **🎨 现代界面**: 基于React + Ant Design的响应式Web界面
- **🚀 高性能**: FastAPI构建的高并发API服务
- **📊 可视化**: 丰富的图表和数据可视化组件
- **🔧 易部署**: 轻量级架构，支持快速部署

## ✨ 功能特性

### 📈 市场数据分析
- **实时行情**: 获取A股实时涨停、跌停数据
- **市场概览**: 全市场统计数据和趋势分析
- **个股查询**: 支持单只股票详细数据查询
- **新闻资讯**: 实时财经新闻获取和分析

### 🧠 智能策略引擎
- **情绪分析**: 基于市场数据的情绪指数计算
- **题材识别**: AI驱动的热点题材自动识别
- **龙头挖掘**: 智能识别各题材龙头股票
- **信号生成**: 多因子交易信号生成和评分

### 💼 投资组合管理
- **持仓管理**: 投资组合实时监控
- **风险控制**: 多维度风险指标计算
- **收益分析**: 详细的收益归因分析
- **回测系统**: 策略历史回测功能

### 🎨 用户界面
- **仪表盘**: 一站式市场概览
- **数据可视化**: 交互式图表和指标展示
- **实时更新**: 数据自动刷新和推送
- **响应式设计**: 支持桌面和移动端

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- 4GB+ 内存

### 一键启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd AIQuant7

# 2. 安装依赖
pip install -r requirements.txt
cd frontend && npm install && cd ..

# 3. 启动后端
python -m uvicorn backend.main:app --host 0.0.0.0 --port 8080 --reload &

# 4. 启动前端
cd frontend && npm start
```

### 访问系统
- 🌐 **前端界面**: http://localhost:3000
- 📖 **API文档**: http://localhost:8080/docs
- 🏥 **健康检查**: http://localhost:8080/health

## 🏗 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   数据存储      │
│                 │    │                 │    │                 │
│  React + Ant    │◄──►│  FastAPI +      │◄──►│  CSV/Feather    │
│  Design         │    │  Python         │    │  Files          │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   策略引擎      │              │
         │              │                 │              │
         └──────────────►│  情绪分析       │◄─────────────┘
                        │  题材识别       │
                        │  龙头挖掘       │
                        │  信号生成       │
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │   数据源        │
                        │                 │
                        │  东方财富       │
                        │  Tushare        │
                        │  新闻API        │
                        └─────────────────┘
```

### 技术栈

#### 前端
- **React 18**: 现代化前端框架
- **Ant Design**: 企业级UI组件库
- **ECharts**: 数据可视化图表库
- **Axios**: HTTP客户端

#### 后端
- **FastAPI**: 高性能Web框架
- **Pydantic**: 数据验证和序列化
- **Pandas**: 数据处理和分析
- **NumPy**: 数值计算

#### 数据存储
- **CSV/Feather**: 轻量级文件存储
- **Redis**: 可选的缓存层
- **SQLite**: 可选的关系数据库

## 📊 API文档

### 核心接口

#### 市场数据
```http
GET /api/v1/market/realtime          # 实时市场数据
GET /api/v1/market/stock/{symbol}    # 个股实时数据
GET /api/v1/market/news              # 市场新闻
```

#### 情绪分析
```http
GET /api/v1/sentiment/current        # 当前市场情绪
GET /api/v1/sentiment/history        # 历史情绪数据
```

#### 题材分析
```http
GET /api/v1/themes/ranking           # 题材排行榜
GET /api/v1/themes/{theme_id}        # 题材详情
```

#### 龙头股分析
```http
GET /api/v1/leaders/ranking          # 龙头股排行
GET /api/v1/leaders/{symbol}/analysis    # 龙头股分析
```

#### 交易信号
```http
GET /api/v1/signals/latest           # 最新交易信号
GET /api/v1/signals/{symbol}         # 个股信号
```

#### 投资组合
```http
GET /api/v1/positions/current        # 当前持仓
GET /api/v1/positions/metrics        # 组合指标
```

### 响应格式
```json
{
  "status": "success",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2024-12-17T15:30:00Z",
  "source": "realtime"
}
```

## 📁 项目结构

```
AIQuant7/
├── backend/                 # 后端代码
│   ├── api/                # API路由
│   ├── core/               # 核心业务逻辑
│   ├── data/               # 数据管理
│   ├── strategy/           # 策略引擎
│   └── main.py             # 应用入口
├── frontend/               # 前端代码
│   ├── public/             # 静态资源
│   ├── src/                # 源代码
│   │   ├── components/     # 组件
│   │   ├── pages/          # 页面
│   │   └── utils/          # 工具函数
│   └── package.json        # 依赖配置
├── config/                 # 配置文件
├── data/                   # 数据存储目录
├── logs/                   # 日志文件
├── tests/                  # 测试代码
├── requirements.txt        # Python依赖
├── DEPLOYMENT.md           # 部署文档
└── README.md              # 项目说明
```

## 🔧 配置说明

### 基础配置
```python
# config/settings.py
class Settings:
    # 服务配置
    API_HOST = "0.0.0.0"
    API_PORT = 8080
    FRONTEND_PORT = 3000
    
    # 数据配置
    DATA_DIR = "data"
    ENABLE_TUSHARE = False
    TUSHARE_TOKEN = ""
    
    # 缓存配置
    ENABLE_REDIS = False
    REDIS_URL = "redis://localhost:6379"
```

## 🧪 测试

### 运行测试
```bash
# 集成测试
python test_system_integration.py

# 单元测试
python -m pytest tests/
```

## 📚 部署指南

详细的部署说明请参考 [DEPLOYMENT.md](DEPLOYMENT.md)

### 开发环境
```bash
# 后端开发服务器
python -m uvicorn backend.main:app --reload

# 前端开发服务器
cd frontend && npm start
```

## 📞 联系我们

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: GitHub Issues
- 💬 讨论: GitHub Discussions

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**

Made with ❤️ by AIQuant7 Team

</div>
