# 游资超短线交易策略系统

## 项目简介

这是一个基于"三维共振系统"的游资超短线交易策略系统，通过实时监控市场情绪、智能识别热点题材、精准分析个股技术形态，为超短线交易提供系统化的解决方案。

### 核心特性

- 🎯 **三维共振策略**: 市场情绪 + 题材筛选 + 个股技术分析
- 📊 **实时情绪计算**: 基于涨跌停数据的市场情绪五周期识别
- 🔥 **智能题材识别**: 自动发现和评分热点题材
- 📈 **龙头股筛选**: 多维度算法识别题材龙头股
- ⚡ **高频数据处理**: 毫秒级实时数据处理和信号生成
- 🛡️ **完善风控系统**: 多层次风险控制和仓位管理
- 📱 **现代化界面**: 响应式Web界面，支持多终端访问

## 技术架构

### 核心技术栈
- **后端**: Python + FastAPI + VNPY
- **数据库**: PostgreSQL + Redis + InfluxDB
- **消息队列**: Apache Kafka
- **前端**: React + TypeScript + Ant Design
- **容器化**: Docker + Kubernetes
- **监控**: Prometheus + Grafana

### 数据源
- **免费数据**: akshare + tushare
- **实时行情**: 聚宽/米筐商业数据源
- **新闻数据**: 东财爬虫 + 雪球API
- **基本面数据**: 同花顺iFinD API

## 快速开始

### 环境要求
- Python 3.9+
- Docker & Docker Compose
- 8GB+ RAM
- 100GB+ 存储空间

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/super-short-term-trading.git
cd super-short-term-trading
```

### 2. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，填入必要的配置信息
```

### 3. 启动服务
```bash
# 启动基础设施服务
docker-compose up -d postgres redis influxdb kafka

# 启动应用服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 4. 访问系统
- 主界面: http://localhost:3001
- API文档: http://localhost:8080/docs
- Grafana监控: http://localhost:3000 (admin/admin)
- Prometheus: http://localhost:9090

## 策略算法详解

### 1. 市场情绪计算
```python
情绪值 = (涨停股数 × 2) - (跌停股数 × 3) + (连板高度 × 5)

五大情绪周期：
- 冰点期 (< 20): 超跌反弹机会
- 回暖期 (20-50): 题材萌芽阶段  
- 高潮期 (> 80): 追涨杀跌时期
- 退潮期: 分化调整阶段
- 混沌期: 方向不明阶段
```

### 2. 题材评分算法
```python
题材评分 = 政策权重×4 + 行业权重×3 + 事件权重×2 + 主题权重×1

优先级排序：
1. 政策驱动型 (权重×4)
2. 行业拐点型 (权重×3)
3. 事件催化型 (权重×2)
4. 主题炒作型 (权重×1)
```

### 3. 龙头股识别
```python
龙头评分 = (涨幅排名×0.3) + (成交额排名×0.25) + (连板高度×0.2) + 
          (概念纯正度×0.15) + (流通市值适中度×0.1)
```

### 4. 仓位管理策略
```python
基于市场情绪的仓位控制：
- 冰点期：总仓位 ≤ 30%
- 回暖期：总仓位 50-70%
- 高潮期：总仓位 ≤ 40% (防风险)
- 退潮期：总仓位 ≤ 20%
- 混沌期：总仓位 = 0% (空仓)
```

## 使用指南

### 1. 数据配置
```python
# 配置数据源API密钥
TUSHARE_TOKEN = "your_tushare_token"
WIND_USERNAME = "your_wind_username"
WIND_PASSWORD = "your_wind_password"
```

### 2. 策略参数调整
```python
# 在config.py中调整策略参数
strategy_config = {
    "sentiment_weights": {
        "limit_up_weight": 2.0,
        "limit_down_weight": -3.0,
        "continuous_board_weight": 5.0
    },
    "position_limits": {
        "max_single_position": 0.15,  # 单票最大仓位15%
        "max_theme_position": 0.40    # 同题材最大仓位40%
    }
}
```

### 3. 回测验证
```python
# 运行策略回测
python -m scripts.backtest --start-date 2023-01-01 --end-date 2023-12-31

# 查看回测结果
python -m scripts.analyze_backtest --result-file backtest_results.json
```

### 4. 实盘交易
```python
# 启动实盘交易（请谨慎操作）
python -m scripts.live_trading --dry-run  # 先以模拟模式运行
```

## API文档

### 市场情绪API
```bash
# 获取当前市场情绪
GET /api/v1/sentiment/current

# 获取历史情绪数据
GET /api/v1/sentiment/history?start_date=2023-01-01&end_date=2023-12-31
```

### 题材分析API
```bash
# 获取热点题材排行
GET /api/v1/themes/ranking

# 获取题材详细信息
GET /api/v1/themes/{theme_id}
```

### 交易信号API
```bash
# 获取交易信号
GET /api/v1/signals/latest

# 订阅实时信号推送
WebSocket: ws://localhost:8080/ws/signals
```

## 部署指南

### 开发环境部署
```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export PYTHONPATH=$PWD

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8080
```

### 生产环境部署
```bash
# 使用Docker Compose
docker-compose -f docker-compose.prod.yml up -d

# 或使用Kubernetes
kubectl apply -f k8s/
```

### 性能调优
- 调整Kafka分区数量以提高并发处理能力
- 配置InfluxDB数据保留策略避免存储溢出
- 使用Redis集群提高缓存性能
- 配置Nginx负载均衡分散Web请求

## 监控和运维

### 关键监控指标
- **系统性能**: CPU、内存、网络、磁盘IO
- **业务指标**: 交易量、信号准确率、收益率、回撤
- **数据质量**: 数据延迟、缺失率、异常值检测
- **服务健康**: 服务可用性、响应时间、错误率

### 告警配置
```yaml
# prometheus/alerts.yml
groups:
- name: trading_system
  rules:
  - alert: HighLatency
    expr: http_request_duration_seconds > 2
    for: 5m
    annotations:
      summary: "API响应时间过长"
  
  - alert: HighDrawdown
    expr: portfolio_drawdown > 0.10
    for: 1m
    annotations:
      summary: "投资组合回撤超过10%"
```

### 日志管理
```python
# 配置结构化日志
import loguru

logger.add(
    "logs/trading_{time}.log",
    rotation="1 day",
    retention="30 days",
    format="{time} | {level} | {module} | {message}",
    serialize=True
)
```

## 常见问题

### Q: 如何添加新的数据源？
A: 在`data_manager.py`中实现数据源接口，并在配置文件中添加相应配置。

### Q: 如何自定义交易策略？
A: 继承`CtaTemplate`类，实现自己的策略逻辑，并在策略管理器中注册。

### Q: 系统延迟如何优化？
A: 检查网络连接、调整Kafka配置、优化数据库查询、使用更快的硬件。

### Q: 如何处理数据异常？
A: 系统内置数据质量检查，异常数据会被自动标记和过滤。

## 风险声明

⚠️ **重要提示**: 
- 本系统仅供学习和研究使用
- 股市有风险，投资需谨慎
- 任何交易决策请以实际市场情况为准
- 使用本系统进行实盘交易造成的损失自负

## 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页: https://github.com/your-repo/super-short-term-trading
- 问题反馈: https://github.com/your-repo/super-short-term-trading/issues
- 邮箱: <EMAIL>

## 致谢

感谢以下开源项目的支持：
- [VNPY](https://github.com/vnpy/vnpy) - 优秀的量化交易框架
- [akshare](https://github.com/akfamily/akshare) - 免费的金融数据接口
- [FastAPI](https://github.com/tiangolo/fastapi) - 现代的Web框架 