#!/usr/bin/env python3
"""
AIQuant7 系统测试脚本
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.core.sentiment.sentiment_engine import sentiment_engine
from backend.core.theme.theme_engine import theme_engine
from backend.core.leader.leader_engine import leader_engine
from backend.core.signal.signal_engine import signal_engine
from backend.core.position.position_manager import position_manager
from backend.data.data_manager import data_manager


async def test_sentiment_engine():
    """测试市场情绪引擎"""
    print("🧪 测试市场情绪引擎...")
    
    # 模拟市场数据
    mock_market_data = {
        'timestamp': '2024-01-15 09:30:00',
        'limit_up_count': 25,
        'limit_down_count': 8,
        'max_continuous_boards': 3,
        'volume_factor': 1.2
    }
    
    # 测试情绪计算
    sentiment_score = sentiment_engine.calculate_sentiment_score(mock_market_data)
    print(f"   情绪评分: {sentiment_score:.2f}")
    
    # 测试情绪周期识别
    sentiment_phase = sentiment_engine.get_sentiment_phase(sentiment_score)
    print(f"   情绪周期: {sentiment_phase}")
    
    # 测试完整情绪分析
    sentiment_data = sentiment_engine.process_market_data(mock_market_data)
    print(f"   市场温度: {sentiment_data.market_temperature:.1f}")
    print(f"   趋势方向: {sentiment_data.trend_direction}")
    print(f"   波动率指数: {sentiment_data.volatility_index:.3f}")
    
    print("✅ 市场情绪引擎测试完成\n")


async def test_theme_engine():
    """测试题材识别引擎"""
    print("🧪 测试题材识别引擎...")
    
    # 模拟新闻数据
    mock_news = [
        {
            'title': '国家发改委发布新能源汽车产业发展规划',
            'content': '新能源汽车产业将迎来重大政策支持，锂电池、充电桩等相关产业链受益',
            'publish_time': '2024-01-15 08:00:00',
            'source': '新华社'
        },
        {
            'title': '人工智能芯片技术获得重大突破',
            'content': '国产AI芯片在算力和能效方面实现重大突破，半导体产业迎来新机遇',
            'publish_time': '2024-01-15 09:00:00',
            'source': '科技日报'
        },
        {
            'title': '央行宣布降准释放流动性',
            'content': '央行决定下调存款准备金率，向市场释放长期资金，支持实体经济发展',
            'publish_time': '2024-01-15 10:00:00',
            'source': '中国证券报'
        }
    ]
    
    # 测试题材识别
    themes = theme_engine.identify_themes_from_news(mock_news)
    print(f"   识别到 {len(themes)} 个题材:")
    
    for i, theme in enumerate(themes[:3], 1):
        print(f"   {i}. {theme['theme_name']} (评分: {theme['theme_score']:.2f})")
        print(f"      类型: {theme['theme_type']}")
        print(f"      关键词: {', '.join(theme['keywords'][:5])}")
    
    print("✅ 题材识别引擎测试完成\n")


async def test_leader_engine():
    """测试龙头股识别引擎"""
    print("🧪 测试龙头股识别引擎...")
    
    # 模拟候选股票数据
    mock_stocks = [
        {
            'symbol': '300750',
            'name': '宁德时代',
            'industry': '电池制造',
            'market_cap': 1200.0,
            'continuous_boards': 0,
            'change_pct': 5.2,
            'volume': 1000000,
            'turnover_rate': 3.5
        },
        {
            'symbol': '002594',
            'name': '比亚迪',
            'industry': '新能源汽车',
            'market_cap': 800.0,
            'continuous_boards': 1,
            'change_pct': 8.1,
            'volume': 800000,
            'turnover_rate': 4.2
        },
        {
            'symbol': '600036',
            'name': '招商银行',
            'industry': '银行',
            'market_cap': 1500.0,
            'continuous_boards': 0,
            'change_pct': 2.1,
            'volume': 500000,
            'turnover_rate': 1.8
        }
    ]
    
    # 测试龙头股识别
    leaders = leader_engine.identify_theme_leaders(
        theme_name="新能源汽车",
        candidate_stocks=mock_stocks,
        theme_keywords=["新能源", "电池", "汽车"],
        top_n=5
    )
    
    print(f"   识别到 {len(leaders)} 只龙头股:")
    for i, leader in enumerate(leaders, 1):
        print(f"   {i}. {leader.name} ({leader.symbol})")
        print(f"      龙头评分: {leader.leader_score:.3f}")
        print(f"      置信度: {leader.confidence_level}")
        print(f"      是否龙头: {'是' if leader.is_leader else '否'}")
    
    print("✅ 龙头股识别引擎测试完成\n")


async def test_signal_engine():
    """测试交易信号引擎"""
    print("🧪 测试交易信号引擎...")
    
    # 模拟市场数据
    mock_market_data = {
        'timestamp': '2024-01-15 14:30:00',
        'limit_up_count': 30,
        'limit_down_count': 5,
        'max_continuous_boards': 4
    }
    
    # 测试批量信号生成
    test_symbols = ["300750", "002594", "600036"]
    signals = signal_engine.batch_generate_signals(test_symbols, mock_market_data)
    
    print(f"   生成 {len(signals)} 个交易信号:")
    for i, signal in enumerate(signals, 1):
        print(f"   {i}. {signal.symbol}")
        print(f"      信号类型: {signal.signal_type.value}")
        print(f"      信号强度: {signal.signal_strength.value}")
        print(f"      置信度: {signal.confidence:.3f}")
        print(f"      综合评分: {signal.composite_score:.3f}")
        print(f"      风险等级: {signal.risk_level}")
    
    # 测试信号摘要
    summary = signal_engine.get_signal_summary()
    print(f"   信号摘要: {summary}")
    
    print("✅ 交易信号引擎测试完成\n")


async def test_position_manager():
    """测试仓位管理器"""
    print("🧪 测试仓位管理器...")
    
    # 测试投资组合指标
    metrics = position_manager.get_portfolio_metrics()
    print(f"   总资产: {metrics.total_value:,.2f}")
    print(f"   现金余额: {metrics.cash_balance:,.2f}")
    print(f"   持仓数量: {metrics.position_count}")
    print(f"   总盈亏: {metrics.total_pnl:,.2f}")
    print(f"   总盈亏率: {metrics.total_pnl_pct:.2%}")
    
    # 测试持仓摘要
    summary = position_manager.get_position_summary()
    print(f"   持仓摘要: {summary}")
    
    print("✅ 仓位管理器测试完成\n")


async def test_data_manager():
    """测试数据管理器"""
    print("🧪 测试数据管理器...")
    
    # 测试股票基础信息获取（可能会失败，因为需要网络）
    try:
        stock_info = await data_manager.get_stock_basic_info()
        if stock_info is not None:
            print(f"   获取股票基础信息成功: {len(stock_info)} 只股票")
        else:
            print("   获取股票基础信息失败（可能是网络问题）")
    except Exception as e:
        print(f"   获取股票基础信息异常: {e}")
    
    # 测试实时市场数据获取
    try:
        market_data = await data_manager.get_realtime_market_data()
        if market_data:
            print(f"   获取实时市场数据成功:")
            print(f"     涨停股数: {market_data.get('limit_up_count', 0)}")
            print(f"     跌停股数: {market_data.get('limit_down_count', 0)}")
            print(f"     最高连板: {market_data.get('max_continuous_boards', 0)}")
        else:
            print("   获取实时市场数据失败")
    except Exception as e:
        print(f"   获取实时市场数据异常: {e}")
    
    print("✅ 数据管理器测试完成\n")


async def main():
    """主测试函数"""
    print("🚀 开始 AIQuant7 系统测试\n")
    
    # 运行各模块测试
    await test_sentiment_engine()
    await test_theme_engine()
    await test_leader_engine()
    await test_signal_engine()
    await test_position_manager()
    await test_data_manager()
    
    print("🎉 AIQuant7 系统测试完成！")
    print("\n📊 系统状态:")
    print("   ✅ 市场情绪引擎 - 正常")
    print("   ✅ 题材识别引擎 - 正常")
    print("   ✅ 龙头股识别引擎 - 正常")
    print("   ✅ 交易信号引擎 - 正常")
    print("   ✅ 仓位管理器 - 正常")
    print("   ⚠️  数据管理器 - 需要配置数据源")
    print("\n🔗 API服务地址:")
    print("   📖 API文档: http://localhost:8080/docs")
    print("   🏥 健康检查: http://localhost:8080/health")
    print("   🌐 API根路径: http://localhost:8080/api/v1/")


if __name__ == "__main__":
    asyncio.run(main())
