#!/bin/bash

# AIQuant7 系统状态检查脚本
# 用于检查前端和后端服务的运行状态

echo "📊 AIQuant7 系统状态检查"
echo "=========================="

# 检查服务状态的函数
check_service_status() {
    local pid_file=$1
    local service_name=$2
    local port=$3
    local health_url=$4
    
    echo ""
    echo "🔍 检查 $service_name..."
    
    # 检查PID文件
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            echo "   ✅ 进程运行中 (PID: $pid)"
            
            # 检查进程详情
            local process_info=$(ps -p $pid -o pid,ppid,etime,cmd --no-headers 2>/dev/null || echo "无法获取进程信息")
            echo "   📋 进程信息: $process_info"
        else
            echo "   ❌ PID文件存在但进程不存在"
            rm -f "$pid_file"
        fi
    else
        echo "   ⚠️  PID文件不存在"
        
        # 尝试通过进程名查找
        local process_pattern=""
        if [[ "$service_name" == *"后端"* ]]; then
            process_pattern="uvicorn.*backend.main:app"
        elif [[ "$service_name" == *"前端"* ]]; then
            process_pattern="react-scripts start"
        fi
        
        if [ -n "$process_pattern" ]; then
            local pids=$(pgrep -f "$process_pattern" 2>/dev/null || true)
            if [ -n "$pids" ]; then
                echo "   🔍 发现相关进程: $pids"
            else
                echo "   ❌ 未发现相关进程"
            fi
        fi
    fi
    
    # 检查端口
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        local port_pid=$(lsof -Pi :$port -sTCP:LISTEN -t)
        echo "   ✅ 端口 $port 已监听 (PID: $port_pid)"
    else
        echo "   ❌ 端口 $port 未监听"
    fi
    
    # 检查服务健康状态
    if [ -n "$health_url" ]; then
        echo "   🏥 检查服务健康状态..."
        local response=$(curl -s -w "%{http_code}" -o /dev/null "$health_url" 2>/dev/null || echo "000")
        if [ "$response" = "200" ]; then
            echo "   ✅ 服务健康检查通过"
            
            # 获取响应时间
            local response_time=$(curl -s -w "%{time_total}" -o /dev/null "$health_url" 2>/dev/null || echo "N/A")
            echo "   ⏱️  响应时间: ${response_time}s"
        else
            echo "   ❌ 服务健康检查失败 (HTTP: $response)"
        fi
    fi
}

# 检查系统资源
check_system_resources() {
    echo ""
    echo "💻 系统资源状态..."
    
    # 检查内存使用
    if command -v free &> /dev/null; then
        echo "   📊 内存使用:"
        free -h | grep -E "Mem|Swap" | sed 's/^/      /'
    elif command -v vm_stat &> /dev/null; then
        # macOS
        echo "   📊 内存使用 (macOS):"
        vm_stat | head -4 | sed 's/^/      /'
    fi
    
    # 检查磁盘使用
    echo "   💾 磁盘使用:"
    df -h . | sed 's/^/      /'
    
    # 检查CPU负载
    if command -v uptime &> /dev/null; then
        echo "   ⚡ 系统负载:"
        uptime | sed 's/^/      /'
    fi
}

# 检查日志文件
check_logs() {
    echo ""
    echo "📝 日志文件状态..."
    
    local log_files=("logs/backend.log" "logs/frontend.log")
    
    for log_file in "${log_files[@]}"; do
        if [ -f "$log_file" ]; then
            local size=$(du -h "$log_file" | cut -f1)
            local lines=$(wc -l < "$log_file")
            echo "   📄 $log_file: $size ($lines 行)"
            
            # 显示最近的错误
            local errors=$(grep -i "error\|exception\|failed" "$log_file" 2>/dev/null | tail -3 || true)
            if [ -n "$errors" ]; then
                echo "   ⚠️  最近错误:"
                echo "$errors" | sed 's/^/      /'
            fi
        else
            echo "   ❌ $log_file: 文件不存在"
        fi
    done
}

# 检查网络连接
check_network() {
    echo ""
    echo "🌐 网络连接检查..."
    
    # 检查外网连接
    if curl -s --max-time 5 http://www.baidu.com > /dev/null 2>&1; then
        echo "   ✅ 外网连接正常"
    else
        echo "   ❌ 外网连接异常"
    fi
    
    # 检查数据源连接
    local data_sources=(
        "http://push2.eastmoney.com"
        "http://quote.eastmoney.com"
    )
    
    for source in "${data_sources[@]}"; do
        if curl -s --max-time 5 "$source" > /dev/null 2>&1; then
            echo "   ✅ 数据源连接正常: $source"
        else
            echo "   ⚠️  数据源连接异常: $source"
        fi
    done
}

# 主检查流程
main() {
    # 检查后端服务
    check_service_status ".backend.pid" "后端服务" "8080" "http://localhost:8080/health"
    
    # 检查前端服务
    check_service_status ".frontend.pid" "前端服务" "3000" "http://localhost:3000"
    
    # 检查系统资源
    check_system_resources
    
    # 检查日志文件
    check_logs
    
    # 检查网络连接
    check_network
    
    echo ""
    echo "=========================="
    echo "📊 状态检查完成"
    echo ""
    echo "💡 常用命令:"
    echo "   🚀 启动系统: ./start.sh"
    echo "   🛑 停止系统: ./stop.sh"
    echo "   🧪 运行测试: python test_system_integration.py"
    echo "   📝 查看日志: tail -f logs/backend.log"
    echo "   🌐 访问前端: http://localhost:3000"
    echo "   📖 API文档:  http://localhost:8080/docs"
}

# 运行主函数
main
