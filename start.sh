#!/bin/bash

# AIQuant7 系统启动脚本
# 用于快速启动前端和后端服务

set -e

echo "🚀 启动 AIQuant7 系统..."

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ Python 未安装，请先安装 Python 3.8+"
    exit 1
fi

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 16+"
    exit 1
fi

# 检查依赖是否已安装
if [ ! -d "venv" ] && [ ! -f "requirements.txt" ]; then
    echo "❌ 请先运行 ./deploy.sh 安装依赖"
    exit 1
fi

# 激活虚拟环境（如果存在）
if [ -d "venv" ]; then
    echo "📦 激活Python虚拟环境..."
    source venv/bin/activate
fi

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用，$service 可能已在运行"
        echo "   如需重启，请先运行: ./stop.sh"
        return 1
    fi
    return 0
}

# 检查后端端口
if ! check_port 8080 "后端服务"; then
    echo "🔄 尝试停止现有服务..."
    pkill -f "uvicorn.*backend.main:app" || true
    sleep 2
fi

# 检查前端端口
if ! check_port 3000 "前端服务"; then
    echo "🔄 尝试停止现有服务..."
    pkill -f "react-scripts start" || true
    sleep 2
fi

# 创建日志目录
mkdir -p logs

# 启动后端服务
echo "🔧 启动后端服务..."
nohup python -m uvicorn backend.main:app --host 0.0.0.0 --port 8080 --reload > logs/backend.log 2>&1 &
BACKEND_PID=$!
echo "   后端服务 PID: $BACKEND_PID"

# 等待后端启动
echo "⏳ 等待后端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:8080/health > /dev/null 2>&1; then
        echo "✅ 后端服务启动成功"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 后端服务启动超时"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
    sleep 1
done

# 启动前端服务
echo "🎨 启动前端服务..."
cd frontend

# 检查Node.js依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 启动前端开发服务器
nohup npm start > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo "   前端服务 PID: $FRONTEND_PID"

cd ..

# 等待前端启动
echo "⏳ 等待前端服务启动..."
for i in {1..60}; do
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ 前端服务启动成功"
        break
    fi
    if [ $i -eq 60 ]; then
        echo "❌ 前端服务启动超时"
        kill $FRONTEND_PID 2>/dev/null || true
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
    sleep 1
done

# 保存PID到文件
echo $BACKEND_PID > .backend.pid
echo $FRONTEND_PID > .frontend.pid

echo ""
echo "🎉 AIQuant7 系统启动成功！"
echo ""
echo "📊 系统访问地址:"
echo "   🌐 前端界面: http://localhost:3000"
echo "   📖 API文档:  http://localhost:8080/docs"
echo "   🏥 健康检查: http://localhost:8080/health"
echo ""
echo "📝 日志文件:"
echo "   🔧 后端日志: logs/backend.log"
echo "   🎨 前端日志: logs/frontend.log"
echo ""
echo "🛑 停止服务: ./stop.sh"
echo "📊 查看状态: ./status.sh"
echo ""
echo "✨ 系统已准备就绪，开始您的量化交易之旅！"
