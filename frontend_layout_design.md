# 前端界面设计方案

## 1. 整体布局设计

### 1.1 主框架布局
```
┌─────────────────────────────────────────────────────────────────┐
│                        顶部导航栏                                 │
│  Logo | 市场情绪 | 热点题材 | 个股分析 | 策略回测 | 交易管理 | 设置   │
├─────────────────────────────────────────────────────────────────┤
│ 侧边栏    │                   主要内容区域                        │
│          │                                                    │
│ 📊 仪表板 │ ┌─────────────────────────────────────────────────┐ │
│ 📈 行情   │ │                                                │ │
│ 🔥 情绪   │ │              动态内容展示区                      │ │
│ 🎯 选股   │ │                                                │ │
│ ⚡ 信号   │ │                                                │ │
│ 💼 交易   │ │                                                │ │
│ ⚙️ 设置   │ │                                                │ │
│          │ └─────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        底部状态栏                                 │
│    连接状态 | 数据更新时间 | 市场状态 | 系统消息                   │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 关键页面设计

#### 1.2.1 市场情绪监控页面
```
┌─────────────────────────────────────────────────────────────────┐
│                    市场情绪监控中心                               │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │  情绪值     │ │  涨停家数   │ │  跌停家数   │ │  连板高度   │ │
│ │    88      │ │     45     │ │     12     │ │     7板    │ │
│ │  ↗ 高潮期   │ │   ↗ +8     │ │   ↘ -3     │ │   ↗ +1     │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                      情绪趋势图                                  │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                                                            │ │
│ │        情绪值变化曲线 (实时更新)                             │ │
│ │                                                            │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ ┌──────────────────────┐ ┌──────────────────────────────────┐   │
│ │    涨停分析          │ │         连板梯队                  │   │
│ │                     │ │                                  │   │
│ │ • 首板: 38只         │ │ 7板: 恒为科技                     │   │
│ │ • 二板: 15只         │ │ 6板: 航锦科技                     │   │
│ │ • 三板: 8只          │ │ 5板: xxx, xxx                    │   │
│ │ • 四板+: 6只         │ │ 4板: xxx, xxx, xxx               │   │
│ │                     │ │                                  │   │
│ └──────────────────────┘ └──────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

#### 1.2.2 热点题材页面
```
┌─────────────────────────────────────────────────────────────────┐
│                      热点题材分析                                │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                    题材热度排行榜                            │ │
│ │ 排名  板块      涨幅   成交额    龙头股      强度    状态     │ │
│ │  1   人工智能   +8.5%   120亿   恒为科技    ★★★★★  主升浪  │ │
│ │  2   军工      +6.2%    95亿   航锦科技    ★★★★☆  加速期  │ │
│ │  3   新能源    +4.8%   180亿   宁德时代    ★★★☆☆  震荡期  │ │
│ │  4   医药      +3.2%    85亿   药明康德    ★★☆☆☆  分化期  │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ ┌──────────────────────┐ ┌──────────────────────────────────┐   │
│ │    龙头股监控        │ │         题材生命周期              │   │
│ │                     │ │                                  │   │
│ │ 恒为科技 (603496)    │ │ ┌─────┐  ┌─────┐  ┌─────┐       │   │
│ │ 现价: 45.67         │ │ │启动期│─▶│主升浪│─▶│分化期│       │   │
│ │ 涨幅: +10.01%       │ │ └─────┘  └─────┘  └─────┘       │   │
│ │ 量比: 3.2           │ │    ↑      当前位置                │   │
│ │ 换手: 15.6%         │ │                                  │   │
│ │ 状态: 强势主升       │ │                                  │   │
│ │                     │ │                                  │   │
│ └──────────────────────┘ └──────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

#### 1.2.3 个股分析页面
```
┌─────────────────────────────────────────────────────────────────┐
│ 股票代码: 000001  │  股票名称: 平安银行  │  当前价格: 12.45 (+2.3%) │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                      K线图表区域                            │ │
│ │                                                            │ │
│ │  [日K] [60分] [30分] [15分] [5分] [1分]  [前复权] [不复权]   │ │
│ │                                                            │ │
│ │              交互式K线图 + 技术指标叠加                      │ │
│ │                                                            │ │
│ └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ ┌────────────────┐ ┌────────────────┐ ┌───────────────────┐   │
│ │   基本信息     │ │   技术指标     │ │     交易信号       │   │
│ │               │ │               │ │                   │   │
│ │ 总市值: 2430亿 │ │ MACD: 金叉     │ │ 🟢 买入信号       │   │
│ │ 流通市值: 2100亿│ │ RSI: 65       │ │ 强度: ★★★★☆     │   │
│ │ PE: 6.8       │ │ 布林: 上轨附近  │ │ 时间: 14:32       │   │
│ │ PB: 0.9       │ │ 量比: 2.3     │ │ 依据: 技术突破     │   │
│ │               │ │               │ │                   │   │
│ └────────────────┘ └────────────────┘ └───────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## 2. 组件设计

### 2.1 核心组件列表

#### 2.1.1 数据展示组件
- **MarketSentimentCard**: 市场情绪卡片
- **StockCard**: 个股卡片
- **TrendChart**: 趋势图表
- **CandlestickChart**: K线图表
- **VolumeChart**: 成交量图表
- **HeatMap**: 热力图

#### 2.1.2 交互组件
- **StockSelector**: 股票选择器
- **TimeRangePicker**: 时间范围选择器
- **StrategySelector**: 策略选择器
- **AlertCenter**: 消息中心
- **TradingPanel**: 交易面板

#### 2.1.3 业务组件
- **SentimentMonitor**: 情绪监控器
- **HotTopicTracker**: 热点追踪器
- **SignalGenerator**: 信号生成器
- **RiskController**: 风险控制器
- **BacktestRunner**: 回测运行器

### 2.2 组件技术实现

#### 2.2.1 React组件示例
```tsx
// MarketSentimentCard 组件
interface SentimentData {
  value: number;
  status: 'ice' | 'warming' | 'climax' | 'retreat' | 'chaos';
  upLimit: number;
  downLimit: number;
  consecutive: number;
}

const MarketSentimentCard: React.FC<{ data: SentimentData }> = ({ data }) => {
  const getStatusColor = (status: string) => {
    const colors = {
      ice: '#1890ff',
      warming: '#52c41a', 
      climax: '#ff4d4f',
      retreat: '#fa8c16',
      chaos: '#722ed1'
    };
    return colors[status] || '#666';
  };

  return (
    <Card className="sentiment-card">
      <Statistic
        title="市场情绪值"
        value={data.value}
        precision={0}
        valueStyle={{ color: getStatusColor(data.status) }}
        prefix={<TrendingUpOutlined />}
        suffix={<Badge status="processing" text={data.status} />}
      />
      <Row gutter={16} style={{ marginTop: 16 }}>
        <Col span={8}>
          <Statistic title="涨停" value={data.upLimit} suffix="只" />
        </Col>
        <Col span={8}>
          <Statistic title="跌停" value={data.downLimit} suffix="只" />
        </Col>
        <Col span={8}>
          <Statistic title="连板" value={data.consecutive} suffix="板" />
        </Col>
      </Row>
    </Card>
  );
};
```

#### 2.2.2 ECharts图表组件
```tsx
// TrendChart 组件
const TrendChart: React.FC<{ data: ChartData[] }> = ({ data }) => {
  const option = {
    title: {
      text: '情绪趋势图',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.time)
    },
    yAxis: {
      type: 'value',
      name: '情绪值'
    },
    series: [
      {
        name: '情绪值',
        type: 'line',
        data: data.map(item => item.value),
        smooth: true,
        lineStyle: {
          width: 3
        },
        markLine: {
          data: [
            { yAxis: 20, name: '冰点线' },
            { yAxis: 50, name: '平衡线' },
            { yAxis: 80, name: '高潮线' }
          ]
        }
      }
    ]
  };

  return <ReactECharts option={option} style={{ height: '400px' }} />;
};
```

## 3. 响应式设计

### 3.1 断点设计
- **超小屏** (<576px): 手机竖屏
- **小屏** (576px-768px): 手机横屏/小平板
- **中屏** (768px-992px): 平板
- **大屏** (992px-1200px): 桌面
- **超大屏** (>1200px): 大桌面

### 3.2 适配策略
- **手机端**: 单列布局，底部导航，滑动切换
- **平板端**: 双列布局，侧边导航，手势操作
- **桌面端**: 多列布局，顶部+侧边导航，鼠标操作

## 4. 交互设计

### 4.1 实时数据更新
- WebSocket连接实时推送
- 自动刷新关键数据
- 加载状态显示
- 错误重连机制

### 4.2 用户操作流程
- 快捷键支持
- 手势操作（移动端）
- 拖拽排序
- 右键菜单

### 4.3 消息通知
- 信号提醒
- 风险警告
- 系统通知
- 交易确认 