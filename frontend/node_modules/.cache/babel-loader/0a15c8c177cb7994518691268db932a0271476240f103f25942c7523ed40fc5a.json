{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/TradingSignals.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Table, Tag, Button, Spin, Select, Alert } from 'antd';\nimport { SignalOutlined, BellOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport moment from 'moment';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst TradingSignals = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [signals, setSignals] = useState([]);\n  const [filteredSignals, setFilteredSignals] = useState([]);\n  const [filterType, setFilterType] = useState('all');\n  const [filterStatus, setFilterStatus] = useState('all');\n  useEffect(() => {\n    loadSignalData();\n    // 设置定时刷新\n    const interval = setInterval(loadSignalData, 60000); // 1分钟刷新一次\n    return () => clearInterval(interval);\n  }, []);\n  useEffect(() => {\n    filterSignals();\n  }, [signals, filterType, filterStatus]);\n  const loadSignalData = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getLatestSignals(200);\n      console.log('Signal data received:', response);\n      const signalData = (response === null || response === void 0 ? void 0 : response.data) || response;\n      setSignals(Array.isArray(signalData) ? signalData : []);\n    } catch (error) {\n      console.error('加载交易信号失败:', error);\n      // 使用模拟数据\n      const mockSignals = [{\n        signal_id: '1',\n        symbol: '300750',\n        name: '宁德时代',\n        signal_type: 'buy',\n        signal_strength: 'strong',\n        price: 245.80,\n        target_price: 280.00,\n        stop_loss: 220.00,\n        confidence: 85.5,\n        reason: '突破关键阻力位，成交量放大',\n        created_time: moment().subtract(5, 'minutes').toISOString(),\n        status: 'active',\n        theme: '新能源汽车'\n      }, {\n        signal_id: '2',\n        symbol: '000725',\n        name: '京东方A',\n        signal_type: 'sell',\n        signal_strength: 'medium',\n        price: 4.25,\n        target_price: 3.80,\n        stop_loss: 4.50,\n        confidence: 72.3,\n        reason: '技术指标背离，获利了结',\n        created_time: moment().subtract(15, 'minutes').toISOString(),\n        status: 'active',\n        theme: '人工智能'\n      }, {\n        signal_id: '3',\n        symbol: '002594',\n        name: '比亚迪',\n        signal_type: 'buy',\n        signal_strength: 'weak',\n        price: 280.50,\n        target_price: 300.00,\n        stop_loss: 265.00,\n        confidence: 68.8,\n        reason: '题材热度回升，资金流入',\n        created_time: moment().subtract(30, 'minutes').toISOString(),\n        status: 'executed',\n        theme: '新能源汽车'\n      }];\n      setSignals(mockSignals);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filterSignals = () => {\n    if (!Array.isArray(signals)) {\n      setFilteredSignals([]);\n      return;\n    }\n    let filtered = signals;\n\n    // 按信号类型筛选\n    if (filterType !== 'all') {\n      filtered = filtered.filter(signal => signal.signal_type === filterType);\n    }\n\n    // 按状态筛选\n    if (filterStatus !== 'all') {\n      filtered = filtered.filter(signal => signal.status === filterStatus);\n    }\n    setFilteredSignals(filtered);\n  };\n  const getSignalTypeColor = type => {\n    switch (type) {\n      case 'buy':\n        return 'red';\n      case 'sell':\n        return 'green';\n      case 'hold':\n        return 'blue';\n      default:\n        return 'default';\n    }\n  };\n  const getSignalTypeName = type => {\n    switch (type) {\n      case 'buy':\n        return '买入';\n      case 'sell':\n        return '卖出';\n      case 'hold':\n        return '持有';\n      default:\n        return '未知';\n    }\n  };\n  const getStrengthColor = strength => {\n    switch (strength) {\n      case 'strong':\n        return '#ff4d4f';\n      case 'medium':\n        return '#faad14';\n      case 'weak':\n        return '#52c41a';\n      default:\n        return '#d9d9d9';\n    }\n  };\n  const getStrengthName = strength => {\n    switch (strength) {\n      case 'strong':\n        return '强';\n      case 'medium':\n        return '中';\n      case 'weak':\n        return '弱';\n      default:\n        return '未知';\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'active':\n        return 'processing';\n      case 'executed':\n        return 'success';\n      case 'expired':\n        return 'default';\n      case 'cancelled':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusName = status => {\n    switch (status) {\n      case 'active':\n        return '活跃';\n      case 'executed':\n        return '已执行';\n      case 'expired':\n        return '已过期';\n      case 'cancelled':\n        return '已取消';\n      default:\n        return '未知';\n    }\n  };\n  const columns = [{\n    title: '时间',\n    dataIndex: 'created_time',\n    key: 'created_time',\n    width: 120,\n    render: time => moment(time).format('HH:mm:ss'),\n    sorter: (a, b) => moment(a.created_time).unix() - moment(b.created_time).unix()\n  }, {\n    title: '股票信息',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold'\n        },\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.symbol\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '信号类型',\n    dataIndex: 'signal_type',\n    key: 'signal_type',\n    width: 80,\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getSignalTypeColor(type),\n      children: getSignalTypeName(type)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '强度',\n    dataIndex: 'signal_strength',\n    key: 'signal_strength',\n    width: 60,\n    render: strength => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStrengthColor(strength),\n      children: getStrengthName(strength)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '当前价',\n    dataIndex: 'price',\n    key: 'price',\n    width: 80,\n    render: price => `¥${price.toFixed(2)}`\n  }, {\n    title: '目标价',\n    dataIndex: 'target_price',\n    key: 'target_price',\n    width: 80,\n    render: price => `¥${price.toFixed(2)}`\n  }, {\n    title: '止损价',\n    dataIndex: 'stop_loss',\n    key: 'stop_loss',\n    width: 80,\n    render: price => `¥${price.toFixed(2)}`\n  }, {\n    title: '置信度',\n    dataIndex: 'confidence',\n    key: 'confidence',\n    width: 80,\n    render: confidence => `${confidence.toFixed(1)}%`,\n    sorter: (a, b) => a.confidence - b.confidence\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 80,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusName(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '信号原因',\n    dataIndex: 'reason',\n    key: 'reason',\n    ellipsis: true\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 100,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: record.status === 'active' && /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 52\n        }, this),\n        children: \"\\u6267\\u884C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 统计数据\n  const activeSignals = signals.filter(s => s.status === 'active');\n  const buySignals = activeSignals.filter(s => s.signal_type === 'buy');\n  const sellSignals = activeSignals.filter(s => s.signal_type === 'sell');\n  const strongSignals = activeSignals.filter(s => s.signal_strength === 'strong');\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u4EA4\\u6613\\u4FE1\\u53F7...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            style: {\n              color: '#1890ff'\n            },\n            children: activeSignals.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-label\",\n            children: \"\\u6D3B\\u8DC3\\u4FE1\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            style: {\n              color: '#ff4d4f'\n            },\n            children: buySignals.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-label\",\n            children: \"\\u4E70\\u5165\\u4FE1\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            style: {\n              color: '#52c41a'\n            },\n            children: sellSignals.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-label\",\n            children: \"\\u5356\\u51FA\\u4FE1\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            style: {\n              color: '#faad14'\n            },\n            children: strongSignals.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-label\",\n            children: \"\\u5F3A\\u4FE1\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), strongSignals.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      message: `发现 ${strongSignals.length} 个强信号`,\n      description: \"\\u5EFA\\u8BAE\\u91CD\\u70B9\\u5173\\u6CE8\\u5F3A\\u4FE1\\u53F7\\uFF0C\\u53CA\\u65F6\\u6267\\u884C\\u4EA4\\u6613\\u51B3\\u7B56\",\n      type: \"warning\",\n      showIcon: true,\n      icon: /*#__PURE__*/_jsxDEV(BellOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 17\n      }, this),\n      style: {\n        marginBottom: 24\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 8,\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          value: filterType,\n          onChange: setFilterType,\n          style: {\n            width: '100%'\n          },\n          placeholder: \"\\u9009\\u62E9\\u4FE1\\u53F7\\u7C7B\\u578B\",\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: \"all\",\n            children: \"\\u5168\\u90E8\\u7C7B\\u578B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"buy\",\n            children: \"\\u4E70\\u5165\\u4FE1\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"sell\",\n            children: \"\\u5356\\u51FA\\u4FE1\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"hold\",\n            children: \"\\u6301\\u6709\\u4FE1\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 8,\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          value: filterStatus,\n          onChange: setFilterStatus,\n          style: {\n            width: '100%'\n          },\n          placeholder: \"\\u9009\\u62E9\\u72B6\\u6001\",\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: \"all\",\n            children: \"\\u5168\\u90E8\\u72B6\\u6001\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"active\",\n            children: \"\\u6D3B\\u8DC3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"executed\",\n            children: \"\\u5DF2\\u6267\\u884C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"expired\",\n            children: \"\\u5DF2\\u8FC7\\u671F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"cancelled\",\n            children: \"\\u5DF2\\u53D6\\u6D88\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u4EA4\\u6613\\u4FE1\\u53F7\",\n      className: \"dashboard-card\",\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: filteredSignals,\n        rowKey: \"signal_id\",\n        pagination: {\n          pageSize: 20,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个信号`\n        },\n        scroll: {\n          x: 1200\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 287,\n    columnNumber: 5\n  }, this);\n};\n_s(TradingSignals, \"c3OihQ/F1whkr8V44mYGggylDLQ=\");\n_c = TradingSignals;\nexport default TradingSignals;\nvar _c;\n$RefreshReg$(_c, \"TradingSignals\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Table", "Tag", "<PERSON><PERSON>", "Spin", "Select", "<PERSON><PERSON>", "SignalOutlined", "BellOutlined", "CheckCircleOutlined", "moment", "apiService", "jsxDEV", "_jsxDEV", "Option", "TradingSignals", "_s", "loading", "setLoading", "signals", "setSignals", "filteredSignals", "setFilteredSignals", "filterType", "setFilterType", "filterStatus", "setFilterStatus", "loadSignalData", "interval", "setInterval", "clearInterval", "filterSignals", "response", "getLatestSignals", "console", "log", "signalData", "data", "Array", "isArray", "error", "mockSignals", "signal_id", "symbol", "name", "signal_type", "signal_strength", "price", "target_price", "stop_loss", "confidence", "reason", "created_time", "subtract", "toISOString", "status", "theme", "filtered", "filter", "signal", "getSignalTypeColor", "type", "getSignalTypeName", "getStrengthColor", "strength", "getStrengthName", "getStatusColor", "getStatusName", "columns", "title", "dataIndex", "key", "width", "render", "time", "format", "sorter", "a", "b", "unix", "text", "record", "children", "style", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "color", "toFixed", "ellipsis", "_", "size", "icon", "activeSignals", "s", "buySignals", "sellSignals", "strongSignals", "textAlign", "padding", "marginTop", "gutter", "marginBottom", "xs", "sm", "className", "length", "message", "description", "showIcon", "value", "onChange", "placeholder", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "scroll", "x", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/TradingSignals.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Table, Tag, Button, Spin, Select, Alert } from 'antd';\nimport { SignalOutlined, BellOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport moment from 'moment';\nimport apiService from '../services/api';\n\nconst { Option } = Select;\n\nconst TradingSignals = () => {\n  const [loading, setLoading] = useState(true);\n  const [signals, setSignals] = useState([]);\n  const [filteredSignals, setFilteredSignals] = useState([]);\n  const [filterType, setFilterType] = useState('all');\n  const [filterStatus, setFilterStatus] = useState('all');\n\n  useEffect(() => {\n    loadSignalData();\n    // 设置定时刷新\n    const interval = setInterval(loadSignalData, 60000); // 1分钟刷新一次\n    return () => clearInterval(interval);\n  }, []);\n\n  useEffect(() => {\n    filterSignals();\n  }, [signals, filterType, filterStatus]);\n\n  const loadSignalData = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getLatestSignals(200);\n      console.log('Signal data received:', response);\n      const signalData = response?.data || response;\n      setSignals(Array.isArray(signalData) ? signalData : []);\n    } catch (error) {\n      console.error('加载交易信号失败:', error);\n      // 使用模拟数据\n      const mockSignals = [\n        {\n          signal_id: '1',\n          symbol: '300750',\n          name: '宁德时代',\n          signal_type: 'buy',\n          signal_strength: 'strong',\n          price: 245.80,\n          target_price: 280.00,\n          stop_loss: 220.00,\n          confidence: 85.5,\n          reason: '突破关键阻力位，成交量放大',\n          created_time: moment().subtract(5, 'minutes').toISOString(),\n          status: 'active',\n          theme: '新能源汽车'\n        },\n        {\n          signal_id: '2',\n          symbol: '000725',\n          name: '京东方A',\n          signal_type: 'sell',\n          signal_strength: 'medium',\n          price: 4.25,\n          target_price: 3.80,\n          stop_loss: 4.50,\n          confidence: 72.3,\n          reason: '技术指标背离，获利了结',\n          created_time: moment().subtract(15, 'minutes').toISOString(),\n          status: 'active',\n          theme: '人工智能'\n        },\n        {\n          signal_id: '3',\n          symbol: '002594',\n          name: '比亚迪',\n          signal_type: 'buy',\n          signal_strength: 'weak',\n          price: 280.50,\n          target_price: 300.00,\n          stop_loss: 265.00,\n          confidence: 68.8,\n          reason: '题材热度回升，资金流入',\n          created_time: moment().subtract(30, 'minutes').toISOString(),\n          status: 'executed',\n          theme: '新能源汽车'\n        }\n      ];\n      setSignals(mockSignals);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filterSignals = () => {\n    if (!Array.isArray(signals)) {\n      setFilteredSignals([]);\n      return;\n    }\n\n    let filtered = signals;\n\n    // 按信号类型筛选\n    if (filterType !== 'all') {\n      filtered = filtered.filter(signal => signal.signal_type === filterType);\n    }\n\n    // 按状态筛选\n    if (filterStatus !== 'all') {\n      filtered = filtered.filter(signal => signal.status === filterStatus);\n    }\n\n    setFilteredSignals(filtered);\n  };\n\n  const getSignalTypeColor = (type) => {\n    switch (type) {\n      case 'buy': return 'red';\n      case 'sell': return 'green';\n      case 'hold': return 'blue';\n      default: return 'default';\n    }\n  };\n\n  const getSignalTypeName = (type) => {\n    switch (type) {\n      case 'buy': return '买入';\n      case 'sell': return '卖出';\n      case 'hold': return '持有';\n      default: return '未知';\n    }\n  };\n\n  const getStrengthColor = (strength) => {\n    switch (strength) {\n      case 'strong': return '#ff4d4f';\n      case 'medium': return '#faad14';\n      case 'weak': return '#52c41a';\n      default: return '#d9d9d9';\n    }\n  };\n\n  const getStrengthName = (strength) => {\n    switch (strength) {\n      case 'strong': return '强';\n      case 'medium': return '中';\n      case 'weak': return '弱';\n      default: return '未知';\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'active': return 'processing';\n      case 'executed': return 'success';\n      case 'expired': return 'default';\n      case 'cancelled': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getStatusName = (status) => {\n    switch (status) {\n      case 'active': return '活跃';\n      case 'executed': return '已执行';\n      case 'expired': return '已过期';\n      case 'cancelled': return '已取消';\n      default: return '未知';\n    }\n  };\n\n  const columns = [\n    {\n      title: '时间',\n      dataIndex: 'created_time',\n      key: 'created_time',\n      width: 120,\n      render: (time) => moment(time).format('HH:mm:ss'),\n      sorter: (a, b) => moment(a.created_time).unix() - moment(b.created_time).unix()\n    },\n    {\n      title: '股票信息',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <div>\n          <div style={{ fontWeight: 'bold' }}>{text}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>{record.symbol}</div>\n        </div>\n      )\n    },\n    {\n      title: '信号类型',\n      dataIndex: 'signal_type',\n      key: 'signal_type',\n      width: 80,\n      render: (type) => (\n        <Tag color={getSignalTypeColor(type)}>\n          {getSignalTypeName(type)}\n        </Tag>\n      )\n    },\n    {\n      title: '强度',\n      dataIndex: 'signal_strength',\n      key: 'signal_strength',\n      width: 60,\n      render: (strength) => (\n        <Tag color={getStrengthColor(strength)}>\n          {getStrengthName(strength)}\n        </Tag>\n      )\n    },\n    {\n      title: '当前价',\n      dataIndex: 'price',\n      key: 'price',\n      width: 80,\n      render: (price) => `¥${price.toFixed(2)}`\n    },\n    {\n      title: '目标价',\n      dataIndex: 'target_price',\n      key: 'target_price',\n      width: 80,\n      render: (price) => `¥${price.toFixed(2)}`\n    },\n    {\n      title: '止损价',\n      dataIndex: 'stop_loss',\n      key: 'stop_loss',\n      width: 80,\n      render: (price) => `¥${price.toFixed(2)}`\n    },\n    {\n      title: '置信度',\n      dataIndex: 'confidence',\n      key: 'confidence',\n      width: 80,\n      render: (confidence) => `${confidence.toFixed(1)}%`,\n      sorter: (a, b) => a.confidence - b.confidence\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 80,\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusName(status)}\n        </Tag>\n      )\n    },\n    {\n      title: '信号原因',\n      dataIndex: 'reason',\n      key: 'reason',\n      ellipsis: true\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 100,\n      render: (_, record) => (\n        <div>\n          {record.status === 'active' && (\n            <Button type=\"link\" size=\"small\" icon={<CheckCircleOutlined />}>\n              执行\n            </Button>\n          )}\n        </div>\n      )\n    }\n  ];\n\n  // 统计数据\n  const activeSignals = signals.filter(s => s.status === 'active');\n  const buySignals = activeSignals.filter(s => s.signal_type === 'buy');\n  const sellSignals = activeSignals.filter(s => s.signal_type === 'sell');\n  const strongSignals = activeSignals.filter(s => s.signal_strength === 'strong');\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>正在加载交易信号...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* 信号统计 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={12} sm={6}>\n          <Card className=\"metric-card\">\n            <div className=\"metric-value\" style={{ color: '#1890ff' }}>\n              {activeSignals.length}\n            </div>\n            <div className=\"metric-label\">活跃信号</div>\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card className=\"metric-card\">\n            <div className=\"metric-value\" style={{ color: '#ff4d4f' }}>\n              {buySignals.length}\n            </div>\n            <div className=\"metric-label\">买入信号</div>\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card className=\"metric-card\">\n            <div className=\"metric-value\" style={{ color: '#52c41a' }}>\n              {sellSignals.length}\n            </div>\n            <div className=\"metric-label\">卖出信号</div>\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card className=\"metric-card\">\n            <div className=\"metric-value\" style={{ color: '#faad14' }}>\n              {strongSignals.length}\n            </div>\n            <div className=\"metric-label\">强信号</div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 重要提醒 */}\n      {strongSignals.length > 0 && (\n        <Alert\n          message={`发现 ${strongSignals.length} 个强信号`}\n          description=\"建议重点关注强信号，及时执行交易决策\"\n          type=\"warning\"\n          showIcon\n          icon={<BellOutlined />}\n          style={{ marginBottom: 24 }}\n        />\n      )}\n\n      {/* 筛选控件 */}\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col xs={24} sm={8}>\n          <Select\n            value={filterType}\n            onChange={setFilterType}\n            style={{ width: '100%' }}\n            placeholder=\"选择信号类型\"\n          >\n            <Option value=\"all\">全部类型</Option>\n            <Option value=\"buy\">买入信号</Option>\n            <Option value=\"sell\">卖出信号</Option>\n            <Option value=\"hold\">持有信号</Option>\n          </Select>\n        </Col>\n        <Col xs={24} sm={8}>\n          <Select\n            value={filterStatus}\n            onChange={setFilterStatus}\n            style={{ width: '100%' }}\n            placeholder=\"选择状态\"\n          >\n            <Option value=\"all\">全部状态</Option>\n            <Option value=\"active\">活跃</Option>\n            <Option value=\"executed\">已执行</Option>\n            <Option value=\"expired\">已过期</Option>\n            <Option value=\"cancelled\">已取消</Option>\n          </Select>\n        </Col>\n      </Row>\n\n      {/* 交易信号表格 */}\n      <Card title=\"交易信号\" className=\"dashboard-card\">\n        <Table\n          columns={columns}\n          dataSource={filteredSignals}\n          rowKey=\"signal_id\"\n          pagination={{\n            pageSize: 20,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个信号`\n          }}\n          scroll={{ x: 1200 }}\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default TradingSignals;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAC9E,SAASC,cAAc,EAAEC,YAAY,EAAEC,mBAAmB,QAAQ,mBAAmB;AACrF,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAM;EAAEC;AAAO,CAAC,GAAGT,MAAM;AAEzB,MAAMU,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd8B,cAAc,CAAC,CAAC;IAChB;IACA,MAAMC,QAAQ,GAAGC,WAAW,CAACF,cAAc,EAAE,KAAK,CAAC,CAAC,CAAC;IACrD,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN/B,SAAS,CAAC,MAAM;IACdkC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACZ,OAAO,EAAEI,UAAU,EAAEE,YAAY,CAAC,CAAC;EAEvC,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMc,QAAQ,GAAG,MAAMrB,UAAU,CAACsB,gBAAgB,CAAC,GAAG,CAAC;MACvDC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,QAAQ,CAAC;MAC9C,MAAMI,UAAU,GAAG,CAAAJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEK,IAAI,KAAIL,QAAQ;MAC7CZ,UAAU,CAACkB,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,GAAGA,UAAU,GAAG,EAAE,CAAC;IACzD,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;MACA,MAAMC,WAAW,GAAG,CAClB;QACEC,SAAS,EAAE,GAAG;QACdC,MAAM,EAAE,QAAQ;QAChBC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,KAAK;QAClBC,eAAe,EAAE,QAAQ;QACzBC,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,MAAM;QACjBC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,eAAe;QACvBC,YAAY,EAAE1C,MAAM,CAAC,CAAC,CAAC2C,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,CAACC,WAAW,CAAC,CAAC;QAC3DC,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE;MACT,CAAC,EACD;QACEd,SAAS,EAAE,GAAG;QACdC,MAAM,EAAE,QAAQ;QAChBC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,MAAM;QACnBC,eAAe,EAAE,QAAQ;QACzBC,KAAK,EAAE,IAAI;QACXC,YAAY,EAAE,IAAI;QAClBC,SAAS,EAAE,IAAI;QACfC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,aAAa;QACrBC,YAAY,EAAE1C,MAAM,CAAC,CAAC,CAAC2C,QAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,CAACC,WAAW,CAAC,CAAC;QAC5DC,MAAM,EAAE,QAAQ;QAChBC,KAAK,EAAE;MACT,CAAC,EACD;QACEd,SAAS,EAAE,GAAG;QACdC,MAAM,EAAE,QAAQ;QAChBC,IAAI,EAAE,KAAK;QACXC,WAAW,EAAE,KAAK;QAClBC,eAAe,EAAE,MAAM;QACvBC,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE,MAAM;QACjBC,UAAU,EAAE,IAAI;QAChBC,MAAM,EAAE,aAAa;QACrBC,YAAY,EAAE1C,MAAM,CAAC,CAAC,CAAC2C,QAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,CAACC,WAAW,CAAC,CAAC;QAC5DC,MAAM,EAAE,UAAU;QAClBC,KAAK,EAAE;MACT,CAAC,CACF;MACDpC,UAAU,CAACqB,WAAW,CAAC;IACzB,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACO,KAAK,CAACC,OAAO,CAACpB,OAAO,CAAC,EAAE;MAC3BG,kBAAkB,CAAC,EAAE,CAAC;MACtB;IACF;IAEA,IAAImC,QAAQ,GAAGtC,OAAO;;IAEtB;IACA,IAAII,UAAU,KAAK,KAAK,EAAE;MACxBkC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACd,WAAW,KAAKtB,UAAU,CAAC;IACzE;;IAEA;IACA,IAAIE,YAAY,KAAK,KAAK,EAAE;MAC1BgC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACJ,MAAM,KAAK9B,YAAY,CAAC;IACtE;IAEAH,kBAAkB,CAACmC,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMG,kBAAkB,GAAIC,IAAI,IAAK;IACnC,QAAQA,IAAI;MACV,KAAK,KAAK;QAAE,OAAO,KAAK;MACxB,KAAK,MAAM;QAAE,OAAO,OAAO;MAC3B,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAID,IAAI,IAAK;IAClC,QAAQA,IAAI;MACV,KAAK,KAAK;QAAE,OAAO,IAAI;MACvB,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAME,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,eAAe,GAAID,QAAQ,IAAK;IACpC,QAAQA,QAAQ;MACd,KAAK,QAAQ;QAAE,OAAO,GAAG;MACzB,KAAK,QAAQ;QAAE,OAAO,GAAG;MACzB,KAAK,MAAM;QAAE,OAAO,GAAG;MACvB;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAME,cAAc,GAAIX,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,YAAY;MAClC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMY,aAAa,GAAIZ,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,UAAU;QAAE,OAAO,KAAK;MAC7B,KAAK,SAAS;QAAE,OAAO,KAAK;MAC5B,KAAK,WAAW;QAAE,OAAO,KAAK;MAC9B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMa,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAI,IAAKhE,MAAM,CAACgE,IAAI,CAAC,CAACC,MAAM,CAAC,UAAU,CAAC;IACjDC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKpE,MAAM,CAACmE,CAAC,CAACzB,YAAY,CAAC,CAAC2B,IAAI,CAAC,CAAC,GAAGrE,MAAM,CAACoE,CAAC,CAAC1B,YAAY,CAAC,CAAC2B,IAAI,CAAC;EAChF,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXE,MAAM,EAAEA,CAACO,IAAI,EAAEC,MAAM,kBACnBpE,OAAA;MAAAqE,QAAA,gBACErE,OAAA;QAAKsE,KAAK,EAAE;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAEF;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChD3E,OAAA;QAAKsE,KAAK,EAAE;UAAEM,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAED,MAAM,CAACtC;MAAM;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE;EAET,CAAC,EACD;IACEnB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGZ,IAAI,iBACXhD,OAAA,CAACX,GAAG;MAACwF,KAAK,EAAE9B,kBAAkB,CAACC,IAAI,CAAE;MAAAqB,QAAA,EAClCpB,iBAAiB,CAACD,IAAI;IAAC;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB;EAET,CAAC,EACD;IACEnB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGT,QAAQ,iBACfnD,OAAA,CAACX,GAAG;MAACwF,KAAK,EAAE3B,gBAAgB,CAACC,QAAQ,CAAE;MAAAkB,QAAA,EACpCjB,eAAe,CAACD,QAAQ;IAAC;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB;EAET,CAAC,EACD;IACEnB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAG1B,KAAK,IAAK,IAAIA,KAAK,CAAC4C,OAAO,CAAC,CAAC,CAAC;EACzC,CAAC,EACD;IACEtB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAG1B,KAAK,IAAK,IAAIA,KAAK,CAAC4C,OAAO,CAAC,CAAC,CAAC;EACzC,CAAC,EACD;IACEtB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAG1B,KAAK,IAAK,IAAIA,KAAK,CAAC4C,OAAO,CAAC,CAAC,CAAC;EACzC,CAAC,EACD;IACEtB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGvB,UAAU,IAAK,GAAGA,UAAU,CAACyC,OAAO,CAAC,CAAC,CAAC,GAAG;IACnDf,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3B,UAAU,GAAG4B,CAAC,CAAC5B;EACrC,CAAC,EACD;IACEmB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGlB,MAAM,iBACb1C,OAAA,CAACX,GAAG;MAACwF,KAAK,EAAExB,cAAc,CAACX,MAAM,CAAE;MAAA2B,QAAA,EAChCf,aAAa,CAACZ,MAAM;IAAC;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEnB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbqB,QAAQ,EAAE;EACZ,CAAC,EACD;IACEvB,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACoB,CAAC,EAAEZ,MAAM,kBAChBpE,OAAA;MAAAqE,QAAA,EACGD,MAAM,CAAC1B,MAAM,KAAK,QAAQ,iBACzB1C,OAAA,CAACV,MAAM;QAAC0D,IAAI,EAAC,MAAM;QAACiC,IAAI,EAAC,OAAO;QAACC,IAAI,eAAElF,OAAA,CAACJ,mBAAmB;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAN,QAAA,EAAC;MAEhE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,CACF;;EAED;EACA,MAAMQ,aAAa,GAAG7E,OAAO,CAACuC,MAAM,CAACuC,CAAC,IAAIA,CAAC,CAAC1C,MAAM,KAAK,QAAQ,CAAC;EAChE,MAAM2C,UAAU,GAAGF,aAAa,CAACtC,MAAM,CAACuC,CAAC,IAAIA,CAAC,CAACpD,WAAW,KAAK,KAAK,CAAC;EACrE,MAAMsD,WAAW,GAAGH,aAAa,CAACtC,MAAM,CAACuC,CAAC,IAAIA,CAAC,CAACpD,WAAW,KAAK,MAAM,CAAC;EACvE,MAAMuD,aAAa,GAAGJ,aAAa,CAACtC,MAAM,CAACuC,CAAC,IAAIA,CAAC,CAACnD,eAAe,KAAK,QAAQ,CAAC;EAE/E,IAAI7B,OAAO,EAAE;IACX,oBACEJ,OAAA;MAAKsE,KAAK,EAAE;QAAEkB,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAApB,QAAA,gBACnDrE,OAAA,CAACT,IAAI;QAAC0F,IAAI,EAAC;MAAO;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB3E,OAAA;QAAKsE,KAAK,EAAE;UAAEoB,SAAS,EAAE;QAAG,CAAE;QAAArB,QAAA,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEV;EAEA,oBACE3E,OAAA;IAAAqE,QAAA,gBAEErE,OAAA,CAACf,GAAG;MAAC0G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACrB,KAAK,EAAE;QAAEsB,YAAY,EAAE;MAAG,CAAE;MAAAvB,QAAA,gBACjDrE,OAAA,CAACd,GAAG;QAAC2G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACjBrE,OAAA,CAACb,IAAI;UAAC4G,SAAS,EAAC,aAAa;UAAA1B,QAAA,gBAC3BrE,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAACzB,KAAK,EAAE;cAAEO,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,EACvDc,aAAa,CAACa;UAAM;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACN3E,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAAA1B,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3E,OAAA,CAACd,GAAG;QAAC2G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACjBrE,OAAA,CAACb,IAAI;UAAC4G,SAAS,EAAC,aAAa;UAAA1B,QAAA,gBAC3BrE,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAACzB,KAAK,EAAE;cAAEO,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,EACvDgB,UAAU,CAACW;UAAM;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACN3E,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAAA1B,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3E,OAAA,CAACd,GAAG;QAAC2G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACjBrE,OAAA,CAACb,IAAI;UAAC4G,SAAS,EAAC,aAAa;UAAA1B,QAAA,gBAC3BrE,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAACzB,KAAK,EAAE;cAAEO,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,EACvDiB,WAAW,CAACU;UAAM;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACN3E,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAAA1B,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3E,OAAA,CAACd,GAAG;QAAC2G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACjBrE,OAAA,CAACb,IAAI;UAAC4G,SAAS,EAAC,aAAa;UAAA1B,QAAA,gBAC3BrE,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAACzB,KAAK,EAAE;cAAEO,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,EACvDkB,aAAa,CAACS;UAAM;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACN3E,OAAA;YAAK+F,SAAS,EAAC,cAAc;YAAA1B,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLY,aAAa,CAACS,MAAM,GAAG,CAAC,iBACvBhG,OAAA,CAACP,KAAK;MACJwG,OAAO,EAAE,MAAMV,aAAa,CAACS,MAAM,OAAQ;MAC3CE,WAAW,EAAC,8GAAoB;MAChClD,IAAI,EAAC,SAAS;MACdmD,QAAQ;MACRjB,IAAI,eAAElF,OAAA,CAACL,YAAY;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACvBL,KAAK,EAAE;QAAEsB,YAAY,EAAE;MAAG;IAAE;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CACF,eAGD3E,OAAA,CAACf,GAAG;MAAC0G,MAAM,EAAE,EAAG;MAACrB,KAAK,EAAE;QAAEsB,YAAY,EAAE;MAAG,CAAE;MAAAvB,QAAA,gBAC3CrE,OAAA,CAACd,GAAG;QAAC2G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACjBrE,OAAA,CAACR,MAAM;UACL4G,KAAK,EAAE1F,UAAW;UAClB2F,QAAQ,EAAE1F,aAAc;UACxB2D,KAAK,EAAE;YAAEX,KAAK,EAAE;UAAO,CAAE;UACzB2C,WAAW,EAAC,sCAAQ;UAAAjC,QAAA,gBAEpBrE,OAAA,CAACC,MAAM;YAACmG,KAAK,EAAC,KAAK;YAAA/B,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjC3E,OAAA,CAACC,MAAM;YAACmG,KAAK,EAAC,KAAK;YAAA/B,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjC3E,OAAA,CAACC,MAAM;YAACmG,KAAK,EAAC,MAAM;YAAA/B,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClC3E,OAAA,CAACC,MAAM;YAACmG,KAAK,EAAC,MAAM;YAAA/B,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN3E,OAAA,CAACd,GAAG;QAAC2G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACjBrE,OAAA,CAACR,MAAM;UACL4G,KAAK,EAAExF,YAAa;UACpByF,QAAQ,EAAExF,eAAgB;UAC1ByD,KAAK,EAAE;YAAEX,KAAK,EAAE;UAAO,CAAE;UACzB2C,WAAW,EAAC,0BAAM;UAAAjC,QAAA,gBAElBrE,OAAA,CAACC,MAAM;YAACmG,KAAK,EAAC,KAAK;YAAA/B,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjC3E,OAAA,CAACC,MAAM;YAACmG,KAAK,EAAC,QAAQ;YAAA/B,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClC3E,OAAA,CAACC,MAAM;YAACmG,KAAK,EAAC,UAAU;YAAA/B,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrC3E,OAAA,CAACC,MAAM;YAACmG,KAAK,EAAC,SAAS;YAAA/B,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpC3E,OAAA,CAACC,MAAM;YAACmG,KAAK,EAAC,WAAW;YAAA/B,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3E,OAAA,CAACb,IAAI;MAACqE,KAAK,EAAC,0BAAM;MAACuC,SAAS,EAAC,gBAAgB;MAAA1B,QAAA,eAC3CrE,OAAA,CAACZ,KAAK;QACJmE,OAAO,EAAEA,OAAQ;QACjBgD,UAAU,EAAE/F,eAAgB;QAC5BgG,MAAM,EAAC,WAAW;QAClBC,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC,CAAE;QACFC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK;MAAE;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACxE,EAAA,CAvXID,cAAc;AAAA+G,EAAA,GAAd/G,cAAc;AAyXpB,eAAeA,cAAc;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}