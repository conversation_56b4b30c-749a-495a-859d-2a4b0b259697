{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/LeaderStocks.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Table, Tag, Button, Spin, Input, Select, Modal } from 'antd';\nimport { CrownOutlined, SearchOutlined, EyeOutlined, StarOutlined } from '@ant-design/icons';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Search\n} = Input;\nconst {\n  Option\n} = Select;\nconst LeaderStocks = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [leaders, setLeaders] = useState([]);\n  const [filteredLeaders, setFilteredLeaders] = useState([]);\n  const [searchText, setSearchText] = useState('');\n  const [filterTheme, setFilterTheme] = useState('all');\n  const [selectedStock, setSelectedStock] = useState(null);\n  const [detailVisible, setDetailVisible] = useState(false);\n  useEffect(() => {\n    loadLeaderData();\n  }, []);\n  useEffect(() => {\n    filterLeaders();\n  }, [leaders, searchText, filterTheme]);\n  const loadLeaderData = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getLeaderRanking(100);\n      console.log('Leader data received:', response);\n      const leaderData = (response === null || response === void 0 ? void 0 : response.data) || response;\n      setLeaders(Array.isArray(leaderData) ? leaderData : []);\n    } catch (error) {\n      console.error('加载龙头股数据失败:', error);\n      // 使用模拟数据\n      const mockLeaders = [{\n        symbol: '300750',\n        name: '宁德时代',\n        theme: '新能源汽车',\n        leader_score: 98.5,\n        market_cap: 1200000000000,\n        price: 245.80,\n        change_pct: 8.5,\n        volume_ratio: 2.3,\n        turnover_rate: 1.2,\n        leader_type: 'absolute',\n        continuous_days: 5,\n        theme_position: 1,\n        strength_level: 'very_strong'\n      }, {\n        symbol: '002594',\n        name: '比亚迪',\n        theme: '新能源汽车',\n        leader_score: 95.2,\n        market_cap: 800000000000,\n        price: 280.50,\n        change_pct: 6.8,\n        volume_ratio: 1.8,\n        turnover_rate: 0.9,\n        leader_type: 'relative',\n        continuous_days: 3,\n        theme_position: 2,\n        strength_level: 'strong'\n      }, {\n        symbol: '000725',\n        name: '京东方A',\n        theme: '人工智能',\n        leader_score: 88.7,\n        market_cap: 150000000000,\n        price: 4.25,\n        change_pct: 10.0,\n        volume_ratio: 3.5,\n        turnover_rate: 2.8,\n        leader_type: 'absolute',\n        continuous_days: 2,\n        theme_position: 1,\n        strength_level: 'very_strong'\n      }];\n      setLeaders(mockLeaders);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filterLeaders = () => {\n    if (!Array.isArray(leaders)) {\n      setFilteredLeaders([]);\n      return;\n    }\n    let filtered = leaders;\n\n    // 按题材筛选\n    if (filterTheme !== 'all') {\n      filtered = filtered.filter(leader => leader.theme === filterTheme);\n    }\n\n    // 按搜索文本筛选\n    if (searchText) {\n      filtered = filtered.filter(leader => leader.name && leader.name.toLowerCase().includes(searchText.toLowerCase()) || leader.symbol && leader.symbol.includes(searchText) || leader.theme && leader.theme.toLowerCase().includes(searchText.toLowerCase()));\n    }\n    setFilteredLeaders(filtered);\n  };\n  const getLeaderTypeColor = type => {\n    switch (type) {\n      case 'absolute':\n        return 'red';\n      case 'relative':\n        return 'orange';\n      case 'potential':\n        return 'blue';\n      default:\n        return 'default';\n    }\n  };\n  const getLeaderTypeName = type => {\n    switch (type) {\n      case 'absolute':\n        return '绝对龙头';\n      case 'relative':\n        return '相对龙头';\n      case 'potential':\n        return '潜在龙头';\n      default:\n        return '未知';\n    }\n  };\n  const getStrengthColor = level => {\n    switch (level) {\n      case 'very_strong':\n        return '#ff4d4f';\n      case 'strong':\n        return '#fa8c16';\n      case 'medium':\n        return '#faad14';\n      case 'weak':\n        return '#52c41a';\n      default:\n        return '#d9d9d9';\n    }\n  };\n  const getStrengthName = level => {\n    switch (level) {\n      case 'very_strong':\n        return '极强';\n      case 'strong':\n        return '强势';\n      case 'medium':\n        return '中等';\n      case 'weak':\n        return '偏弱';\n      default:\n        return '未知';\n    }\n  };\n  const showStockDetail = async stock => {\n    setSelectedStock(stock);\n    setDetailVisible(true);\n  };\n  const columns = [{\n    title: '排名',\n    dataIndex: 'index',\n    key: 'index',\n    width: 60,\n    render: (_, __, index) => {\n      const rank = index + 1;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: rank <= 3 ? /*#__PURE__*/_jsxDEV(CrownOutlined, {\n          style: {\n            color: rank === 1 ? '#ffd700' : rank === 2 ? '#c0c0c0' : '#cd7f32',\n            fontSize: '16px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 15\n        }, this) : rank\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '股票信息',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold'\n        },\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.symbol\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '所属题材',\n    dataIndex: 'theme',\n    key: 'theme',\n    render: (theme, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: theme\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: [\"\\u9898\\u6750\\u7B2C\", record.theme_position, \"\\u4F4D\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '龙头评分',\n    dataIndex: 'leader_score',\n    key: 'leader_score',\n    width: 100,\n    render: score => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold',\n          fontSize: '16px'\n        },\n        children: score.toFixed(1)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.leader_score - b.leader_score\n  }, {\n    title: '龙头类型',\n    dataIndex: 'leader_type',\n    key: 'leader_type',\n    width: 100,\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getLeaderTypeColor(type),\n      children: getLeaderTypeName(type)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '股价',\n    dataIndex: 'price',\n    key: 'price',\n    width: 80,\n    render: (price, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold'\n        },\n        children: [\"\\xA5\", price.toFixed(2)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: record.change_pct >= 0 ? 'price-up' : 'price-down',\n        children: [record.change_pct >= 0 ? '+' : '', record.change_pct.toFixed(2), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '强度',\n    dataIndex: 'strength_level',\n    key: 'strength_level',\n    width: 80,\n    render: level => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStrengthColor(level),\n      children: getStrengthName(level)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '连续天数',\n    dataIndex: 'continuous_days',\n    key: 'continuous_days',\n    width: 80,\n    render: days => `${days}天`,\n    sorter: (a, b) => a.continuous_days - b.continuous_days\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 120,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 19\n        }, this),\n        onClick: () => showStockDetail(record),\n        children: \"\\u8BE6\\u60C5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(StarOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 19\n        }, this),\n        children: \"\\u5173\\u6CE8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 获取唯一题材列表\n  const themes = Array.isArray(leaders) ? [...new Set(leaders.map(leader => leader.theme))] : [];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u9F99\\u5934\\u80A1\\u6570\\u636E...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Search, {\n          placeholder: \"\\u641C\\u7D22\\u80A1\\u7968\\u540D\\u79F0\\u3001\\u4EE3\\u7801\\u6216\\u9898\\u6750\",\n          allowClear: true,\n          onSearch: setSearchText,\n          onChange: e => setSearchText(e.target.value),\n          prefix: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 21\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          value: filterTheme,\n          onChange: setFilterTheme,\n          style: {\n            width: '100%'\n          },\n          placeholder: \"\\u9009\\u62E9\\u9898\\u6750\",\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: \"all\",\n            children: \"\\u5168\\u90E8\\u9898\\u6750\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this), themes.map(theme => /*#__PURE__*/_jsxDEV(Option, {\n            value: theme,\n            children: theme\n          }, theme, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            style: {\n              color: '#1890ff'\n            },\n            children: leaders.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-label\",\n            children: \"\\u9F99\\u5934\\u80A1\\u603B\\u6570\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            style: {\n              color: '#ff4d4f'\n            },\n            children: leaders.filter(l => l.leader_type === 'absolute').length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-label\",\n            children: \"\\u7EDD\\u5BF9\\u9F99\\u5934\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            style: {\n              color: '#52c41a'\n            },\n            children: leaders.filter(l => l.change_pct > 5).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-label\",\n            children: \"\\u5F3A\\u52BF\\u9F99\\u5934\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            style: {\n              color: '#faad14'\n            },\n            children: themes.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-label\",\n            children: \"\\u6D89\\u53CA\\u9898\\u6750\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u9F99\\u5934\\u80A1\\u6392\\u884C\\u699C\",\n      className: \"dashboard-card\",\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: filteredLeaders,\n        rowKey: \"symbol\",\n        pagination: {\n          pageSize: 20,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 只龙头股`\n        },\n        scroll: {\n          x: 1000\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `${selectedStock === null || selectedStock === void 0 ? void 0 : selectedStock.name} (${selectedStock === null || selectedStock === void 0 ? void 0 : selectedStock.symbol}) 详情`,\n      open: detailVisible,\n      onCancel: () => setDetailVisible(false),\n      footer: null,\n      width: 800,\n      children: selectedStock && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u57FA\\u672C\\u4FE1\\u606F\",\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u80A1\\u7968\\u540D\\u79F0: \", selectedStock.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u80A1\\u7968\\u4EE3\\u7801: \", selectedStock.symbol]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u6240\\u5C5E\\u9898\\u6750: \", selectedStock.theme]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u5E02\\u503C: \", (selectedStock.market_cap / 100000000).toFixed(0), \"\\u4EBF\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u9F99\\u5934\\u6307\\u6807\",\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u9F99\\u5934\\u8BC4\\u5206: \", selectedStock.leader_score.toFixed(1)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u9F99\\u5934\\u7C7B\\u578B: \", getLeaderTypeName(selectedStock.leader_type)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u5F3A\\u5EA6\\u7B49\\u7EA7: \", getStrengthName(selectedStock.strength_level)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u8FDE\\u7EED\\u5929\\u6570: \", selectedStock.continuous_days, \"\\u5929\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          style: {\n            marginTop: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u4EA4\\u6613\\u6570\\u636E\",\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u5F53\\u524D\\u4EF7\\u683C: \\xA5\", selectedStock.price.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u6DA8\\u8DCC\\u5E45: \", selectedStock.change_pct >= 0 ? '+' : '', selectedStock.change_pct.toFixed(2), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u91CF\\u6BD4: \", selectedStock.volume_ratio.toFixed(1)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u6362\\u624B\\u7387: \", selectedStock.turnover_rate.toFixed(2), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u9898\\u6750\\u5730\\u4F4D\",\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u9898\\u6750\\u6392\\u540D: \\u7B2C\", selectedStock.theme_position, \"\\u4F4D\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u9898\\u6750: \", selectedStock.theme]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"\\u9F99\\u5934\\u5730\\u4F4D: \", getLeaderTypeName(selectedStock.leader_type)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 300,\n    columnNumber: 5\n  }, this);\n};\n_s(LeaderStocks, \"EiN4hca1tu7iirecEyoav2FOOms=\");\n_c = LeaderStocks;\nexport default LeaderStocks;\nvar _c;\n$RefreshReg$(_c, \"LeaderStocks\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Table", "Tag", "<PERSON><PERSON>", "Spin", "Input", "Select", "Modal", "CrownOutlined", "SearchOutlined", "EyeOutlined", "StarOutlined", "apiService", "jsxDEV", "_jsxDEV", "Search", "Option", "LeaderStocks", "_s", "loading", "setLoading", "leaders", "setLeaders", "filteredLeaders", "setFilteredLeaders", "searchText", "setSearchText", "filterTheme", "setFilterTheme", "selectedStock", "setSelectedStock", "detailVisible", "setDetailVisible", "loadLeaderData", "filterLeaders", "response", "getLeaderRanking", "console", "log", "leader<PERSON><PERSON>", "data", "Array", "isArray", "error", "mockLeaders", "symbol", "name", "theme", "leader_score", "market_cap", "price", "change_pct", "volume_ratio", "turnover_rate", "leader_type", "continuous_days", "theme_position", "strength_level", "filtered", "filter", "leader", "toLowerCase", "includes", "getLeaderTypeColor", "type", "getLeaderTypeName", "getStrengthColor", "level", "getStrengthName", "showStockDetail", "stock", "columns", "title", "dataIndex", "key", "width", "render", "_", "__", "index", "rank", "style", "textAlign", "children", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "text", "record", "fontWeight", "score", "toFixed", "sorter", "a", "b", "className", "days", "size", "icon", "onClick", "themes", "Set", "map", "padding", "marginTop", "gutter", "marginBottom", "xs", "sm", "md", "placeholder", "allowClear", "onSearch", "onChange", "e", "target", "value", "prefix", "length", "l", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "scroll", "x", "open", "onCancel", "footer", "span", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/LeaderStocks.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Table, Tag, Button, Spin, Input, Select, Modal } from 'antd';\nimport { CrownOutlined, SearchOutlined, EyeOutlined, StarOutlined } from '@ant-design/icons';\nimport apiService from '../services/api';\n\nconst { Search } = Input;\nconst { Option } = Select;\n\nconst LeaderStocks = () => {\n  const [loading, setLoading] = useState(true);\n  const [leaders, setLeaders] = useState([]);\n  const [filteredLeaders, setFilteredLeaders] = useState([]);\n  const [searchText, setSearchText] = useState('');\n  const [filterTheme, setFilterTheme] = useState('all');\n  const [selectedStock, setSelectedStock] = useState(null);\n  const [detailVisible, setDetailVisible] = useState(false);\n\n  useEffect(() => {\n    loadLeaderData();\n  }, []);\n\n  useEffect(() => {\n    filterLeaders();\n  }, [leaders, searchText, filterTheme]);\n\n  const loadLeaderData = async () => {\n    try {\n      setLoading(true);\n      const response = await apiService.getLeaderRanking(100);\n      console.log('Leader data received:', response);\n      const leaderData = response?.data || response;\n      setLeaders(Array.isArray(leaderData) ? leaderData : []);\n    } catch (error) {\n      console.error('加载龙头股数据失败:', error);\n      // 使用模拟数据\n      const mockLeaders = [\n        {\n          symbol: '300750',\n          name: '宁德时代',\n          theme: '新能源汽车',\n          leader_score: 98.5,\n          market_cap: 1200000000000,\n          price: 245.80,\n          change_pct: 8.5,\n          volume_ratio: 2.3,\n          turnover_rate: 1.2,\n          leader_type: 'absolute',\n          continuous_days: 5,\n          theme_position: 1,\n          strength_level: 'very_strong'\n        },\n        {\n          symbol: '002594',\n          name: '比亚迪',\n          theme: '新能源汽车',\n          leader_score: 95.2,\n          market_cap: 800000000000,\n          price: 280.50,\n          change_pct: 6.8,\n          volume_ratio: 1.8,\n          turnover_rate: 0.9,\n          leader_type: 'relative',\n          continuous_days: 3,\n          theme_position: 2,\n          strength_level: 'strong'\n        },\n        {\n          symbol: '000725',\n          name: '京东方A',\n          theme: '人工智能',\n          leader_score: 88.7,\n          market_cap: 150000000000,\n          price: 4.25,\n          change_pct: 10.0,\n          volume_ratio: 3.5,\n          turnover_rate: 2.8,\n          leader_type: 'absolute',\n          continuous_days: 2,\n          theme_position: 1,\n          strength_level: 'very_strong'\n        }\n      ];\n      setLeaders(mockLeaders);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filterLeaders = () => {\n    if (!Array.isArray(leaders)) {\n      setFilteredLeaders([]);\n      return;\n    }\n\n    let filtered = leaders;\n\n    // 按题材筛选\n    if (filterTheme !== 'all') {\n      filtered = filtered.filter(leader => leader.theme === filterTheme);\n    }\n\n    // 按搜索文本筛选\n    if (searchText) {\n      filtered = filtered.filter(leader =>\n        (leader.name && leader.name.toLowerCase().includes(searchText.toLowerCase())) ||\n        (leader.symbol && leader.symbol.includes(searchText)) ||\n        (leader.theme && leader.theme.toLowerCase().includes(searchText.toLowerCase()))\n      );\n    }\n\n    setFilteredLeaders(filtered);\n  };\n\n  const getLeaderTypeColor = (type) => {\n    switch (type) {\n      case 'absolute': return 'red';\n      case 'relative': return 'orange';\n      case 'potential': return 'blue';\n      default: return 'default';\n    }\n  };\n\n  const getLeaderTypeName = (type) => {\n    switch (type) {\n      case 'absolute': return '绝对龙头';\n      case 'relative': return '相对龙头';\n      case 'potential': return '潜在龙头';\n      default: return '未知';\n    }\n  };\n\n  const getStrengthColor = (level) => {\n    switch (level) {\n      case 'very_strong': return '#ff4d4f';\n      case 'strong': return '#fa8c16';\n      case 'medium': return '#faad14';\n      case 'weak': return '#52c41a';\n      default: return '#d9d9d9';\n    }\n  };\n\n  const getStrengthName = (level) => {\n    switch (level) {\n      case 'very_strong': return '极强';\n      case 'strong': return '强势';\n      case 'medium': return '中等';\n      case 'weak': return '偏弱';\n      default: return '未知';\n    }\n  };\n\n  const showStockDetail = async (stock) => {\n    setSelectedStock(stock);\n    setDetailVisible(true);\n  };\n\n  const columns = [\n    {\n      title: '排名',\n      dataIndex: 'index',\n      key: 'index',\n      width: 60,\n      render: (_, __, index) => {\n        const rank = index + 1;\n        return (\n          <div style={{ textAlign: 'center' }}>\n            {rank <= 3 ? (\n              <CrownOutlined style={{ \n                color: rank === 1 ? '#ffd700' : rank === 2 ? '#c0c0c0' : '#cd7f32',\n                fontSize: '16px'\n              }} />\n            ) : (\n              rank\n            )}\n          </div>\n        );\n      }\n    },\n    {\n      title: '股票信息',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <div>\n          <div style={{ fontWeight: 'bold' }}>{text}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>{record.symbol}</div>\n        </div>\n      )\n    },\n    {\n      title: '所属题材',\n      dataIndex: 'theme',\n      key: 'theme',\n      render: (theme, record) => (\n        <div>\n          <Tag color=\"blue\">{theme}</Tag>\n          <div style={{ fontSize: '12px', color: '#666' }}>\n            题材第{record.theme_position}位\n          </div>\n        </div>\n      )\n    },\n    {\n      title: '龙头评分',\n      dataIndex: 'leader_score',\n      key: 'leader_score',\n      width: 100,\n      render: (score) => (\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ fontWeight: 'bold', fontSize: '16px' }}>\n            {score.toFixed(1)}\n          </div>\n        </div>\n      ),\n      sorter: (a, b) => a.leader_score - b.leader_score\n    },\n    {\n      title: '龙头类型',\n      dataIndex: 'leader_type',\n      key: 'leader_type',\n      width: 100,\n      render: (type) => (\n        <Tag color={getLeaderTypeColor(type)}>\n          {getLeaderTypeName(type)}\n        </Tag>\n      )\n    },\n    {\n      title: '股价',\n      dataIndex: 'price',\n      key: 'price',\n      width: 80,\n      render: (price, record) => (\n        <div>\n          <div style={{ fontWeight: 'bold' }}>¥{price.toFixed(2)}</div>\n          <div className={record.change_pct >= 0 ? 'price-up' : 'price-down'}>\n            {record.change_pct >= 0 ? '+' : ''}{record.change_pct.toFixed(2)}%\n          </div>\n        </div>\n      )\n    },\n    {\n      title: '强度',\n      dataIndex: 'strength_level',\n      key: 'strength_level',\n      width: 80,\n      render: (level) => (\n        <Tag color={getStrengthColor(level)}>\n          {getStrengthName(level)}\n        </Tag>\n      )\n    },\n    {\n      title: '连续天数',\n      dataIndex: 'continuous_days',\n      key: 'continuous_days',\n      width: 80,\n      render: (days) => `${days}天`,\n      sorter: (a, b) => a.continuous_days - b.continuous_days\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 120,\n      render: (_, record) => (\n        <div>\n          <Button \n            type=\"link\" \n            size=\"small\" \n            icon={<EyeOutlined />}\n            onClick={() => showStockDetail(record)}\n          >\n            详情\n          </Button>\n          <Button \n            type=\"link\" \n            size=\"small\" \n            icon={<StarOutlined />}\n          >\n            关注\n          </Button>\n        </div>\n      )\n    }\n  ];\n\n  // 获取唯一题材列表\n  const themes = Array.isArray(leaders) ? [...new Set(leaders.map(leader => leader.theme))] : [];\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>正在加载龙头股数据...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* 筛选控件 */}\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col xs={24} sm={12} md={8}>\n          <Search\n            placeholder=\"搜索股票名称、代码或题材\"\n            allowClear\n            onSearch={setSearchText}\n            onChange={(e) => setSearchText(e.target.value)}\n            prefix={<SearchOutlined />}\n          />\n        </Col>\n        <Col xs={24} sm={12} md={8}>\n          <Select\n            value={filterTheme}\n            onChange={setFilterTheme}\n            style={{ width: '100%' }}\n            placeholder=\"选择题材\"\n          >\n            <Option value=\"all\">全部题材</Option>\n            {themes.map(theme => (\n              <Option key={theme} value={theme}>{theme}</Option>\n            ))}\n          </Select>\n        </Col>\n      </Row>\n\n      {/* 龙头股统计 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={12} sm={6}>\n          <Card className=\"metric-card\">\n            <div className=\"metric-value\" style={{ color: '#1890ff' }}>\n              {leaders.length}\n            </div>\n            <div className=\"metric-label\">龙头股总数</div>\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card className=\"metric-card\">\n            <div className=\"metric-value\" style={{ color: '#ff4d4f' }}>\n              {leaders.filter(l => l.leader_type === 'absolute').length}\n            </div>\n            <div className=\"metric-label\">绝对龙头</div>\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card className=\"metric-card\">\n            <div className=\"metric-value\" style={{ color: '#52c41a' }}>\n              {leaders.filter(l => l.change_pct > 5).length}\n            </div>\n            <div className=\"metric-label\">强势龙头</div>\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card className=\"metric-card\">\n            <div className=\"metric-value\" style={{ color: '#faad14' }}>\n              {themes.length}\n            </div>\n            <div className=\"metric-label\">涉及题材</div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 龙头股排行榜 */}\n      <Card title=\"龙头股排行榜\" className=\"dashboard-card\">\n        <Table\n          columns={columns}\n          dataSource={filteredLeaders}\n          rowKey=\"symbol\"\n          pagination={{\n            pageSize: 20,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 只龙头股`\n          }}\n          scroll={{ x: 1000 }}\n        />\n      </Card>\n\n      {/* 股票详情弹窗 */}\n      <Modal\n        title={`${selectedStock?.name} (${selectedStock?.symbol}) 详情`}\n        open={detailVisible}\n        onCancel={() => setDetailVisible(false)}\n        footer={null}\n        width={800}\n      >\n        {selectedStock && (\n          <div>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Card title=\"基本信息\" size=\"small\">\n                  <p>股票名称: {selectedStock.name}</p>\n                  <p>股票代码: {selectedStock.symbol}</p>\n                  <p>所属题材: {selectedStock.theme}</p>\n                  <p>市值: {(selectedStock.market_cap / 100000000).toFixed(0)}亿</p>\n                </Card>\n              </Col>\n              <Col span={12}>\n                <Card title=\"龙头指标\" size=\"small\">\n                  <p>龙头评分: {selectedStock.leader_score.toFixed(1)}</p>\n                  <p>龙头类型: {getLeaderTypeName(selectedStock.leader_type)}</p>\n                  <p>强度等级: {getStrengthName(selectedStock.strength_level)}</p>\n                  <p>连续天数: {selectedStock.continuous_days}天</p>\n                </Card>\n              </Col>\n            </Row>\n            <Row gutter={16} style={{ marginTop: 16 }}>\n              <Col span={12}>\n                <Card title=\"交易数据\" size=\"small\">\n                  <p>当前价格: ¥{selectedStock.price.toFixed(2)}</p>\n                  <p>涨跌幅: {selectedStock.change_pct >= 0 ? '+' : ''}{selectedStock.change_pct.toFixed(2)}%</p>\n                  <p>量比: {selectedStock.volume_ratio.toFixed(1)}</p>\n                  <p>换手率: {selectedStock.turnover_rate.toFixed(2)}%</p>\n                </Card>\n              </Col>\n              <Col span={12}>\n                <Card title=\"题材地位\" size=\"small\">\n                  <p>题材排名: 第{selectedStock.theme_position}位</p>\n                  <p>题材: {selectedStock.theme}</p>\n                  <p>龙头地位: {getLeaderTypeName(selectedStock.leader_type)}</p>\n                </Card>\n              </Col>\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default LeaderStocks;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AACrF,SAASC,aAAa,EAAEC,cAAc,EAAEC,WAAW,EAAEC,YAAY,QAAQ,mBAAmB;AAC5F,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAM;EAAEC;AAAO,CAAC,GAAGV,KAAK;AACxB,MAAM;EAAEW;AAAO,CAAC,GAAGV,MAAM;AAEzB,MAAMW,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAEzDC,SAAS,CAAC,MAAM;IACdoC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAENpC,SAAS,CAAC,MAAM;IACdqC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACb,OAAO,EAAEI,UAAU,EAAEE,WAAW,CAAC,CAAC;EAEtC,MAAMM,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMe,QAAQ,GAAG,MAAMvB,UAAU,CAACwB,gBAAgB,CAAC,GAAG,CAAC;MACvDC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,QAAQ,CAAC;MAC9C,MAAMI,UAAU,GAAG,CAAAJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEK,IAAI,KAAIL,QAAQ;MAC7Cb,UAAU,CAACmB,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,GAAGA,UAAU,GAAG,EAAE,CAAC;IACzD,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC;MACA,MAAMC,WAAW,GAAG,CAClB;QACEC,MAAM,EAAE,QAAQ;QAChBC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,OAAO;QACdC,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,aAAa;QACzBC,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,GAAG;QACfC,YAAY,EAAE,GAAG;QACjBC,aAAa,EAAE,GAAG;QAClBC,WAAW,EAAE,UAAU;QACvBC,eAAe,EAAE,CAAC;QAClBC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE;MAClB,CAAC,EACD;QACEZ,MAAM,EAAE,QAAQ;QAChBC,IAAI,EAAE,KAAK;QACXC,KAAK,EAAE,OAAO;QACdC,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,YAAY;QACxBC,KAAK,EAAE,MAAM;QACbC,UAAU,EAAE,GAAG;QACfC,YAAY,EAAE,GAAG;QACjBC,aAAa,EAAE,GAAG;QAClBC,WAAW,EAAE,UAAU;QACvBC,eAAe,EAAE,CAAC;QAClBC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE;MAClB,CAAC,EACD;QACEZ,MAAM,EAAE,QAAQ;QAChBC,IAAI,EAAE,MAAM;QACZC,KAAK,EAAE,MAAM;QACbC,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,YAAY;QACxBC,KAAK,EAAE,IAAI;QACXC,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,GAAG;QACjBC,aAAa,EAAE,GAAG;QAClBC,WAAW,EAAE,UAAU;QACvBC,eAAe,EAAE,CAAC;QAClBC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE;MAClB,CAAC,CACF;MACDnC,UAAU,CAACsB,WAAW,CAAC;IACzB,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACO,KAAK,CAACC,OAAO,CAACrB,OAAO,CAAC,EAAE;MAC3BG,kBAAkB,CAAC,EAAE,CAAC;MACtB;IACF;IAEA,IAAIkC,QAAQ,GAAGrC,OAAO;;IAEtB;IACA,IAAIM,WAAW,KAAK,KAAK,EAAE;MACzB+B,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACb,KAAK,KAAKpB,WAAW,CAAC;IACpE;;IAEA;IACA,IAAIF,UAAU,EAAE;MACdiC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,MAAM,IAC9BA,MAAM,CAACd,IAAI,IAAIc,MAAM,CAACd,IAAI,CAACe,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrC,UAAU,CAACoC,WAAW,CAAC,CAAC,CAAC,IAC3ED,MAAM,CAACf,MAAM,IAAIe,MAAM,CAACf,MAAM,CAACiB,QAAQ,CAACrC,UAAU,CAAE,IACpDmC,MAAM,CAACb,KAAK,IAAIa,MAAM,CAACb,KAAK,CAACc,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrC,UAAU,CAACoC,WAAW,CAAC,CAAC,CAC/E,CAAC;IACH;IAEArC,kBAAkB,CAACkC,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMK,kBAAkB,GAAIC,IAAI,IAAK;IACnC,QAAQA,IAAI;MACV,KAAK,UAAU;QAAE,OAAO,KAAK;MAC7B,KAAK,UAAU;QAAE,OAAO,QAAQ;MAChC,KAAK,WAAW;QAAE,OAAO,MAAM;MAC/B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAID,IAAI,IAAK;IAClC,QAAQA,IAAI;MACV,KAAK,UAAU;QAAE,OAAO,MAAM;MAC9B,KAAK,UAAU;QAAE,OAAO,MAAM;MAC9B,KAAK,WAAW;QAAE,OAAO,MAAM;MAC/B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAME,gBAAgB,GAAIC,KAAK,IAAK;IAClC,QAAQA,KAAK;MACX,KAAK,aAAa;QAAE,OAAO,SAAS;MACpC,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,eAAe,GAAID,KAAK,IAAK;IACjC,QAAQA,KAAK;MACX,KAAK,aAAa;QAAE,OAAO,IAAI;MAC/B,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAME,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvCxC,gBAAgB,CAACwC,KAAK,CAAC;IACvBtC,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMuC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAEA,CAACC,CAAC,EAAEC,EAAE,EAAEC,KAAK,KAAK;MACxB,MAAMC,IAAI,GAAGD,KAAK,GAAG,CAAC;MACtB,oBACEjE,OAAA;QAAKmE,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAC,QAAA,EACjCH,IAAI,IAAI,CAAC,gBACRlE,OAAA,CAACN,aAAa;UAACyE,KAAK,EAAE;YACpBG,KAAK,EAAEJ,IAAI,KAAK,CAAC,GAAG,SAAS,GAAGA,IAAI,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YAClEK,QAAQ,EAAE;UACZ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAELT;MACD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEV;EACF,CAAC,EACD;IACEjB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXE,MAAM,EAAEA,CAACc,IAAI,EAAEC,MAAM,kBACnB7E,OAAA;MAAAqE,QAAA,gBACErE,OAAA;QAAKmE,KAAK,EAAE;UAAEW,UAAU,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAEO;MAAI;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChD3E,OAAA;QAAKmE,KAAK,EAAE;UAAEI,QAAQ,EAAE,MAAM;UAAED,KAAK,EAAE;QAAO,CAAE;QAAAD,QAAA,EAAEQ,MAAM,CAAC9C;MAAM;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE;EAET,CAAC,EACD;IACEjB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZE,MAAM,EAAEA,CAAC7B,KAAK,EAAE4C,MAAM,kBACpB7E,OAAA;MAAAqE,QAAA,gBACErE,OAAA,CAACZ,GAAG;QAACkF,KAAK,EAAC,MAAM;QAAAD,QAAA,EAAEpC;MAAK;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/B3E,OAAA;QAAKmE,KAAK,EAAE;UAAEI,QAAQ,EAAE,MAAM;UAAED,KAAK,EAAE;QAAO,CAAE;QAAAD,QAAA,GAAC,oBAC5C,EAACQ,MAAM,CAACnC,cAAc,EAAC,QAC5B;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEjB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGiB,KAAK,iBACZ/E,OAAA;MAAKmE,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,eAClCrE,OAAA;QAAKmE,KAAK,EAAE;UAAEW,UAAU,EAAE,MAAM;UAAEP,QAAQ,EAAE;QAAO,CAAE;QAAAF,QAAA,EAClDU,KAAK,CAACC,OAAO,CAAC,CAAC;MAAC;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IACDM,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChD,YAAY,GAAGiD,CAAC,CAACjD;EACvC,CAAC,EACD;IACEwB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGZ,IAAI,iBACXlD,OAAA,CAACZ,GAAG;MAACkF,KAAK,EAAErB,kBAAkB,CAACC,IAAI,CAAE;MAAAmB,QAAA,EAClClB,iBAAiB,CAACD,IAAI;IAAC;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB;EAET,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAEA,CAAC1B,KAAK,EAAEyC,MAAM,kBACpB7E,OAAA;MAAAqE,QAAA,gBACErE,OAAA;QAAKmE,KAAK,EAAE;UAAEW,UAAU,EAAE;QAAO,CAAE;QAAAT,QAAA,GAAC,MAAC,EAACjC,KAAK,CAAC4C,OAAO,CAAC,CAAC,CAAC;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7D3E,OAAA;QAAKoF,SAAS,EAAEP,MAAM,CAACxC,UAAU,IAAI,CAAC,GAAG,UAAU,GAAG,YAAa;QAAAgC,QAAA,GAChEQ,MAAM,CAACxC,UAAU,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEwC,MAAM,CAACxC,UAAU,CAAC2C,OAAO,CAAC,CAAC,CAAC,EAAC,GACnE;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,gBAAgB;IAC3BC,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGT,KAAK,iBACZrD,OAAA,CAACZ,GAAG;MAACkF,KAAK,EAAElB,gBAAgB,CAACC,KAAK,CAAE;MAAAgB,QAAA,EACjCf,eAAe,CAACD,KAAK;IAAC;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB;EAET,CAAC,EACD;IACEjB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,iBAAiB;IAC5BC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGuB,IAAI,IAAK,GAAGA,IAAI,GAAG;IAC5BJ,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzC,eAAe,GAAG0C,CAAC,CAAC1C;EAC1C,CAAC,EACD;IACEiB,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACC,CAAC,EAAEc,MAAM,kBAChB7E,OAAA;MAAAqE,QAAA,gBACErE,OAAA,CAACX,MAAM;QACL6D,IAAI,EAAC,MAAM;QACXoC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAEvF,OAAA,CAACJ,WAAW;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBa,OAAO,EAAEA,CAAA,KAAMjC,eAAe,CAACsB,MAAM,CAAE;QAAAR,QAAA,EACxC;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3E,OAAA,CAACX,MAAM;QACL6D,IAAI,EAAC,MAAM;QACXoC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAEvF,OAAA,CAACH,YAAY;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAN,QAAA,EACxB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAET,CAAC,CACF;;EAED;EACA,MAAMc,MAAM,GAAG9D,KAAK,CAACC,OAAO,CAACrB,OAAO,CAAC,GAAG,CAAC,GAAG,IAAImF,GAAG,CAACnF,OAAO,CAACoF,GAAG,CAAC7C,MAAM,IAAIA,MAAM,CAACb,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;EAE9F,IAAI5B,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKmE,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEwB,OAAO,EAAE;MAAO,CAAE;MAAAvB,QAAA,gBACnDrE,OAAA,CAACV,IAAI;QAACgG,IAAI,EAAC;MAAO;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB3E,OAAA;QAAKmE,KAAK,EAAE;UAAE0B,SAAS,EAAE;QAAG,CAAE;QAAAxB,QAAA,EAAC;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC;EAEV;EAEA,oBACE3E,OAAA;IAAAqE,QAAA,gBAEErE,OAAA,CAAChB,GAAG;MAAC8G,MAAM,EAAE,EAAG;MAAC3B,KAAK,EAAE;QAAE4B,YAAY,EAAE;MAAG,CAAE;MAAA1B,QAAA,gBAC3CrE,OAAA,CAACf,GAAG;QAAC+G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACzBrE,OAAA,CAACC,MAAM;UACLkG,WAAW,EAAC,0EAAc;UAC1BC,UAAU;UACVC,QAAQ,EAAEzF,aAAc;UACxB0F,QAAQ,EAAGC,CAAC,IAAK3F,aAAa,CAAC2F,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;UAC/CC,MAAM,eAAE1G,OAAA,CAACL,cAAc;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN3E,OAAA,CAACf,GAAG;QAAC+G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACzBrE,OAAA,CAACR,MAAM;UACLiH,KAAK,EAAE5F,WAAY;UACnByF,QAAQ,EAAExF,cAAe;UACzBqD,KAAK,EAAE;YAAEN,KAAK,EAAE;UAAO,CAAE;UACzBsC,WAAW,EAAC,0BAAM;UAAA9B,QAAA,gBAElBrE,OAAA,CAACE,MAAM;YAACuG,KAAK,EAAC,KAAK;YAAApC,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAChCc,MAAM,CAACE,GAAG,CAAC1D,KAAK,iBACfjC,OAAA,CAACE,MAAM;YAAauG,KAAK,EAAExE,KAAM;YAAAoC,QAAA,EAAEpC;UAAK,GAA3BA,KAAK;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA+B,CAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3E,OAAA,CAAChB,GAAG;MAAC8G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAC3B,KAAK,EAAE;QAAE4B,YAAY,EAAE;MAAG,CAAE;MAAA1B,QAAA,gBACjDrE,OAAA,CAACf,GAAG;QAAC+G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eACjBrE,OAAA,CAACd,IAAI;UAACkG,SAAS,EAAC,aAAa;UAAAf,QAAA,gBAC3BrE,OAAA;YAAKoF,SAAS,EAAC,cAAc;YAACjB,KAAK,EAAE;cAAEG,KAAK,EAAE;YAAU,CAAE;YAAAD,QAAA,EACvD9D,OAAO,CAACoG;UAAM;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACN3E,OAAA;YAAKoF,SAAS,EAAC,cAAc;YAAAf,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3E,OAAA,CAACf,GAAG;QAAC+G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eACjBrE,OAAA,CAACd,IAAI;UAACkG,SAAS,EAAC,aAAa;UAAAf,QAAA,gBAC3BrE,OAAA;YAAKoF,SAAS,EAAC,cAAc;YAACjB,KAAK,EAAE;cAAEG,KAAK,EAAE;YAAU,CAAE;YAAAD,QAAA,EACvD9D,OAAO,CAACsC,MAAM,CAAC+D,CAAC,IAAIA,CAAC,CAACpE,WAAW,KAAK,UAAU,CAAC,CAACmE;UAAM;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACN3E,OAAA;YAAKoF,SAAS,EAAC,cAAc;YAAAf,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3E,OAAA,CAACf,GAAG;QAAC+G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eACjBrE,OAAA,CAACd,IAAI;UAACkG,SAAS,EAAC,aAAa;UAAAf,QAAA,gBAC3BrE,OAAA;YAAKoF,SAAS,EAAC,cAAc;YAACjB,KAAK,EAAE;cAAEG,KAAK,EAAE;YAAU,CAAE;YAAAD,QAAA,EACvD9D,OAAO,CAACsC,MAAM,CAAC+D,CAAC,IAAIA,CAAC,CAACvE,UAAU,GAAG,CAAC,CAAC,CAACsE;UAAM;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN3E,OAAA;YAAKoF,SAAS,EAAC,cAAc;YAAAf,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3E,OAAA,CAACf,GAAG;QAAC+G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eACjBrE,OAAA,CAACd,IAAI;UAACkG,SAAS,EAAC,aAAa;UAAAf,QAAA,gBAC3BrE,OAAA;YAAKoF,SAAS,EAAC,cAAc;YAACjB,KAAK,EAAE;cAAEG,KAAK,EAAE;YAAU,CAAE;YAAAD,QAAA,EACvDoB,MAAM,CAACkB;UAAM;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACN3E,OAAA;YAAKoF,SAAS,EAAC,cAAc;YAAAf,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3E,OAAA,CAACd,IAAI;MAACwE,KAAK,EAAC,sCAAQ;MAAC0B,SAAS,EAAC,gBAAgB;MAAAf,QAAA,eAC7CrE,OAAA,CAACb,KAAK;QACJsE,OAAO,EAAEA,OAAQ;QACjBoD,UAAU,EAAEpG,eAAgB;QAC5BqG,MAAM,EAAC,QAAQ;QACfC,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC,CAAE;QACFC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK;MAAE;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP3E,OAAA,CAACP,KAAK;MACJiE,KAAK,EAAE,GAAG3C,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiB,IAAI,KAAKjB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgB,MAAM,MAAO;MAC9DwF,IAAI,EAAEtG,aAAc;MACpBuG,QAAQ,EAAEA,CAAA,KAAMtG,gBAAgB,CAAC,KAAK,CAAE;MACxCuG,MAAM,EAAE,IAAK;MACb5D,KAAK,EAAE,GAAI;MAAAQ,QAAA,EAEVtD,aAAa,iBACZf,OAAA;QAAAqE,QAAA,gBACErE,OAAA,CAAChB,GAAG;UAAC8G,MAAM,EAAE,EAAG;UAAAzB,QAAA,gBACdrE,OAAA,CAACf,GAAG;YAACyI,IAAI,EAAE,EAAG;YAAArD,QAAA,eACZrE,OAAA,CAACd,IAAI;cAACwE,KAAK,EAAC,0BAAM;cAAC4B,IAAI,EAAC,OAAO;cAAAjB,QAAA,gBAC7BrE,OAAA;gBAAAqE,QAAA,GAAG,4BAAM,EAACtD,aAAa,CAACiB,IAAI;cAAA;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjC3E,OAAA;gBAAAqE,QAAA,GAAG,4BAAM,EAACtD,aAAa,CAACgB,MAAM;cAAA;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnC3E,OAAA;gBAAAqE,QAAA,GAAG,4BAAM,EAACtD,aAAa,CAACkB,KAAK;cAAA;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClC3E,OAAA;gBAAAqE,QAAA,GAAG,gBAAI,EAAC,CAACtD,aAAa,CAACoB,UAAU,GAAG,SAAS,EAAE6C,OAAO,CAAC,CAAC,CAAC,EAAC,QAAC;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN3E,OAAA,CAACf,GAAG;YAACyI,IAAI,EAAE,EAAG;YAAArD,QAAA,eACZrE,OAAA,CAACd,IAAI;cAACwE,KAAK,EAAC,0BAAM;cAAC4B,IAAI,EAAC,OAAO;cAAAjB,QAAA,gBAC7BrE,OAAA;gBAAAqE,QAAA,GAAG,4BAAM,EAACtD,aAAa,CAACmB,YAAY,CAAC8C,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpD3E,OAAA;gBAAAqE,QAAA,GAAG,4BAAM,EAAClB,iBAAiB,CAACpC,aAAa,CAACyB,WAAW,CAAC;cAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3D3E,OAAA;gBAAAqE,QAAA,GAAG,4BAAM,EAACf,eAAe,CAACvC,aAAa,CAAC4B,cAAc,CAAC;cAAA;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5D3E,OAAA;gBAAAqE,QAAA,GAAG,4BAAM,EAACtD,aAAa,CAAC0B,eAAe,EAAC,QAAC;cAAA;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3E,OAAA,CAAChB,GAAG;UAAC8G,MAAM,EAAE,EAAG;UAAC3B,KAAK,EAAE;YAAE0B,SAAS,EAAE;UAAG,CAAE;UAAAxB,QAAA,gBACxCrE,OAAA,CAACf,GAAG;YAACyI,IAAI,EAAE,EAAG;YAAArD,QAAA,eACZrE,OAAA,CAACd,IAAI;cAACwE,KAAK,EAAC,0BAAM;cAAC4B,IAAI,EAAC,OAAO;cAAAjB,QAAA,gBAC7BrE,OAAA;gBAAAqE,QAAA,GAAG,gCAAO,EAACtD,aAAa,CAACqB,KAAK,CAAC4C,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9C3E,OAAA;gBAAAqE,QAAA,GAAG,sBAAK,EAACtD,aAAa,CAACsB,UAAU,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEtB,aAAa,CAACsB,UAAU,CAAC2C,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC5F3E,OAAA;gBAAAqE,QAAA,GAAG,gBAAI,EAACtD,aAAa,CAACuB,YAAY,CAAC0C,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClD3E,OAAA;gBAAAqE,QAAA,GAAG,sBAAK,EAACtD,aAAa,CAACwB,aAAa,CAACyC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN3E,OAAA,CAACf,GAAG;YAACyI,IAAI,EAAE,EAAG;YAAArD,QAAA,eACZrE,OAAA,CAACd,IAAI;cAACwE,KAAK,EAAC,0BAAM;cAAC4B,IAAI,EAAC,OAAO;cAAAjB,QAAA,gBAC7BrE,OAAA;gBAAAqE,QAAA,GAAG,kCAAO,EAACtD,aAAa,CAAC2B,cAAc,EAAC,QAAC;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC7C3E,OAAA;gBAAAqE,QAAA,GAAG,gBAAI,EAACtD,aAAa,CAACkB,KAAK;cAAA;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChC3E,OAAA;gBAAAqE,QAAA,GAAG,4BAAM,EAAClB,iBAAiB,CAACpC,aAAa,CAACyB,WAAW,CAAC;cAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACvE,EAAA,CApaID,YAAY;AAAAwH,EAAA,GAAZxH,YAAY;AAsalB,eAAeA,YAAY;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}