{"ast": null, "code": "import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar string = function string(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'string');\n    if (!isEmptyValue(value, 'string')) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n      rules.pattern(rule, value, source, errors, options);\n      if (rule.whitespace === true) {\n        rules.whitespace(rule, value, source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\nexport default string;", "map": {"version": 3, "names": ["rules", "isEmptyValue", "string", "rule", "value", "callback", "source", "options", "errors", "validate", "required", "hasOwnProperty", "field", "type", "range", "pattern", "whitespace"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/node_modules/@rc-component/async-validator/es/validator/string.js"], "sourcesContent": ["import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar string = function string(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, 'string') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, 'string');\n    if (!isEmptyValue(value, 'string')) {\n      rules.type(rule, value, source, errors, options);\n      rules.range(rule, value, source, errors, options);\n      rules.pattern(rule, value, source, errors, options);\n      if (rule.whitespace === true) {\n        rules.whitespace(rule, value, source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\nexport default string;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,SAASC,YAAY,QAAQ,SAAS;AACtC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACnE,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGN,IAAI,CAACO,QAAQ,IAAI,CAACP,IAAI,CAACO,QAAQ,IAAIJ,MAAM,CAACK,cAAc,CAACR,IAAI,CAACS,KAAK,CAAC;EACnF,IAAIH,QAAQ,EAAE;IACZ,IAAIR,YAAY,CAACG,KAAK,EAAE,QAAQ,CAAC,IAAI,CAACD,IAAI,CAACO,QAAQ,EAAE;MACnD,OAAOL,QAAQ,CAAC,CAAC;IACnB;IACAL,KAAK,CAACU,QAAQ,CAACP,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,EAAE,QAAQ,CAAC;IAC9D,IAAI,CAACN,YAAY,CAACG,KAAK,EAAE,QAAQ,CAAC,EAAE;MAClCJ,KAAK,CAACa,IAAI,CAACV,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;MAChDP,KAAK,CAACc,KAAK,CAACX,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;MACjDP,KAAK,CAACe,OAAO,CAACZ,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;MACnD,IAAIJ,IAAI,CAACa,UAAU,KAAK,IAAI,EAAE;QAC5BhB,KAAK,CAACgB,UAAU,CAACb,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;MACxD;IACF;EACF;EACAF,QAAQ,CAACG,MAAM,CAAC;AAClB,CAAC;AACD,eAAeN,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}