{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Portfolio.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Table, Tag, Button, Spin, Progress, Modal, Form, Input, InputNumber } from 'antd';\nimport { WalletOutlined, PlusOutlined, MinusOutlined, DollarOutlined } from '@ant-design/icons';\nimport ReactECharts from 'echarts-for-react';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Portfolio = () => {\n  _s();\n  var _portfolioMetrics$tot, _portfolioMetrics$tot2, _portfolioMetrics$cas, _portfolioMetrics$sha, _portfolioMetrics$win, _portfolioMetrics$max;\n  const [loading, setLoading] = useState(true);\n  const [positions, setPositions] = useState([]);\n  const [portfolioMetrics, setPortfolioMetrics] = useState(null);\n  const [addPositionVisible, setAddPositionVisible] = useState(false);\n  const [form] = Form.useForm();\n  useEffect(() => {\n    loadPortfolioData();\n  }, []);\n  const loadPortfolioData = async () => {\n    try {\n      setLoading(true);\n      const [positionsData, metricsData] = await Promise.allSettled([apiService.getCurrentPositions(), apiService.getPortfolioMetrics()]);\n      if (positionsData.status === 'fulfilled') {\n        var _positionsData$value;\n        console.log('Positions data received:', positionsData.value);\n        const positions = ((_positionsData$value = positionsData.value) === null || _positionsData$value === void 0 ? void 0 : _positionsData$value.data) || positionsData.value;\n        setPositions(Array.isArray(positions) ? positions : []);\n      } else {\n        // 模拟持仓数据\n        const mockPositions = [{\n          position_id: '1',\n          symbol: '300750',\n          name: '宁德时代',\n          quantity: 1000,\n          avg_cost: 220.50,\n          current_price: 245.80,\n          market_value: 245800,\n          pnl: 25300,\n          pnl_pct: 11.47,\n          weight: 24.58,\n          theme: '新能源汽车',\n          open_date: '2024-12-01',\n          days_held: 16\n        }, {\n          position_id: '2',\n          symbol: '002594',\n          name: '比亚迪',\n          quantity: 500,\n          avg_cost: 260.00,\n          current_price: 280.50,\n          market_value: 140250,\n          pnl: 10250,\n          pnl_pct: 7.88,\n          weight: 14.03,\n          theme: '新能源汽车',\n          open_date: '2024-12-05',\n          days_held: 12\n        }];\n        setPositions(mockPositions);\n      }\n      if (metricsData.status === 'fulfilled') {\n        var _metricsData$value;\n        console.log('Portfolio metrics received:', metricsData.value);\n        const metrics = ((_metricsData$value = metricsData.value) === null || _metricsData$value === void 0 ? void 0 : _metricsData$value.data) || metricsData.value;\n        setPortfolioMetrics(metrics);\n      } else {\n        // 模拟组合指标\n        setPortfolioMetrics({\n          total_value: 1000000,\n          total_cost: 950000,\n          total_pnl: 50000,\n          total_pnl_pct: 5.26,\n          cash_balance: 614000,\n          position_count: 2,\n          max_drawdown: -8.5,\n          sharpe_ratio: 1.25,\n          win_rate: 68.5\n        });\n      }\n    } catch (error) {\n      console.error('加载投资组合数据失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAddPosition = async values => {\n    try {\n      await apiService.openPosition(values);\n      setAddPositionVisible(false);\n      form.resetFields();\n      loadPortfolioData();\n    } catch (error) {\n      console.error('开仓失败:', error);\n    }\n  };\n  const handleClosePosition = async positionId => {\n    try {\n      await apiService.closePosition(positionId);\n      loadPortfolioData();\n    } catch (error) {\n      console.error('平仓失败:', error);\n    }\n  };\n\n  // 持仓分布饼图\n  const getPositionDistributionOption = () => {\n    if (!Array.isArray(positions) || positions.length === 0) {\n      return {\n        title: {\n          text: '持仓分布',\n          left: 'center'\n        },\n        series: []\n      };\n    }\n    const data = positions.map(pos => ({\n      name: pos.name,\n      value: pos.weight\n    }));\n    return {\n      title: {\n        text: '持仓分布',\n        left: 'center'\n      },\n      tooltip: {\n        trigger: 'item',\n        formatter: '{a} <br/>{b}: {c}% ({d}%)'\n      },\n      legend: {\n        orient: 'vertical',\n        left: 'left'\n      },\n      series: [{\n        name: '持仓权重',\n        type: 'pie',\n        radius: '50%',\n        data: data,\n        emphasis: {\n          itemStyle: {\n            shadowBlur: 10,\n            shadowOffsetX: 0,\n            shadowColor: 'rgba(0, 0, 0, 0.5)'\n          }\n        }\n      }]\n    };\n  };\n  const columns = [{\n    title: '股票信息',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold'\n        },\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#666'\n        },\n        children: record.symbol\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '数量',\n    dataIndex: 'quantity',\n    key: 'quantity',\n    render: quantity => quantity.toLocaleString()\n  }, {\n    title: '成本价',\n    dataIndex: 'avg_cost',\n    key: 'avg_cost',\n    render: cost => `¥${(cost || 0).toFixed(2)}`\n  }, {\n    title: '现价',\n    dataIndex: 'current_price',\n    key: 'current_price',\n    render: price => `¥${(price || 0).toFixed(2)}`\n  }, {\n    title: '市值',\n    dataIndex: 'market_value',\n    key: 'market_value',\n    render: value => `¥${value.toLocaleString()}`\n  }, {\n    title: '盈亏',\n    dataIndex: 'pnl',\n    key: 'pnl',\n    render: (pnl, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: pnl >= 0 ? 'price-up' : 'price-down',\n        children: [\"\\xA5\", pnl.toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: record.pnl_pct >= 0 ? 'price-up' : 'price-down',\n        children: [record.pnl_pct >= 0 ? '+' : '', record.pnl_pct.toFixed(2), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.pnl - b.pnl\n  }, {\n    title: '权重',\n    dataIndex: 'weight',\n    key: 'weight',\n    render: weight => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [weight.toFixed(2), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Progress, {\n        percent: weight,\n        size: \"small\",\n        showInfo: false,\n        strokeColor: weight > 20 ? '#ff4d4f' : '#1890ff'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '持有天数',\n    dataIndex: 'days_held',\n    key: 'days_held',\n    render: days => `${days}天`\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"link\",\n      danger: true,\n      size: \"small\",\n      icon: /*#__PURE__*/_jsxDEV(MinusOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 17\n      }, this),\n      onClick: () => handleClosePosition(record.position_id),\n      children: \"\\u5E73\\u4ED3\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 9\n    }, this)\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u6295\\u8D44\\u7EC4\\u5408\\u6570\\u636E...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            style: {\n              color: '#1890ff'\n            },\n            children: [\"\\xA5\", (portfolioMetrics === null || portfolioMetrics === void 0 ? void 0 : (_portfolioMetrics$tot = portfolioMetrics.total_value) === null || _portfolioMetrics$tot === void 0 ? void 0 : _portfolioMetrics$tot.toLocaleString()) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-label\",\n            children: \"\\u603B\\u8D44\\u4EA7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            style: {\n              color: ((portfolioMetrics === null || portfolioMetrics === void 0 ? void 0 : portfolioMetrics.total_pnl) || 0) >= 0 ? '#ff4d4f' : '#52c41a'\n            },\n            children: [\"\\xA5\", (portfolioMetrics === null || portfolioMetrics === void 0 ? void 0 : (_portfolioMetrics$tot2 = portfolioMetrics.total_pnl) === null || _portfolioMetrics$tot2 === void 0 ? void 0 : _portfolioMetrics$tot2.toLocaleString()) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-label\",\n            children: \"\\u603B\\u76C8\\u4E8F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            style: {\n              color: ((portfolioMetrics === null || portfolioMetrics === void 0 ? void 0 : portfolioMetrics.total_pnl_pct) || 0) >= 0 ? '#ff4d4f' : '#52c41a'\n            },\n            children: [(((portfolioMetrics === null || portfolioMetrics === void 0 ? void 0 : portfolioMetrics.total_pnl_pct) || 0) * 100).toFixed(2), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-label\",\n            children: \"\\u603B\\u6536\\u76CA\\u7387\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            style: {\n              color: '#52c41a'\n            },\n            children: [\"\\xA5\", (portfolioMetrics === null || portfolioMetrics === void 0 ? void 0 : (_portfolioMetrics$cas = portfolioMetrics.cash_balance) === null || _portfolioMetrics$cas === void 0 ? void 0 : _portfolioMetrics$cas.toLocaleString()) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-label\",\n            children: \"\\u73B0\\u91D1\\u4F59\\u989D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6301\\u4ED3\\u5206\\u5E03\",\n          className: \"dashboard-card\",\n          children: positions.length > 0 ? /*#__PURE__*/_jsxDEV(ReactECharts, {\n            option: getPositionDistributionOption(),\n            style: {\n              height: '300px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '50px',\n              color: '#666'\n            },\n            children: \"\\u6682\\u65E0\\u6301\\u4ED3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u98CE\\u9669\\u6307\\u6807\",\n          className: \"dashboard-card\",\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center',\n                  marginBottom: 16\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '24px',\n                    fontWeight: 'bold'\n                  },\n                  children: (portfolioMetrics === null || portfolioMetrics === void 0 ? void 0 : (_portfolioMetrics$sha = portfolioMetrics.sharpe_ratio) === null || _portfolioMetrics$sha === void 0 ? void 0 : _portfolioMetrics$sha.toFixed(2)) || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#666'\n                  },\n                  children: \"\\u590F\\u666E\\u6BD4\\u7387\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center',\n                  marginBottom: 16\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '24px',\n                    fontWeight: 'bold'\n                  },\n                  children: [(portfolioMetrics === null || portfolioMetrics === void 0 ? void 0 : (_portfolioMetrics$win = portfolioMetrics.win_rate) === null || _portfolioMetrics$win === void 0 ? void 0 : _portfolioMetrics$win.toFixed(1)) || 0, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#666'\n                  },\n                  children: \"\\u80DC\\u7387\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8\n              },\n              children: [\"\\u6700\\u5927\\u56DE\\u64A4: \", (portfolioMetrics === null || portfolioMetrics === void 0 ? void 0 : (_portfolioMetrics$max = portfolioMetrics.max_drawdown) === null || _portfolioMetrics$max === void 0 ? void 0 : _portfolioMetrics$max.toFixed(2)) || 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: Math.abs((portfolioMetrics === null || portfolioMetrics === void 0 ? void 0 : portfolioMetrics.max_drawdown) || 0),\n              status: \"exception\",\n              strokeColor: \"#ff4d4f\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 19\n          }, this),\n          onClick: () => setAddPositionVisible(true),\n          children: \"\\u65B0\\u589E\\u6301\\u4ED3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6301\\u4ED3\\u660E\\u7EC6\",\n      className: \"dashboard-card\",\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: positions,\n        rowKey: \"position_id\",\n        pagination: false,\n        scroll: {\n          x: 1000\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u65B0\\u589E\\u6301\\u4ED3\",\n      open: addPositionVisible,\n      onCancel: () => setAddPositionVisible(false),\n      onOk: () => form.submit(),\n      okText: \"\\u786E\\u8BA4\\u5F00\\u4ED3\",\n      cancelText: \"\\u53D6\\u6D88\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleAddPosition,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"symbol\",\n          label: \"\\u80A1\\u7968\\u4EE3\\u7801\",\n          rules: [{\n            required: true,\n            message: '请输入股票代码'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982: 300750\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"quantity\",\n          label: \"\\u4E70\\u5165\\u6570\\u91CF\",\n          rules: [{\n            required: true,\n            message: '请输入买入数量'\n          }],\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u80A1\\u6570\",\n            min: 100,\n            step: 100\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"price\",\n          label: \"\\u4E70\\u5165\\u4EF7\\u683C\",\n          rules: [{\n            required: true,\n            message: '请输入买入价格'\n          }],\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u5143\",\n            min: 0,\n            step: 0.01,\n            precision: 2\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 259,\n    columnNumber: 5\n  }, this);\n};\n_s(Portfolio, \"dyyzicg31ANo9nUvmoWW8vrGWhA=\", false, function () {\n  return [Form.useForm];\n});\n_c = Portfolio;\nexport default Portfolio;\nvar _c;\n$RefreshReg$(_c, \"Portfolio\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Table", "Tag", "<PERSON><PERSON>", "Spin", "Progress", "Modal", "Form", "Input", "InputNumber", "WalletOutlined", "PlusOutlined", "MinusOutlined", "DollarOutlined", "ReactECharts", "apiService", "jsxDEV", "_jsxDEV", "Portfolio", "_s", "_portfolioMetrics$tot", "_portfolioMetrics$tot2", "_portfolioMetrics$cas", "_portfolioMetrics$sha", "_portfolioMetrics$win", "_portfolioMetrics$max", "loading", "setLoading", "positions", "setPositions", "portfolioMetrics", "setPortfolioMetrics", "addPositionVisible", "setAddPositionVisible", "form", "useForm", "loadPortfolioData", "positionsData", "metricsData", "Promise", "allSettled", "getCurrentPositions", "getPortfolioMetrics", "status", "_positionsData$value", "console", "log", "value", "data", "Array", "isArray", "mockPositions", "position_id", "symbol", "name", "quantity", "avg_cost", "current_price", "market_value", "pnl", "pnl_pct", "weight", "theme", "open_date", "days_held", "_metricsData$value", "metrics", "total_value", "total_cost", "total_pnl", "total_pnl_pct", "cash_balance", "position_count", "max_drawdown", "sharpe_ratio", "win_rate", "error", "handleAddPosition", "values", "openPosition", "resetFields", "handleClosePosition", "positionId", "closePosition", "getPositionDistributionOption", "length", "title", "text", "left", "series", "map", "pos", "tooltip", "trigger", "formatter", "legend", "orient", "type", "radius", "emphasis", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "columns", "dataIndex", "key", "render", "record", "children", "style", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "color", "toLocaleString", "cost", "toFixed", "price", "className", "sorter", "a", "b", "percent", "size", "showInfo", "strokeColor", "days", "_", "danger", "icon", "onClick", "textAlign", "padding", "marginTop", "gutter", "marginBottom", "xs", "sm", "lg", "option", "height", "span", "Math", "abs", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "scroll", "x", "open", "onCancel", "onOk", "submit", "okText", "cancelText", "layout", "onFinish", "<PERSON><PERSON>", "label", "rules", "required", "message", "placeholder", "width", "min", "step", "precision", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Portfolio.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Table, Tag, Button, Spin, Progress, Modal, Form, Input, InputNumber } from 'antd';\nimport { WalletOutlined, PlusOutlined, MinusOutlined, DollarOutlined } from '@ant-design/icons';\nimport ReactECharts from 'echarts-for-react';\nimport apiService from '../services/api';\n\nconst Portfolio = () => {\n  const [loading, setLoading] = useState(true);\n  const [positions, setPositions] = useState([]);\n  const [portfolioMetrics, setPortfolioMetrics] = useState(null);\n  const [addPositionVisible, setAddPositionVisible] = useState(false);\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    loadPortfolioData();\n  }, []);\n\n  const loadPortfolioData = async () => {\n    try {\n      setLoading(true);\n      \n      const [positionsData, metricsData] = await Promise.allSettled([\n        apiService.getCurrentPositions(),\n        apiService.getPortfolioMetrics()\n      ]);\n\n      if (positionsData.status === 'fulfilled') {\n        console.log('Positions data received:', positionsData.value);\n        const positions = positionsData.value?.data || positionsData.value;\n        setPositions(Array.isArray(positions) ? positions : []);\n      } else {\n        // 模拟持仓数据\n        const mockPositions = [\n          {\n            position_id: '1',\n            symbol: '300750',\n            name: '宁德时代',\n            quantity: 1000,\n            avg_cost: 220.50,\n            current_price: 245.80,\n            market_value: 245800,\n            pnl: 25300,\n            pnl_pct: 11.47,\n            weight: 24.58,\n            theme: '新能源汽车',\n            open_date: '2024-12-01',\n            days_held: 16\n          },\n          {\n            position_id: '2',\n            symbol: '002594',\n            name: '比亚迪',\n            quantity: 500,\n            avg_cost: 260.00,\n            current_price: 280.50,\n            market_value: 140250,\n            pnl: 10250,\n            pnl_pct: 7.88,\n            weight: 14.03,\n            theme: '新能源汽车',\n            open_date: '2024-12-05',\n            days_held: 12\n          }\n        ];\n        setPositions(mockPositions);\n      }\n\n      if (metricsData.status === 'fulfilled') {\n        console.log('Portfolio metrics received:', metricsData.value);\n        const metrics = metricsData.value?.data || metricsData.value;\n        setPortfolioMetrics(metrics);\n      } else {\n        // 模拟组合指标\n        setPortfolioMetrics({\n          total_value: 1000000,\n          total_cost: 950000,\n          total_pnl: 50000,\n          total_pnl_pct: 5.26,\n          cash_balance: 614000,\n          position_count: 2,\n          max_drawdown: -8.5,\n          sharpe_ratio: 1.25,\n          win_rate: 68.5\n        });\n      }\n    } catch (error) {\n      console.error('加载投资组合数据失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAddPosition = async (values) => {\n    try {\n      await apiService.openPosition(values);\n      setAddPositionVisible(false);\n      form.resetFields();\n      loadPortfolioData();\n    } catch (error) {\n      console.error('开仓失败:', error);\n    }\n  };\n\n  const handleClosePosition = async (positionId) => {\n    try {\n      await apiService.closePosition(positionId);\n      loadPortfolioData();\n    } catch (error) {\n      console.error('平仓失败:', error);\n    }\n  };\n\n  // 持仓分布饼图\n  const getPositionDistributionOption = () => {\n    if (!Array.isArray(positions) || positions.length === 0) {\n      return {\n        title: { text: '持仓分布', left: 'center' },\n        series: []\n      };\n    }\n\n    const data = positions.map(pos => ({\n      name: pos.name,\n      value: pos.weight\n    }));\n\n    return {\n      title: {\n        text: '持仓分布',\n        left: 'center'\n      },\n      tooltip: {\n        trigger: 'item',\n        formatter: '{a} <br/>{b}: {c}% ({d}%)'\n      },\n      legend: {\n        orient: 'vertical',\n        left: 'left'\n      },\n      series: [\n        {\n          name: '持仓权重',\n          type: 'pie',\n          radius: '50%',\n          data: data,\n          emphasis: {\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          }\n        }\n      ]\n    };\n  };\n\n  const columns = [\n    {\n      title: '股票信息',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <div>\n          <div style={{ fontWeight: 'bold' }}>{text}</div>\n          <div style={{ fontSize: '12px', color: '#666' }}>{record.symbol}</div>\n        </div>\n      )\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n      render: (quantity) => quantity.toLocaleString()\n    },\n    {\n      title: '成本价',\n      dataIndex: 'avg_cost',\n      key: 'avg_cost',\n      render: (cost) => `¥${(cost || 0).toFixed(2)}`\n    },\n    {\n      title: '现价',\n      dataIndex: 'current_price',\n      key: 'current_price',\n      render: (price) => `¥${(price || 0).toFixed(2)}`\n    },\n    {\n      title: '市值',\n      dataIndex: 'market_value',\n      key: 'market_value',\n      render: (value) => `¥${value.toLocaleString()}`\n    },\n    {\n      title: '盈亏',\n      dataIndex: 'pnl',\n      key: 'pnl',\n      render: (pnl, record) => (\n        <div>\n          <div className={pnl >= 0 ? 'price-up' : 'price-down'}>\n            ¥{pnl.toLocaleString()}\n          </div>\n          <div className={record.pnl_pct >= 0 ? 'price-up' : 'price-down'}>\n            {record.pnl_pct >= 0 ? '+' : ''}{record.pnl_pct.toFixed(2)}%\n          </div>\n        </div>\n      ),\n      sorter: (a, b) => a.pnl - b.pnl\n    },\n    {\n      title: '权重',\n      dataIndex: 'weight',\n      key: 'weight',\n      render: (weight) => (\n        <div>\n          <div>{weight.toFixed(2)}%</div>\n          <Progress \n            percent={weight} \n            size=\"small\" \n            showInfo={false}\n            strokeColor={weight > 20 ? '#ff4d4f' : '#1890ff'}\n          />\n        </div>\n      )\n    },\n    {\n      title: '持有天数',\n      dataIndex: 'days_held',\n      key: 'days_held',\n      render: (days) => `${days}天`\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Button \n          type=\"link\" \n          danger \n          size=\"small\"\n          icon={<MinusOutlined />}\n          onClick={() => handleClosePosition(record.position_id)}\n        >\n          平仓\n        </Button>\n      )\n    }\n  ];\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>正在加载投资组合数据...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* 组合概览 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={12} sm={6}>\n          <Card className=\"metric-card\">\n            <div className=\"metric-value\" style={{ color: '#1890ff' }}>\n              ¥{portfolioMetrics?.total_value?.toLocaleString() || 0}\n            </div>\n            <div className=\"metric-label\">总资产</div>\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card className=\"metric-card\">\n            <div className=\"metric-value\" style={{ \n              color: (portfolioMetrics?.total_pnl || 0) >= 0 ? '#ff4d4f' : '#52c41a' \n            }}>\n              ¥{portfolioMetrics?.total_pnl?.toLocaleString() || 0}\n            </div>\n            <div className=\"metric-label\">总盈亏</div>\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card className=\"metric-card\">\n            <div className=\"metric-value\" style={{ \n              color: (portfolioMetrics?.total_pnl_pct || 0) >= 0 ? '#ff4d4f' : '#52c41a' \n            }}>\n              {((portfolioMetrics?.total_pnl_pct || 0) * 100).toFixed(2)}%\n            </div>\n            <div className=\"metric-label\">总收益率</div>\n          </Card>\n        </Col>\n        <Col xs={12} sm={6}>\n          <Card className=\"metric-card\">\n            <div className=\"metric-value\" style={{ color: '#52c41a' }}>\n              ¥{portfolioMetrics?.cash_balance?.toLocaleString() || 0}\n            </div>\n            <div className=\"metric-label\">现金余额</div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 组合分析 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} lg={12}>\n          <Card title=\"持仓分布\" className=\"dashboard-card\">\n            {positions.length > 0 ? (\n              <ReactECharts \n                option={getPositionDistributionOption()} \n                style={{ height: '300px' }}\n              />\n            ) : (\n              <div style={{ textAlign: 'center', padding: '50px', color: '#666' }}>\n                暂无持仓\n              </div>\n            )}\n          </Card>\n        </Col>\n        <Col xs={24} lg={12}>\n          <Card title=\"风险指标\" className=\"dashboard-card\">\n            <Row gutter={16}>\n              <Col span={12}>\n                <div style={{ textAlign: 'center', marginBottom: 16 }}>\n                  <div style={{ fontSize: '24px', fontWeight: 'bold' }}>\n                    {portfolioMetrics?.sharpe_ratio?.toFixed(2) || 0}\n                  </div>\n                  <div style={{ color: '#666' }}>夏普比率</div>\n                </div>\n              </Col>\n              <Col span={12}>\n                <div style={{ textAlign: 'center', marginBottom: 16 }}>\n                  <div style={{ fontSize: '24px', fontWeight: 'bold' }}>\n                    {portfolioMetrics?.win_rate?.toFixed(1) || 0}%\n                  </div>\n                  <div style={{ color: '#666' }}>胜率</div>\n                </div>\n              </Col>\n            </Row>\n            <div style={{ marginTop: 16 }}>\n              <div style={{ marginBottom: 8 }}>\n                最大回撤: {portfolioMetrics?.max_drawdown?.toFixed(2) || 0}%\n              </div>\n              <Progress\n                percent={Math.abs(portfolioMetrics?.max_drawdown || 0)}\n                status=\"exception\"\n                strokeColor=\"#ff4d4f\"\n              />\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 操作按钮 */}\n      <Row style={{ marginBottom: 16 }}>\n        <Col>\n          <Button \n            type=\"primary\" \n            icon={<PlusOutlined />}\n            onClick={() => setAddPositionVisible(true)}\n          >\n            新增持仓\n          </Button>\n        </Col>\n      </Row>\n\n      {/* 持仓明细 */}\n      <Card title=\"持仓明细\" className=\"dashboard-card\">\n        <Table\n          columns={columns}\n          dataSource={positions}\n          rowKey=\"position_id\"\n          pagination={false}\n          scroll={{ x: 1000 }}\n        />\n      </Card>\n\n      {/* 新增持仓弹窗 */}\n      <Modal\n        title=\"新增持仓\"\n        open={addPositionVisible}\n        onCancel={() => setAddPositionVisible(false)}\n        onOk={() => form.submit()}\n        okText=\"确认开仓\"\n        cancelText=\"取消\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleAddPosition}\n        >\n          <Form.Item\n            name=\"symbol\"\n            label=\"股票代码\"\n            rules={[{ required: true, message: '请输入股票代码' }]}\n          >\n            <Input placeholder=\"如: 300750\" />\n          </Form.Item>\n          <Form.Item\n            name=\"quantity\"\n            label=\"买入数量\"\n            rules={[{ required: true, message: '请输入买入数量' }]}\n          >\n            <InputNumber \n              style={{ width: '100%' }}\n              placeholder=\"股数\"\n              min={100}\n              step={100}\n            />\n          </Form.Item>\n          <Form.Item\n            name=\"price\"\n            label=\"买入价格\"\n            rules={[{ required: true, message: '请输入买入价格' }]}\n          >\n            <InputNumber \n              style={{ width: '100%' }}\n              placeholder=\"元\"\n              min={0}\n              step={0.01}\n              precision={2}\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default Portfolio;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,WAAW,QAAQ,MAAM;AAC1G,SAASC,cAAc,EAAEC,YAAY,EAAEC,aAAa,EAAEC,cAAc,QAAQ,mBAAmB;AAC/F,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACtB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACoC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACsC,IAAI,CAAC,GAAG3B,IAAI,CAAC4B,OAAO,CAAC,CAAC;EAE7BtC,SAAS,CAAC,MAAM;IACduC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM,CAACU,aAAa,EAAEC,WAAW,CAAC,GAAG,MAAMC,OAAO,CAACC,UAAU,CAAC,CAC5DzB,UAAU,CAAC0B,mBAAmB,CAAC,CAAC,EAChC1B,UAAU,CAAC2B,mBAAmB,CAAC,CAAC,CACjC,CAAC;MAEF,IAAIL,aAAa,CAACM,MAAM,KAAK,WAAW,EAAE;QAAA,IAAAC,oBAAA;QACxCC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAET,aAAa,CAACU,KAAK,CAAC;QAC5D,MAAMnB,SAAS,GAAG,EAAAgB,oBAAA,GAAAP,aAAa,CAACU,KAAK,cAAAH,oBAAA,uBAAnBA,oBAAA,CAAqBI,IAAI,KAAIX,aAAa,CAACU,KAAK;QAClElB,YAAY,CAACoB,KAAK,CAACC,OAAO,CAACtB,SAAS,CAAC,GAAGA,SAAS,GAAG,EAAE,CAAC;MACzD,CAAC,MAAM;QACL;QACA,MAAMuB,aAAa,GAAG,CACpB;UACEC,WAAW,EAAE,GAAG;UAChBC,MAAM,EAAE,QAAQ;UAChBC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,MAAM;UAChBC,aAAa,EAAE,MAAM;UACrBC,YAAY,EAAE,MAAM;UACpBC,GAAG,EAAE,KAAK;UACVC,OAAO,EAAE,KAAK;UACdC,MAAM,EAAE,KAAK;UACbC,KAAK,EAAE,OAAO;UACdC,SAAS,EAAE,YAAY;UACvBC,SAAS,EAAE;QACb,CAAC,EACD;UACEZ,WAAW,EAAE,GAAG;UAChBC,MAAM,EAAE,QAAQ;UAChBC,IAAI,EAAE,KAAK;UACXC,QAAQ,EAAE,GAAG;UACbC,QAAQ,EAAE,MAAM;UAChBC,aAAa,EAAE,MAAM;UACrBC,YAAY,EAAE,MAAM;UACpBC,GAAG,EAAE,KAAK;UACVC,OAAO,EAAE,IAAI;UACbC,MAAM,EAAE,KAAK;UACbC,KAAK,EAAE,OAAO;UACdC,SAAS,EAAE,YAAY;UACvBC,SAAS,EAAE;QACb,CAAC,CACF;QACDnC,YAAY,CAACsB,aAAa,CAAC;MAC7B;MAEA,IAAIb,WAAW,CAACK,MAAM,KAAK,WAAW,EAAE;QAAA,IAAAsB,kBAAA;QACtCpB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAER,WAAW,CAACS,KAAK,CAAC;QAC7D,MAAMmB,OAAO,GAAG,EAAAD,kBAAA,GAAA3B,WAAW,CAACS,KAAK,cAAAkB,kBAAA,uBAAjBA,kBAAA,CAAmBjB,IAAI,KAAIV,WAAW,CAACS,KAAK;QAC5DhB,mBAAmB,CAACmC,OAAO,CAAC;MAC9B,CAAC,MAAM;QACL;QACAnC,mBAAmB,CAAC;UAClBoC,WAAW,EAAE,OAAO;UACpBC,UAAU,EAAE,MAAM;UAClBC,SAAS,EAAE,KAAK;UAChBC,aAAa,EAAE,IAAI;UACnBC,YAAY,EAAE,MAAM;UACpBC,cAAc,EAAE,CAAC;UACjBC,YAAY,EAAE,CAAC,GAAG;UAClBC,YAAY,EAAE,IAAI;UAClBC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC,CAAC,SAAS;MACRjD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkD,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IAC1C,IAAI;MACF,MAAM/D,UAAU,CAACgE,YAAY,CAACD,MAAM,CAAC;MACrC7C,qBAAqB,CAAC,KAAK,CAAC;MAC5BC,IAAI,CAAC8C,WAAW,CAAC,CAAC;MAClB5C,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMK,mBAAmB,GAAG,MAAOC,UAAU,IAAK;IAChD,IAAI;MACF,MAAMnE,UAAU,CAACoE,aAAa,CAACD,UAAU,CAAC;MAC1C9C,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAMQ,6BAA6B,GAAGA,CAAA,KAAM;IAC1C,IAAI,CAACnC,KAAK,CAACC,OAAO,CAACtB,SAAS,CAAC,IAAIA,SAAS,CAACyD,MAAM,KAAK,CAAC,EAAE;MACvD,OAAO;QACLC,KAAK,EAAE;UAAEC,IAAI,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAS,CAAC;QACvCC,MAAM,EAAE;MACV,CAAC;IACH;IAEA,MAAMzC,IAAI,GAAGpB,SAAS,CAAC8D,GAAG,CAACC,GAAG,KAAK;MACjCrC,IAAI,EAAEqC,GAAG,CAACrC,IAAI;MACdP,KAAK,EAAE4C,GAAG,CAAC9B;IACb,CAAC,CAAC,CAAC;IAEH,OAAO;MACLyB,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE;MACR,CAAC;MACDI,OAAO,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,SAAS,EAAE;MACb,CAAC;MACDC,MAAM,EAAE;QACNC,MAAM,EAAE,UAAU;QAClBR,IAAI,EAAE;MACR,CAAC;MACDC,MAAM,EAAE,CACN;QACEnC,IAAI,EAAE,MAAM;QACZ2C,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,KAAK;QACblD,IAAI,EAAEA,IAAI;QACVmD,QAAQ,EAAE;UACRC,SAAS,EAAE;YACTC,UAAU,EAAE,EAAE;YACdC,aAAa,EAAE,CAAC;YAChBC,WAAW,EAAE;UACf;QACF;MACF,CAAC;IAEL,CAAC;EACH,CAAC;EAED,MAAMC,OAAO,GAAG,CACd;IACElB,KAAK,EAAE,MAAM;IACbmB,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACpB,IAAI,EAAEqB,MAAM,kBACnB3F,OAAA;MAAA4F,QAAA,gBACE5F,OAAA;QAAK6F,KAAK,EAAE;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAF,QAAA,EAAEtB;MAAI;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAChDlG,OAAA;QAAK6F,KAAK,EAAE;UAAEM,QAAQ,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAED,MAAM,CAACvD;MAAM;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE;EAET,CAAC,EACD;IACE7B,KAAK,EAAE,IAAI;IACXmB,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGpD,QAAQ,IAAKA,QAAQ,CAAC+D,cAAc,CAAC;EAChD,CAAC,EACD;IACEhC,KAAK,EAAE,KAAK;IACZmB,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGY,IAAI,IAAK,IAAI,CAACA,IAAI,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;EAC9C,CAAC,EACD;IACElC,KAAK,EAAE,IAAI;IACXmB,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAGc,KAAK,IAAK,IAAI,CAACA,KAAK,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC;EAChD,CAAC,EACD;IACElC,KAAK,EAAE,IAAI;IACXmB,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAG5D,KAAK,IAAK,IAAIA,KAAK,CAACuE,cAAc,CAAC,CAAC;EAC/C,CAAC,EACD;IACEhC,KAAK,EAAE,IAAI;IACXmB,SAAS,EAAE,KAAK;IAChBC,GAAG,EAAE,KAAK;IACVC,MAAM,EAAEA,CAAChD,GAAG,EAAEiD,MAAM,kBAClB3F,OAAA;MAAA4F,QAAA,gBACE5F,OAAA;QAAKyG,SAAS,EAAE/D,GAAG,IAAI,CAAC,GAAG,UAAU,GAAG,YAAa;QAAAkD,QAAA,GAAC,MACnD,EAAClD,GAAG,CAAC2D,cAAc,CAAC,CAAC;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACNlG,OAAA;QAAKyG,SAAS,EAAEd,MAAM,CAAChD,OAAO,IAAI,CAAC,GAAG,UAAU,GAAG,YAAa;QAAAiD,QAAA,GAC7DD,MAAM,CAAChD,OAAO,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAEgD,MAAM,CAAChD,OAAO,CAAC4D,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7D;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IACDQ,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACjE,GAAG,GAAGkE,CAAC,CAAClE;EAC9B,CAAC,EACD;IACE2B,KAAK,EAAE,IAAI;IACXmB,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAG9C,MAAM,iBACb5C,OAAA;MAAA4F,QAAA,gBACE5F,OAAA;QAAA4F,QAAA,GAAMhD,MAAM,CAAC2D,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC/BlG,OAAA,CAACZ,QAAQ;QACPyH,OAAO,EAAEjE,MAAO;QAChBkE,IAAI,EAAC,OAAO;QACZC,QAAQ,EAAE,KAAM;QAChBC,WAAW,EAAEpE,MAAM,GAAG,EAAE,GAAG,SAAS,GAAG;MAAU;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAET,CAAC,EACD;IACE7B,KAAK,EAAE,MAAM;IACbmB,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAGuB,IAAI,IAAK,GAAGA,IAAI;EAC3B,CAAC,EACD;IACE5C,KAAK,EAAE,IAAI;IACXoB,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACwB,CAAC,EAAEvB,MAAM,kBAChB3F,OAAA,CAACd,MAAM;MACL8F,IAAI,EAAC,MAAM;MACXmC,MAAM;MACNL,IAAI,EAAC,OAAO;MACZM,IAAI,eAAEpH,OAAA,CAACL,aAAa;QAAAoG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACxBmB,OAAO,EAAEA,CAAA,KAAMrD,mBAAmB,CAAC2B,MAAM,CAACxD,WAAW,CAAE;MAAAyD,QAAA,EACxD;IAED;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAEZ,CAAC,CACF;EAED,IAAIzF,OAAO,EAAE;IACX,oBACET,OAAA;MAAK6F,KAAK,EAAE;QAAEyB,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAA3B,QAAA,gBACnD5F,OAAA,CAACb,IAAI;QAAC2H,IAAI,EAAC;MAAO;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBlG,OAAA;QAAK6F,KAAK,EAAE;UAAE2B,SAAS,EAAE;QAAG,CAAE;QAAA5B,QAAA,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAEV;EAEA,oBACElG,OAAA;IAAA4F,QAAA,gBAEE5F,OAAA,CAACnB,GAAG;MAAC4I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAC5B,KAAK,EAAE;QAAE6B,YAAY,EAAE;MAAG,CAAE;MAAA9B,QAAA,gBACjD5F,OAAA,CAAClB,GAAG;QAAC6I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAhC,QAAA,eACjB5F,OAAA,CAACjB,IAAI;UAAC0H,SAAS,EAAC,aAAa;UAAAb,QAAA,gBAC3B5F,OAAA;YAAKyG,SAAS,EAAC,cAAc;YAACZ,KAAK,EAAE;cAAEO,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,GAAC,MACxD,EAAC,CAAA/E,gBAAgB,aAAhBA,gBAAgB,wBAAAV,qBAAA,GAAhBU,gBAAgB,CAAEqC,WAAW,cAAA/C,qBAAA,uBAA7BA,qBAAA,CAA+BkG,cAAc,CAAC,CAAC,KAAI,CAAC;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNlG,OAAA;YAAKyG,SAAS,EAAC,cAAc;YAAAb,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlG,OAAA,CAAClB,GAAG;QAAC6I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAhC,QAAA,eACjB5F,OAAA,CAACjB,IAAI;UAAC0H,SAAS,EAAC,aAAa;UAAAb,QAAA,gBAC3B5F,OAAA;YAAKyG,SAAS,EAAC,cAAc;YAACZ,KAAK,EAAE;cACnCO,KAAK,EAAE,CAAC,CAAAvF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuC,SAAS,KAAI,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG;YAC/D,CAAE;YAAAwC,QAAA,GAAC,MACA,EAAC,CAAA/E,gBAAgB,aAAhBA,gBAAgB,wBAAAT,sBAAA,GAAhBS,gBAAgB,CAAEuC,SAAS,cAAAhD,sBAAA,uBAA3BA,sBAAA,CAA6BiG,cAAc,CAAC,CAAC,KAAI,CAAC;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNlG,OAAA;YAAKyG,SAAS,EAAC,cAAc;YAAAb,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlG,OAAA,CAAClB,GAAG;QAAC6I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAhC,QAAA,eACjB5F,OAAA,CAACjB,IAAI;UAAC0H,SAAS,EAAC,aAAa;UAAAb,QAAA,gBAC3B5F,OAAA;YAAKyG,SAAS,EAAC,cAAc;YAACZ,KAAK,EAAE;cACnCO,KAAK,EAAE,CAAC,CAAAvF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEwC,aAAa,KAAI,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG;YACnE,CAAE;YAAAuC,QAAA,GACC,CAAC,CAAC,CAAA/E,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEwC,aAAa,KAAI,CAAC,IAAI,GAAG,EAAEkD,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7D;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNlG,OAAA;YAAKyG,SAAS,EAAC,cAAc;YAAAb,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlG,OAAA,CAAClB,GAAG;QAAC6I,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAhC,QAAA,eACjB5F,OAAA,CAACjB,IAAI;UAAC0H,SAAS,EAAC,aAAa;UAAAb,QAAA,gBAC3B5F,OAAA;YAAKyG,SAAS,EAAC,cAAc;YAACZ,KAAK,EAAE;cAAEO,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,GAAC,MACxD,EAAC,CAAA/E,gBAAgB,aAAhBA,gBAAgB,wBAAAR,qBAAA,GAAhBQ,gBAAgB,CAAEyC,YAAY,cAAAjD,qBAAA,uBAA9BA,qBAAA,CAAgCgG,cAAc,CAAC,CAAC,KAAI,CAAC;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNlG,OAAA;YAAKyG,SAAS,EAAC,cAAc;YAAAb,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA,CAACnB,GAAG;MAAC4I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAC5B,KAAK,EAAE;QAAE6B,YAAY,EAAE;MAAG,CAAE;MAAA9B,QAAA,gBACjD5F,OAAA,CAAClB,GAAG;QAAC6I,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAjC,QAAA,eAClB5F,OAAA,CAACjB,IAAI;UAACsF,KAAK,EAAC,0BAAM;UAACoC,SAAS,EAAC,gBAAgB;UAAAb,QAAA,EAC1CjF,SAAS,CAACyD,MAAM,GAAG,CAAC,gBACnBpE,OAAA,CAACH,YAAY;YACXiI,MAAM,EAAE3D,6BAA6B,CAAC,CAAE;YACxC0B,KAAK,EAAE;cAAEkC,MAAM,EAAE;YAAQ;UAAE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,gBAEFlG,OAAA;YAAK6F,KAAK,EAAE;cAAEyB,SAAS,EAAE,QAAQ;cAAEC,OAAO,EAAE,MAAM;cAAEnB,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,EAAC;UAErE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlG,OAAA,CAAClB,GAAG;QAAC6I,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAjC,QAAA,eAClB5F,OAAA,CAACjB,IAAI;UAACsF,KAAK,EAAC,0BAAM;UAACoC,SAAS,EAAC,gBAAgB;UAAAb,QAAA,gBAC3C5F,OAAA,CAACnB,GAAG;YAAC4I,MAAM,EAAE,EAAG;YAAA7B,QAAA,gBACd5F,OAAA,CAAClB,GAAG;cAACkJ,IAAI,EAAE,EAAG;cAAApC,QAAA,eACZ5F,OAAA;gBAAK6F,KAAK,EAAE;kBAAEyB,SAAS,EAAE,QAAQ;kBAAEI,YAAY,EAAE;gBAAG,CAAE;gBAAA9B,QAAA,gBACpD5F,OAAA;kBAAK6F,KAAK,EAAE;oBAAEM,QAAQ,EAAE,MAAM;oBAAEL,UAAU,EAAE;kBAAO,CAAE;kBAAAF,QAAA,EAClD,CAAA/E,gBAAgB,aAAhBA,gBAAgB,wBAAAP,qBAAA,GAAhBO,gBAAgB,CAAE4C,YAAY,cAAAnD,qBAAA,uBAA9BA,qBAAA,CAAgCiG,OAAO,CAAC,CAAC,CAAC,KAAI;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACNlG,OAAA;kBAAK6F,KAAK,EAAE;oBAAEO,KAAK,EAAE;kBAAO,CAAE;kBAAAR,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlG,OAAA,CAAClB,GAAG;cAACkJ,IAAI,EAAE,EAAG;cAAApC,QAAA,eACZ5F,OAAA;gBAAK6F,KAAK,EAAE;kBAAEyB,SAAS,EAAE,QAAQ;kBAAEI,YAAY,EAAE;gBAAG,CAAE;gBAAA9B,QAAA,gBACpD5F,OAAA;kBAAK6F,KAAK,EAAE;oBAAEM,QAAQ,EAAE,MAAM;oBAAEL,UAAU,EAAE;kBAAO,CAAE;kBAAAF,QAAA,GAClD,CAAA/E,gBAAgB,aAAhBA,gBAAgB,wBAAAN,qBAAA,GAAhBM,gBAAgB,CAAE6C,QAAQ,cAAAnD,qBAAA,uBAA1BA,qBAAA,CAA4BgG,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,GAC/C;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNlG,OAAA;kBAAK6F,KAAK,EAAE;oBAAEO,KAAK,EAAE;kBAAO,CAAE;kBAAAR,QAAA,EAAC;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlG,OAAA;YAAK6F,KAAK,EAAE;cAAE2B,SAAS,EAAE;YAAG,CAAE;YAAA5B,QAAA,gBAC5B5F,OAAA;cAAK6F,KAAK,EAAE;gBAAE6B,YAAY,EAAE;cAAE,CAAE;cAAA9B,QAAA,GAAC,4BACzB,EAAC,CAAA/E,gBAAgB,aAAhBA,gBAAgB,wBAAAL,qBAAA,GAAhBK,gBAAgB,CAAE2C,YAAY,cAAAhD,qBAAA,uBAA9BA,qBAAA,CAAgC+F,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,GACzD;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNlG,OAAA,CAACZ,QAAQ;cACPyH,OAAO,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAArH,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2C,YAAY,KAAI,CAAC,CAAE;cACvD9B,MAAM,EAAC,WAAW;cAClBsF,WAAW,EAAC;YAAS;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA,CAACnB,GAAG;MAACgH,KAAK,EAAE;QAAE6B,YAAY,EAAE;MAAG,CAAE;MAAA9B,QAAA,eAC/B5F,OAAA,CAAClB,GAAG;QAAA8G,QAAA,eACF5F,OAAA,CAACd,MAAM;UACL8F,IAAI,EAAC,SAAS;UACdoC,IAAI,eAAEpH,OAAA,CAACN,YAAY;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBmB,OAAO,EAAEA,CAAA,KAAMrG,qBAAqB,CAAC,IAAI,CAAE;UAAA4E,QAAA,EAC5C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA,CAACjB,IAAI;MAACsF,KAAK,EAAC,0BAAM;MAACoC,SAAS,EAAC,gBAAgB;MAAAb,QAAA,eAC3C5F,OAAA,CAAChB,KAAK;QACJuG,OAAO,EAAEA,OAAQ;QACjB4C,UAAU,EAAExH,SAAU;QACtByH,MAAM,EAAC,aAAa;QACpBC,UAAU,EAAE,KAAM;QAClBC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK;MAAE;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPlG,OAAA,CAACX,KAAK;MACJgF,KAAK,EAAC,0BAAM;MACZmE,IAAI,EAAEzH,kBAAmB;MACzB0H,QAAQ,EAAEA,CAAA,KAAMzH,qBAAqB,CAAC,KAAK,CAAE;MAC7C0H,IAAI,EAAEA,CAAA,KAAMzH,IAAI,CAAC0H,MAAM,CAAC,CAAE;MAC1BC,MAAM,EAAC,0BAAM;MACbC,UAAU,EAAC,cAAI;MAAAjD,QAAA,eAEf5F,OAAA,CAACV,IAAI;QACH2B,IAAI,EAAEA,IAAK;QACX6H,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEnF,iBAAkB;QAAAgC,QAAA,gBAE5B5F,OAAA,CAACV,IAAI,CAAC0J,IAAI;UACR3G,IAAI,EAAC,QAAQ;UACb4G,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAxD,QAAA,eAEhD5F,OAAA,CAACT,KAAK;YAAC8J,WAAW,EAAC;UAAW;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACZlG,OAAA,CAACV,IAAI,CAAC0J,IAAI;UACR3G,IAAI,EAAC,UAAU;UACf4G,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAxD,QAAA,eAEhD5F,OAAA,CAACR,WAAW;YACVqG,KAAK,EAAE;cAAEyD,KAAK,EAAE;YAAO,CAAE;YACzBD,WAAW,EAAC,cAAI;YAChBE,GAAG,EAAE,GAAI;YACTC,IAAI,EAAE;UAAI;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eACZlG,OAAA,CAACV,IAAI,CAAC0J,IAAI;UACR3G,IAAI,EAAC,OAAO;UACZ4G,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEC,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAxD,QAAA,eAEhD5F,OAAA,CAACR,WAAW;YACVqG,KAAK,EAAE;cAAEyD,KAAK,EAAE;YAAO,CAAE;YACzBD,WAAW,EAAC,QAAG;YACfE,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,IAAK;YACXC,SAAS,EAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAChG,EAAA,CAjaID,SAAS;EAAA,QAKEX,IAAI,CAAC4B,OAAO;AAAA;AAAAwI,EAAA,GALvBzJ,SAAS;AAmaf,eAAeA,SAAS;AAAC,IAAAyJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}