{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Statistic, Alert, Spin, Tag, Progress } from 'antd';\nimport { ArrowUpOutlined, ArrowDownOutlined, FireOutlined, DollarOutlined } from '@ant-design/icons';\nimport ReactECharts from 'echarts-for-react';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _sentimentData$market;\n  const [loading, setLoading] = useState(true);\n  const [marketData, setMarketData] = useState(null);\n  const [sentimentData, setSentimentData] = useState(null);\n  const [portfolioData, setPortfolioData] = useState(null);\n  const [systemStatus, setSystemStatus] = useState('online');\n  useEffect(() => {\n    loadDashboardData();\n    // 设置定时刷新\n    const interval = setInterval(loadDashboardData, 30000); // 30秒刷新一次\n    return () => clearInterval(interval);\n  }, []);\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n\n      // 并行加载数据\n      const [market, sentiment, portfolio] = await Promise.allSettled([apiService.getRealtimeMarketData(), apiService.getCurrentSentiment(), apiService.getPortfolioMetrics()]);\n      if (market.status === 'fulfilled') {\n        var _market$value;\n        console.log('Market data received:', market.value);\n        setMarketData(((_market$value = market.value) === null || _market$value === void 0 ? void 0 : _market$value.data) || market.value);\n      }\n      if (sentiment.status === 'fulfilled') {\n        var _sentiment$value;\n        console.log('Sentiment data received:', sentiment.value);\n        setSentimentData(((_sentiment$value = sentiment.value) === null || _sentiment$value === void 0 ? void 0 : _sentiment$value.data) || sentiment.value);\n      }\n      if (portfolio.status === 'fulfilled') {\n        var _portfolio$value;\n        console.log('Portfolio data received:', portfolio.value);\n        setPortfolioData(((_portfolio$value = portfolio.value) === null || _portfolio$value === void 0 ? void 0 : _portfolio$value.data) || portfolio.value);\n      }\n      setSystemStatus('online');\n    } catch (error) {\n      console.error('加载仪表板数据失败:', error);\n      setSystemStatus('error');\n      // 使用模拟数据\n      setMarketData({\n        limit_up_count: 46,\n        limit_down_count: 13,\n        max_continuous_boards: 4,\n        timestamp: new Date().toISOString()\n      });\n      setSentimentData({\n        sentiment_score: 75.5,\n        market_temperature: 68.2,\n        trend_direction: 'bullish',\n        volatility_index: 0.234,\n        sentiment_phase: 'high_tide'\n      });\n      setPortfolioData({\n        total_value: 1000000,\n        total_pnl: 25000,\n        total_pnl_pct: 2.5,\n        position_count: 8,\n        cash_balance: 200000\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 情绪温度计配置\n  const getSentimentGaugeOption = () => {\n    const score = (sentimentData === null || sentimentData === void 0 ? void 0 : sentimentData.sentiment_score) || 0;\n    return {\n      series: [{\n        name: '市场情绪',\n        type: 'gauge',\n        startAngle: 180,\n        endAngle: 0,\n        min: 0,\n        max: 100,\n        splitNumber: 5,\n        itemStyle: {\n          color: score > 70 ? '#ff4d4f' : score > 40 ? '#faad14' : '#52c41a'\n        },\n        progress: {\n          show: true,\n          width: 30\n        },\n        pointer: {\n          show: false\n        },\n        axisLine: {\n          lineStyle: {\n            width: 30\n          }\n        },\n        axisTick: {\n          distance: -45,\n          splitNumber: 5,\n          lineStyle: {\n            width: 2,\n            color: '#999'\n          }\n        },\n        splitLine: {\n          distance: -52,\n          length: 14,\n          lineStyle: {\n            width: 3,\n            color: '#999'\n          }\n        },\n        axisLabel: {\n          distance: -20,\n          color: '#999',\n          fontSize: 20\n        },\n        detail: {\n          valueAnimation: true,\n          width: '60%',\n          lineHeight: 40,\n          borderRadius: 8,\n          offsetCenter: [0, '-15%'],\n          fontSize: 30,\n          fontWeight: 'bolder',\n          formatter: '{value}',\n          color: 'inherit'\n        },\n        data: [{\n          value: score,\n          name: '情绪指数'\n        }]\n      }]\n    };\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u6570\\u636E...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [systemStatus === 'error' && /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u6570\\u636E\\u8FDE\\u63A5\\u5F02\\u5E38\",\n      description: \"\\u5F53\\u524D\\u663E\\u793A\\u6A21\\u62DF\\u6570\\u636E\\uFF0C\\u8BF7\\u68C0\\u67E5\\u540E\\u7AEF\\u670D\\u52A1\\u72B6\\u6001\",\n      type: \"warning\",\n      showIcon: true,\n      style: {\n        marginBottom: 24\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6DA8\\u505C\\u80A1\\u6570\",\n            value: (marketData === null || marketData === void 0 ? void 0 : marketData.limit_up_count) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(ArrowUpOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#ff4d4f'\n            },\n            suffix: \"\\u53EA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8DCC\\u505C\\u80A1\\u6570\",\n            value: (marketData === null || marketData === void 0 ? void 0 : marketData.limit_down_count) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(ArrowDownOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            },\n            suffix: \"\\u53EA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6700\\u9AD8\\u8FDE\\u677F\",\n            value: (marketData === null || marketData === void 0 ? void 0 : marketData.max_continuous_boards) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(FireOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#faad14'\n            },\n            suffix: \"\\u5929\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"metric-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u76C8\\u4E8F\",\n            value: (portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.total_pnl) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: ((portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.total_pnl) || 0) >= 0 ? '#ff4d4f' : '#52c41a'\n            },\n            suffix: \"\\u5143\",\n            precision: 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5E02\\u573A\\u60C5\\u7EEA\\u6E29\\u5EA6\\u8BA1\",\n          className: \"dashboard-card\",\n          children: [/*#__PURE__*/_jsxDEV(ReactECharts, {\n            option: getSentimentGaugeOption(),\n            style: {\n              height: '300px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              marginTop: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tag, {\n              color: (sentimentData === null || sentimentData === void 0 ? void 0 : sentimentData.sentiment_phase) === 'high_tide' ? 'red' : (sentimentData === null || sentimentData === void 0 ? void 0 : sentimentData.sentiment_phase) === 'warming' ? 'orange' : (sentimentData === null || sentimentData === void 0 ? void 0 : sentimentData.sentiment_phase) === 'freezing' ? 'blue' : 'default',\n              children: (sentimentData === null || sentimentData === void 0 ? void 0 : sentimentData.sentiment_phase) === 'high_tide' ? '高潮期' : (sentimentData === null || sentimentData === void 0 ? void 0 : sentimentData.sentiment_phase) === 'warming' ? '回暖期' : (sentimentData === null || sentimentData === void 0 ? void 0 : sentimentData.sentiment_phase) === 'freezing' ? '冰点期' : '未知'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8,\n                color: '#666'\n              },\n              children: [\"\\u5E02\\u573A\\u6E29\\u5EA6: \", (sentimentData === null || sentimentData === void 0 ? void 0 : (_sentimentData$market = sentimentData.market_temperature) === null || _sentimentData$market === void 0 ? void 0 : _sentimentData$market.toFixed(1)) || 0, \"\\xB0C\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6295\\u8D44\\u7EC4\\u5408\\u6982\\u89C8\",\n          className: \"dashboard-card\",\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u603B\\u8D44\\u4EA7\",\n                value: (portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.total_value) || 0,\n                suffix: \"\\u5143\",\n                precision: 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u6301\\u4ED3\\u6570\\u91CF\",\n                value: (portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.position_count) || 0,\n                suffix: \"\\u53EA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 24\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8\n              },\n              children: [\"\\u603B\\u6536\\u76CA\\u7387: \", (((portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.total_pnl_pct) || 0) * 100).toFixed(2), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              percent: Math.abs(((portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.total_pnl_pct) || 0) * 100),\n              status: ((portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.total_pnl_pct) || 0) >= 0 ? 'success' : 'exception',\n              strokeColor: ((portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.total_pnl_pct) || 0) >= 0 ? '#ff4d4f' : '#52c41a'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16,\n              color: '#666'\n            },\n            children: [\"\\u73B0\\u91D1\\u4F59\\u989D: \", ((portfolioData === null || portfolioData === void 0 ? void 0 : portfolioData.cash_balance) || 0).toLocaleString(), \"\\u5143\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginTop: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7CFB\\u7EDF\\u72B6\\u6001\",\n          className: \"dashboard-card\",\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `status-indicator status-${systemStatus === 'online' ? 'online' : 'offline'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), \"\\u6570\\u636E\\u8FDE\\u63A5: \", systemStatus === 'online' ? '正常' : '异常']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"status-indicator status-online\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this), \"\\u7B56\\u7565\\u5F15\\u64CE: \\u8FD0\\u884C\\u4E2D\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"status-indicator status-online\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this), \"\\u98CE\\u63A7\\u7CFB\\u7EDF: \\u6B63\\u5E38\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16,\n              textAlign: 'center',\n              color: '#666'\n            },\n            children: [\"\\u6700\\u540E\\u66F4\\u65B0: \", new Date().toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"meYG2JQMeXNqJVPIEtHfIPi+Ch4=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Statistic", "<PERSON><PERSON>", "Spin", "Tag", "Progress", "ArrowUpOutlined", "ArrowDownOutlined", "FireOutlined", "DollarOutlined", "ReactECharts", "apiService", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "_sentimentData$market", "loading", "setLoading", "marketData", "setMarketData", "sentimentData", "setSentimentData", "portfolioData", "setPortfolioData", "systemStatus", "setSystemStatus", "loadDashboardData", "interval", "setInterval", "clearInterval", "market", "sentiment", "portfolio", "Promise", "allSettled", "getRealtimeMarketData", "getCurrentSentiment", "getPortfolioMetrics", "status", "_market$value", "console", "log", "value", "data", "_sentiment$value", "_portfolio$value", "error", "limit_up_count", "limit_down_count", "max_continuous_boards", "timestamp", "Date", "toISOString", "sentiment_score", "market_temperature", "trend_direction", "volatility_index", "sentiment_phase", "total_value", "total_pnl", "total_pnl_pct", "position_count", "cash_balance", "getSentimentGaugeOption", "score", "series", "name", "type", "startAngle", "endAngle", "min", "max", "splitNumber", "itemStyle", "color", "progress", "show", "width", "pointer", "axisLine", "lineStyle", "axisTick", "distance", "splitLine", "length", "axisLabel", "fontSize", "detail", "valueAnimation", "lineHeight", "borderRadius", "offsetCenter", "fontWeight", "formatter", "style", "textAlign", "padding", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "message", "description", "showIcon", "marginBottom", "gutter", "xs", "sm", "md", "className", "title", "prefix", "valueStyle", "suffix", "precision", "lg", "option", "height", "toFixed", "span", "percent", "Math", "abs", "strokeColor", "toLocaleString", "toLocaleTimeString", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Statistic, Alert, Spin, Tag, Progress } from 'antd';\nimport {\n  ArrowUpOutlined,\n  ArrowDownOutlined,\n  FireOutlined,\n  DollarOutlined\n} from '@ant-design/icons';\nimport ReactECharts from 'echarts-for-react';\nimport apiService from '../services/api';\n\nconst Dashboard = () => {\n  const [loading, setLoading] = useState(true);\n  const [marketData, setMarketData] = useState(null);\n  const [sentimentData, setSentimentData] = useState(null);\n  const [portfolioData, setPortfolioData] = useState(null);\n  const [systemStatus, setSystemStatus] = useState('online');\n\n  useEffect(() => {\n    loadDashboardData();\n    // 设置定时刷新\n    const interval = setInterval(loadDashboardData, 30000); // 30秒刷新一次\n    return () => clearInterval(interval);\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      \n      // 并行加载数据\n      const [market, sentiment, portfolio] = await Promise.allSettled([\n        apiService.getRealtimeMarketData(),\n        apiService.getCurrentSentiment(),\n        apiService.getPortfolioMetrics()\n      ]);\n\n      if (market.status === 'fulfilled') {\n        console.log('Market data received:', market.value);\n        setMarketData(market.value?.data || market.value);\n      }\n\n      if (sentiment.status === 'fulfilled') {\n        console.log('Sentiment data received:', sentiment.value);\n        setSentimentData(sentiment.value?.data || sentiment.value);\n      }\n\n      if (portfolio.status === 'fulfilled') {\n        console.log('Portfolio data received:', portfolio.value);\n        setPortfolioData(portfolio.value?.data || portfolio.value);\n      }\n\n      setSystemStatus('online');\n    } catch (error) {\n      console.error('加载仪表板数据失败:', error);\n      setSystemStatus('error');\n      // 使用模拟数据\n      setMarketData({\n        limit_up_count: 46,\n        limit_down_count: 13,\n        max_continuous_boards: 4,\n        timestamp: new Date().toISOString()\n      });\n      setSentimentData({\n        sentiment_score: 75.5,\n        market_temperature: 68.2,\n        trend_direction: 'bullish',\n        volatility_index: 0.234,\n        sentiment_phase: 'high_tide'\n      });\n      setPortfolioData({\n        total_value: 1000000,\n        total_pnl: 25000,\n        total_pnl_pct: 2.5,\n        position_count: 8,\n        cash_balance: 200000\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 情绪温度计配置\n  const getSentimentGaugeOption = () => {\n    const score = sentimentData?.sentiment_score || 0;\n    return {\n      series: [\n        {\n          name: '市场情绪',\n          type: 'gauge',\n          startAngle: 180,\n          endAngle: 0,\n          min: 0,\n          max: 100,\n          splitNumber: 5,\n          itemStyle: {\n            color: score > 70 ? '#ff4d4f' : score > 40 ? '#faad14' : '#52c41a'\n          },\n          progress: {\n            show: true,\n            width: 30\n          },\n          pointer: {\n            show: false\n          },\n          axisLine: {\n            lineStyle: {\n              width: 30\n            }\n          },\n          axisTick: {\n            distance: -45,\n            splitNumber: 5,\n            lineStyle: {\n              width: 2,\n              color: '#999'\n            }\n          },\n          splitLine: {\n            distance: -52,\n            length: 14,\n            lineStyle: {\n              width: 3,\n              color: '#999'\n            }\n          },\n          axisLabel: {\n            distance: -20,\n            color: '#999',\n            fontSize: 20\n          },\n          detail: {\n            valueAnimation: true,\n            width: '60%',\n            lineHeight: 40,\n            borderRadius: 8,\n            offsetCenter: [0, '-15%'],\n            fontSize: 30,\n            fontWeight: 'bolder',\n            formatter: '{value}',\n            color: 'inherit'\n          },\n          data: [\n            {\n              value: score,\n              name: '情绪指数'\n            }\n          ]\n        }\n      ]\n    };\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>正在加载数据...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* 系统状态提示 */}\n      {systemStatus === 'error' && (\n        <Alert\n          message=\"数据连接异常\"\n          description=\"当前显示模拟数据，请检查后端服务状态\"\n          type=\"warning\"\n          showIcon\n          style={{ marginBottom: 24 }}\n        />\n      )}\n\n      {/* 核心指标卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card className=\"metric-card\">\n            <Statistic\n              title=\"涨停股数\"\n              value={marketData?.limit_up_count || 0}\n              prefix={<ArrowUpOutlined />}\n              valueStyle={{ color: '#ff4d4f' }}\n              suffix=\"只\"\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card className=\"metric-card\">\n            <Statistic\n              title=\"跌停股数\"\n              value={marketData?.limit_down_count || 0}\n              prefix={<ArrowDownOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n              suffix=\"只\"\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card className=\"metric-card\">\n            <Statistic\n              title=\"最高连板\"\n              value={marketData?.max_continuous_boards || 0}\n              prefix={<FireOutlined />}\n              valueStyle={{ color: '#faad14' }}\n              suffix=\"天\"\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card className=\"metric-card\">\n            <Statistic\n              title=\"总盈亏\"\n              value={portfolioData?.total_pnl || 0}\n              prefix={<DollarOutlined />}\n              valueStyle={{ \n                color: (portfolioData?.total_pnl || 0) >= 0 ? '#ff4d4f' : '#52c41a' \n              }}\n              suffix=\"元\"\n              precision={0}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 主要内容区域 */}\n      <Row gutter={[16, 16]}>\n        {/* 市场情绪 */}\n        <Col xs={24} lg={12}>\n          <Card title=\"市场情绪温度计\" className=\"dashboard-card\">\n            <ReactECharts \n              option={getSentimentGaugeOption()} \n              style={{ height: '300px' }}\n            />\n            <div style={{ textAlign: 'center', marginTop: 16 }}>\n              <Tag color={\n                sentimentData?.sentiment_phase === 'high_tide' ? 'red' :\n                sentimentData?.sentiment_phase === 'warming' ? 'orange' :\n                sentimentData?.sentiment_phase === 'freezing' ? 'blue' : 'default'\n              }>\n                {sentimentData?.sentiment_phase === 'high_tide' ? '高潮期' :\n                 sentimentData?.sentiment_phase === 'warming' ? '回暖期' :\n                 sentimentData?.sentiment_phase === 'freezing' ? '冰点期' : '未知'}\n              </Tag>\n              <div style={{ marginTop: 8, color: '#666' }}>\n                市场温度: {sentimentData?.market_temperature?.toFixed(1) || 0}°C\n              </div>\n            </div>\n          </Card>\n        </Col>\n\n        {/* 投资组合概览 */}\n        <Col xs={24} lg={12}>\n          <Card title=\"投资组合概览\" className=\"dashboard-card\">\n            <Row gutter={16}>\n              <Col span={12}>\n                <Statistic\n                  title=\"总资产\"\n                  value={portfolioData?.total_value || 0}\n                  suffix=\"元\"\n                  precision={0}\n                />\n              </Col>\n              <Col span={12}>\n                <Statistic\n                  title=\"持仓数量\"\n                  value={portfolioData?.position_count || 0}\n                  suffix=\"只\"\n                />\n              </Col>\n            </Row>\n            <div style={{ marginTop: 24 }}>\n              <div style={{ marginBottom: 8 }}>\n                总收益率: {((portfolioData?.total_pnl_pct || 0) * 100).toFixed(2)}%\n              </div>\n              <Progress\n                percent={Math.abs((portfolioData?.total_pnl_pct || 0) * 100)}\n                status={(portfolioData?.total_pnl_pct || 0) >= 0 ? 'success' : 'exception'}\n                strokeColor={(portfolioData?.total_pnl_pct || 0) >= 0 ? '#ff4d4f' : '#52c41a'}\n              />\n            </div>\n            <div style={{ marginTop: 16, color: '#666' }}>\n              现金余额: {(portfolioData?.cash_balance || 0).toLocaleString()}元\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 快速操作和状态 */}\n      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>\n        <Col span={24}>\n          <Card title=\"系统状态\" className=\"dashboard-card\">\n            <Row gutter={16}>\n              <Col xs={24} sm={8}>\n                <div style={{ textAlign: 'center' }}>\n                  <div className={`status-indicator status-${systemStatus === 'online' ? 'online' : 'offline'}`}></div>\n                  数据连接: {systemStatus === 'online' ? '正常' : '异常'}\n                </div>\n              </Col>\n              <Col xs={24} sm={8}>\n                <div style={{ textAlign: 'center' }}>\n                  <div className=\"status-indicator status-online\"></div>\n                  策略引擎: 运行中\n                </div>\n              </Col>\n              <Col xs={24} sm={8}>\n                <div style={{ textAlign: 'center' }}>\n                  <div className=\"status-indicator status-online\"></div>\n                  风控系统: 正常\n                </div>\n              </Col>\n            </Row>\n            <div style={{ marginTop: 16, textAlign: 'center', color: '#666' }}>\n              最后更新: {new Date().toLocaleTimeString()}\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,QAAQ,QAAQ,MAAM;AAC5E,SACEC,eAAe,EACfC,iBAAiB,EACjBC,YAAY,EACZC,cAAc,QACT,mBAAmB;AAC1B,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACtB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,QAAQ,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACd8B,iBAAiB,CAAC,CAAC;IACnB;IACA,MAAMC,QAAQ,GAAGC,WAAW,CAACF,iBAAiB,EAAE,KAAK,CAAC,CAAC,CAAC;IACxD,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACa,MAAM,EAAEC,SAAS,EAAEC,SAAS,CAAC,GAAG,MAAMC,OAAO,CAACC,UAAU,CAAC,CAC9DxB,UAAU,CAACyB,qBAAqB,CAAC,CAAC,EAClCzB,UAAU,CAAC0B,mBAAmB,CAAC,CAAC,EAChC1B,UAAU,CAAC2B,mBAAmB,CAAC,CAAC,CACjC,CAAC;MAEF,IAAIP,MAAM,CAACQ,MAAM,KAAK,WAAW,EAAE;QAAA,IAAAC,aAAA;QACjCC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEX,MAAM,CAACY,KAAK,CAAC;QAClDvB,aAAa,CAAC,EAAAoB,aAAA,GAAAT,MAAM,CAACY,KAAK,cAAAH,aAAA,uBAAZA,aAAA,CAAcI,IAAI,KAAIb,MAAM,CAACY,KAAK,CAAC;MACnD;MAEA,IAAIX,SAAS,CAACO,MAAM,KAAK,WAAW,EAAE;QAAA,IAAAM,gBAAA;QACpCJ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEV,SAAS,CAACW,KAAK,CAAC;QACxDrB,gBAAgB,CAAC,EAAAuB,gBAAA,GAAAb,SAAS,CAACW,KAAK,cAAAE,gBAAA,uBAAfA,gBAAA,CAAiBD,IAAI,KAAIZ,SAAS,CAACW,KAAK,CAAC;MAC5D;MAEA,IAAIV,SAAS,CAACM,MAAM,KAAK,WAAW,EAAE;QAAA,IAAAO,gBAAA;QACpCL,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAET,SAAS,CAACU,KAAK,CAAC;QACxDnB,gBAAgB,CAAC,EAAAsB,gBAAA,GAAAb,SAAS,CAACU,KAAK,cAAAG,gBAAA,uBAAfA,gBAAA,CAAiBF,IAAI,KAAIX,SAAS,CAACU,KAAK,CAAC;MAC5D;MAEAjB,eAAe,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCrB,eAAe,CAAC,OAAO,CAAC;MACxB;MACAN,aAAa,CAAC;QACZ4B,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE,EAAE;QACpBC,qBAAqB,EAAE,CAAC;QACxBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CAAC;MACF/B,gBAAgB,CAAC;QACfgC,eAAe,EAAE,IAAI;QACrBC,kBAAkB,EAAE,IAAI;QACxBC,eAAe,EAAE,SAAS;QAC1BC,gBAAgB,EAAE,KAAK;QACvBC,eAAe,EAAE;MACnB,CAAC,CAAC;MACFlC,gBAAgB,CAAC;QACfmC,WAAW,EAAE,OAAO;QACpBC,SAAS,EAAE,KAAK;QAChBC,aAAa,EAAE,GAAG;QAClBC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8C,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,KAAK,GAAG,CAAA5C,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiC,eAAe,KAAI,CAAC;IACjD,OAAO;MACLY,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,GAAG;QACfC,QAAQ,EAAE,CAAC;QACXC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,GAAG;QACRC,WAAW,EAAE,CAAC;QACdC,SAAS,EAAE;UACTC,KAAK,EAAEV,KAAK,GAAG,EAAE,GAAG,SAAS,GAAGA,KAAK,GAAG,EAAE,GAAG,SAAS,GAAG;QAC3D,CAAC;QACDW,QAAQ,EAAE;UACRC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE;QACT,CAAC;QACDC,OAAO,EAAE;UACPF,IAAI,EAAE;QACR,CAAC;QACDG,QAAQ,EAAE;UACRC,SAAS,EAAE;YACTH,KAAK,EAAE;UACT;QACF,CAAC;QACDI,QAAQ,EAAE;UACRC,QAAQ,EAAE,CAAC,EAAE;UACbV,WAAW,EAAE,CAAC;UACdQ,SAAS,EAAE;YACTH,KAAK,EAAE,CAAC;YACRH,KAAK,EAAE;UACT;QACF,CAAC;QACDS,SAAS,EAAE;UACTD,QAAQ,EAAE,CAAC,EAAE;UACbE,MAAM,EAAE,EAAE;UACVJ,SAAS,EAAE;YACTH,KAAK,EAAE,CAAC;YACRH,KAAK,EAAE;UACT;QACF,CAAC;QACDW,SAAS,EAAE;UACTH,QAAQ,EAAE,CAAC,EAAE;UACbR,KAAK,EAAE,MAAM;UACbY,QAAQ,EAAE;QACZ,CAAC;QACDC,MAAM,EAAE;UACNC,cAAc,EAAE,IAAI;UACpBX,KAAK,EAAE,KAAK;UACZY,UAAU,EAAE,EAAE;UACdC,YAAY,EAAE,CAAC;UACfC,YAAY,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;UACzBL,QAAQ,EAAE,EAAE;UACZM,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE,SAAS;UACpBnB,KAAK,EAAE;QACT,CAAC;QACD/B,IAAI,EAAE,CACJ;UACED,KAAK,EAAEsB,KAAK;UACZE,IAAI,EAAE;QACR,CAAC;MAEL,CAAC;IAEL,CAAC;EACH,CAAC;EAED,IAAIlD,OAAO,EAAE;IACX,oBACEJ,OAAA;MAAKkF,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,gBACnDrF,OAAA,CAACV,IAAI;QAACgG,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB1F,OAAA;QAAKkF,KAAK,EAAE;UAAES,SAAS,EAAE;QAAG,CAAE;QAAAN,QAAA,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC;EAEV;EAEA,oBACE1F,OAAA;IAAAqF,QAAA,GAEGzE,YAAY,KAAK,OAAO,iBACvBZ,OAAA,CAACX,KAAK;MACJuG,OAAO,EAAC,sCAAQ;MAChBC,WAAW,EAAC,8GAAoB;MAChCtC,IAAI,EAAC,SAAS;MACduC,QAAQ;MACRZ,KAAK,EAAE;QAAEa,YAAY,EAAE;MAAG;IAAE;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CACF,eAGD1F,OAAA,CAACf,GAAG;MAAC+G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACd,KAAK,EAAE;QAAEa,YAAY,EAAE;MAAG,CAAE;MAAAV,QAAA,gBACjDrF,OAAA,CAACd,GAAG;QAAC+G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAd,QAAA,eACzBrF,OAAA,CAACb,IAAI;UAACiH,SAAS,EAAC,aAAa;UAAAf,QAAA,eAC3BrF,OAAA,CAACZ,SAAS;YACRiH,KAAK,EAAC,0BAAM;YACZvE,KAAK,EAAE,CAAAxB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE6B,cAAc,KAAI,CAAE;YACvCmE,MAAM,eAAEtG,OAAA,CAACP,eAAe;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5Ba,UAAU,EAAE;cAAEzC,KAAK,EAAE;YAAU,CAAE;YACjC0C,MAAM,EAAC;UAAG;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1F,OAAA,CAACd,GAAG;QAAC+G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAd,QAAA,eACzBrF,OAAA,CAACb,IAAI;UAACiH,SAAS,EAAC,aAAa;UAAAf,QAAA,eAC3BrF,OAAA,CAACZ,SAAS;YACRiH,KAAK,EAAC,0BAAM;YACZvE,KAAK,EAAE,CAAAxB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE8B,gBAAgB,KAAI,CAAE;YACzCkE,MAAM,eAAEtG,OAAA,CAACN,iBAAiB;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9Ba,UAAU,EAAE;cAAEzC,KAAK,EAAE;YAAU,CAAE;YACjC0C,MAAM,EAAC;UAAG;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1F,OAAA,CAACd,GAAG;QAAC+G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAd,QAAA,eACzBrF,OAAA,CAACb,IAAI;UAACiH,SAAS,EAAC,aAAa;UAAAf,QAAA,eAC3BrF,OAAA,CAACZ,SAAS;YACRiH,KAAK,EAAC,0BAAM;YACZvE,KAAK,EAAE,CAAAxB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE+B,qBAAqB,KAAI,CAAE;YAC9CiE,MAAM,eAAEtG,OAAA,CAACL,YAAY;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBa,UAAU,EAAE;cAAEzC,KAAK,EAAE;YAAU,CAAE;YACjC0C,MAAM,EAAC;UAAG;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1F,OAAA,CAACd,GAAG;QAAC+G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAd,QAAA,eACzBrF,OAAA,CAACb,IAAI;UAACiH,SAAS,EAAC,aAAa;UAAAf,QAAA,eAC3BrF,OAAA,CAACZ,SAAS;YACRiH,KAAK,EAAC,oBAAK;YACXvE,KAAK,EAAE,CAAApB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqC,SAAS,KAAI,CAAE;YACrCuD,MAAM,eAAEtG,OAAA,CAACJ,cAAc;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3Ba,UAAU,EAAE;cACVzC,KAAK,EAAE,CAAC,CAAApD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqC,SAAS,KAAI,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG;YAC5D,CAAE;YACFyD,MAAM,EAAC,QAAG;YACVC,SAAS,EAAE;UAAE;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1F,OAAA,CAACf,GAAG;MAAC+G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAX,QAAA,gBAEpBrF,OAAA,CAACd,GAAG;QAAC+G,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,EAAG;QAAArB,QAAA,eAClBrF,OAAA,CAACb,IAAI;UAACkH,KAAK,EAAC,4CAAS;UAACD,SAAS,EAAC,gBAAgB;UAAAf,QAAA,gBAC9CrF,OAAA,CAACH,YAAY;YACX8G,MAAM,EAAExD,uBAAuB,CAAC,CAAE;YAClC+B,KAAK,EAAE;cAAE0B,MAAM,EAAE;YAAQ;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACF1F,OAAA;YAAKkF,KAAK,EAAE;cAAEC,SAAS,EAAE,QAAQ;cAAEQ,SAAS,EAAE;YAAG,CAAE;YAAAN,QAAA,gBACjDrF,OAAA,CAACT,GAAG;cAACuE,KAAK,EACR,CAAAtD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqC,eAAe,MAAK,WAAW,GAAG,KAAK,GACtD,CAAArC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqC,eAAe,MAAK,SAAS,GAAG,QAAQ,GACvD,CAAArC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqC,eAAe,MAAK,UAAU,GAAG,MAAM,GAAG,SAC1D;cAAAwC,QAAA,EACE,CAAA7E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqC,eAAe,MAAK,WAAW,GAAG,KAAK,GACtD,CAAArC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqC,eAAe,MAAK,SAAS,GAAG,KAAK,GACpD,CAAArC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqC,eAAe,MAAK,UAAU,GAAG,KAAK,GAAG;YAAI;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACN1F,OAAA;cAAKkF,KAAK,EAAE;gBAAES,SAAS,EAAE,CAAC;gBAAE7B,KAAK,EAAE;cAAO,CAAE;cAAAuB,QAAA,GAAC,4BACrC,EAAC,CAAA7E,aAAa,aAAbA,aAAa,wBAAAL,qBAAA,GAAbK,aAAa,CAAEkC,kBAAkB,cAAAvC,qBAAA,uBAAjCA,qBAAA,CAAmC0G,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,OAC5D;YAAA;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN1F,OAAA,CAACd,GAAG;QAAC+G,EAAE,EAAE,EAAG;QAACS,EAAE,EAAE,EAAG;QAAArB,QAAA,eAClBrF,OAAA,CAACb,IAAI;UAACkH,KAAK,EAAC,sCAAQ;UAACD,SAAS,EAAC,gBAAgB;UAAAf,QAAA,gBAC7CrF,OAAA,CAACf,GAAG;YAAC+G,MAAM,EAAE,EAAG;YAAAX,QAAA,gBACdrF,OAAA,CAACd,GAAG;cAAC4H,IAAI,EAAE,EAAG;cAAAzB,QAAA,eACZrF,OAAA,CAACZ,SAAS;gBACRiH,KAAK,EAAC,oBAAK;gBACXvE,KAAK,EAAE,CAAApB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEoC,WAAW,KAAI,CAAE;gBACvC0D,MAAM,EAAC,QAAG;gBACVC,SAAS,EAAE;cAAE;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1F,OAAA,CAACd,GAAG;cAAC4H,IAAI,EAAE,EAAG;cAAAzB,QAAA,eACZrF,OAAA,CAACZ,SAAS;gBACRiH,KAAK,EAAC,0BAAM;gBACZvE,KAAK,EAAE,CAAApB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEuC,cAAc,KAAI,CAAE;gBAC1CuD,MAAM,EAAC;cAAG;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1F,OAAA;YAAKkF,KAAK,EAAE;cAAES,SAAS,EAAE;YAAG,CAAE;YAAAN,QAAA,gBAC5BrF,OAAA;cAAKkF,KAAK,EAAE;gBAAEa,YAAY,EAAE;cAAE,CAAE;cAAAV,QAAA,GAAC,4BACzB,EAAC,CAAC,CAAC,CAAA3E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsC,aAAa,KAAI,CAAC,IAAI,GAAG,EAAE6D,OAAO,CAAC,CAAC,CAAC,EAAC,GAChE;YAAA;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN1F,OAAA,CAACR,QAAQ;cACPuH,OAAO,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAAvG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsC,aAAa,KAAI,CAAC,IAAI,GAAG,CAAE;cAC7DtB,MAAM,EAAE,CAAC,CAAAhB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsC,aAAa,KAAI,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,WAAY;cAC3EkE,WAAW,EAAE,CAAC,CAAAxG,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsC,aAAa,KAAI,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG;YAAU;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN1F,OAAA;YAAKkF,KAAK,EAAE;cAAES,SAAS,EAAE,EAAE;cAAE7B,KAAK,EAAE;YAAO,CAAE;YAAAuB,QAAA,GAAC,4BACtC,EAAC,CAAC,CAAA3E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwC,YAAY,KAAI,CAAC,EAAEiE,cAAc,CAAC,CAAC,EAAC,QAC7D;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1F,OAAA,CAACf,GAAG;MAAC+G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACd,KAAK,EAAE;QAAES,SAAS,EAAE;MAAG,CAAE;MAAAN,QAAA,eAC9CrF,OAAA,CAACd,GAAG;QAAC4H,IAAI,EAAE,EAAG;QAAAzB,QAAA,eACZrF,OAAA,CAACb,IAAI;UAACkH,KAAK,EAAC,0BAAM;UAACD,SAAS,EAAC,gBAAgB;UAAAf,QAAA,gBAC3CrF,OAAA,CAACf,GAAG;YAAC+G,MAAM,EAAE,EAAG;YAAAX,QAAA,gBACdrF,OAAA,CAACd,GAAG;cAAC+G,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACjBrF,OAAA;gBAAKkF,KAAK,EAAE;kBAAEC,SAAS,EAAE;gBAAS,CAAE;gBAAAE,QAAA,gBAClCrF,OAAA;kBAAKoG,SAAS,EAAE,2BAA2BxF,YAAY,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS;gBAAG;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,8BAC/F,EAAC9E,YAAY,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI;cAAA;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1F,OAAA,CAACd,GAAG;cAAC+G,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACjBrF,OAAA;gBAAKkF,KAAK,EAAE;kBAAEC,SAAS,EAAE;gBAAS,CAAE;gBAAAE,QAAA,gBAClCrF,OAAA;kBAAKoG,SAAS,EAAC;gBAAgC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gDAExD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1F,OAAA,CAACd,GAAG;cAAC+G,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACjBrF,OAAA;gBAAKkF,KAAK,EAAE;kBAAEC,SAAS,EAAE;gBAAS,CAAE;gBAAAE,QAAA,gBAClCrF,OAAA;kBAAKoG,SAAS,EAAC;gBAAgC;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,0CAExD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1F,OAAA;YAAKkF,KAAK,EAAE;cAAES,SAAS,EAAE,EAAE;cAAER,SAAS,EAAE,QAAQ;cAAErB,KAAK,EAAE;YAAO,CAAE;YAAAuB,QAAA,GAAC,4BAC3D,EAAC,IAAI9C,IAAI,CAAC,CAAC,CAAC6E,kBAAkB,CAAC,CAAC;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxF,EAAA,CArTID,SAAS;AAAAoH,EAAA,GAATpH,SAAS;AAuTf,eAAeA,SAAS;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}