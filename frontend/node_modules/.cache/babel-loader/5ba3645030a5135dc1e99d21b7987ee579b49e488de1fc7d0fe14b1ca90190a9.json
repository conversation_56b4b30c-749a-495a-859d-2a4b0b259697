{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { isArray, each } from 'zrender/lib/core/util.js';\nimport * as vector from 'zrender/lib/core/vector.js';\nimport * as symbolUtil from '../../util/symbol.js';\nimport ECLinePath from './LinePath.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, enterEmphasis, leaveEmphasis, SPECIAL_STATES } from '../../util/states.js';\nimport { getLabelStatesModels, setLabelStyle } from '../../label/labelStyle.js';\nimport { round } from '../../util/number.js';\nvar SYMBOL_CATEGORIES = ['fromSymbol', 'toSymbol'];\nfunction makeSymbolTypeKey(symbolCategory) {\n  return '_' + symbolCategory + 'Type';\n}\nfunction makeSymbolTypeValue(name, lineData, idx) {\n  var symbolType = lineData.getItemVisual(idx, name);\n  if (!symbolType || symbolType === 'none') {\n    return symbolType;\n  }\n  var symbolSize = lineData.getItemVisual(idx, name + 'Size');\n  var symbolRotate = lineData.getItemVisual(idx, name + 'Rotate');\n  var symbolOffset = lineData.getItemVisual(idx, name + 'Offset');\n  var symbolKeepAspect = lineData.getItemVisual(idx, name + 'KeepAspect');\n  var symbolSizeArr = symbolUtil.normalizeSymbolSize(symbolSize);\n  var symbolOffsetArr = symbolUtil.normalizeSymbolOffset(symbolOffset || 0, symbolSizeArr);\n  return symbolType + symbolSizeArr + symbolOffsetArr + (symbolRotate || '') + (symbolKeepAspect || '');\n}\n/**\r\n * @inner\r\n */\nfunction createSymbol(name, lineData, idx) {\n  var symbolType = lineData.getItemVisual(idx, name);\n  if (!symbolType || symbolType === 'none') {\n    return;\n  }\n  var symbolSize = lineData.getItemVisual(idx, name + 'Size');\n  var symbolRotate = lineData.getItemVisual(idx, name + 'Rotate');\n  var symbolOffset = lineData.getItemVisual(idx, name + 'Offset');\n  var symbolKeepAspect = lineData.getItemVisual(idx, name + 'KeepAspect');\n  var symbolSizeArr = symbolUtil.normalizeSymbolSize(symbolSize);\n  var symbolOffsetArr = symbolUtil.normalizeSymbolOffset(symbolOffset || 0, symbolSizeArr);\n  var symbolPath = symbolUtil.createSymbol(symbolType, -symbolSizeArr[0] / 2 + symbolOffsetArr[0], -symbolSizeArr[1] / 2 + symbolOffsetArr[1], symbolSizeArr[0], symbolSizeArr[1], null, symbolKeepAspect);\n  symbolPath.__specifiedRotation = symbolRotate == null || isNaN(symbolRotate) ? void 0 : +symbolRotate * Math.PI / 180 || 0;\n  symbolPath.name = name;\n  return symbolPath;\n}\nfunction createLine(points) {\n  var line = new ECLinePath({\n    name: 'line',\n    subPixelOptimize: true\n  });\n  setLinePoints(line.shape, points);\n  return line;\n}\nfunction setLinePoints(targetShape, points) {\n  targetShape.x1 = points[0][0];\n  targetShape.y1 = points[0][1];\n  targetShape.x2 = points[1][0];\n  targetShape.y2 = points[1][1];\n  targetShape.percent = 1;\n  var cp1 = points[2];\n  if (cp1) {\n    targetShape.cpx1 = cp1[0];\n    targetShape.cpy1 = cp1[1];\n  } else {\n    targetShape.cpx1 = NaN;\n    targetShape.cpy1 = NaN;\n  }\n}\nvar Line = /** @class */function (_super) {\n  __extends(Line, _super);\n  function Line(lineData, idx, seriesScope) {\n    var _this = _super.call(this) || this;\n    _this._createLine(lineData, idx, seriesScope);\n    return _this;\n  }\n  Line.prototype._createLine = function (lineData, idx, seriesScope) {\n    var seriesModel = lineData.hostModel;\n    var linePoints = lineData.getItemLayout(idx);\n    var line = createLine(linePoints);\n    line.shape.percent = 0;\n    graphic.initProps(line, {\n      shape: {\n        percent: 1\n      }\n    }, seriesModel, idx);\n    this.add(line);\n    each(SYMBOL_CATEGORIES, function (symbolCategory) {\n      var symbol = createSymbol(symbolCategory, lineData, idx);\n      // symbols must added after line to make sure\n      // it will be updated after line#update.\n      // Or symbol position and rotation update in line#beforeUpdate will be one frame slow\n      this.add(symbol);\n      this[makeSymbolTypeKey(symbolCategory)] = makeSymbolTypeValue(symbolCategory, lineData, idx);\n    }, this);\n    this._updateCommonStl(lineData, idx, seriesScope);\n  };\n  // TODO More strict on the List type in parameters?\n  Line.prototype.updateData = function (lineData, idx, seriesScope) {\n    var seriesModel = lineData.hostModel;\n    var line = this.childOfName('line');\n    var linePoints = lineData.getItemLayout(idx);\n    var target = {\n      shape: {}\n    };\n    setLinePoints(target.shape, linePoints);\n    graphic.updateProps(line, target, seriesModel, idx);\n    each(SYMBOL_CATEGORIES, function (symbolCategory) {\n      var symbolType = makeSymbolTypeValue(symbolCategory, lineData, idx);\n      var key = makeSymbolTypeKey(symbolCategory);\n      // Symbol changed\n      if (this[key] !== symbolType) {\n        this.remove(this.childOfName(symbolCategory));\n        var symbol = createSymbol(symbolCategory, lineData, idx);\n        this.add(symbol);\n      }\n      this[key] = symbolType;\n    }, this);\n    this._updateCommonStl(lineData, idx, seriesScope);\n  };\n  ;\n  Line.prototype.getLinePath = function () {\n    return this.childAt(0);\n  };\n  Line.prototype._updateCommonStl = function (lineData, idx, seriesScope) {\n    var seriesModel = lineData.hostModel;\n    var line = this.childOfName('line');\n    var emphasisLineStyle = seriesScope && seriesScope.emphasisLineStyle;\n    var blurLineStyle = seriesScope && seriesScope.blurLineStyle;\n    var selectLineStyle = seriesScope && seriesScope.selectLineStyle;\n    var labelStatesModels = seriesScope && seriesScope.labelStatesModels;\n    var emphasisDisabled = seriesScope && seriesScope.emphasisDisabled;\n    var focus = seriesScope && seriesScope.focus;\n    var blurScope = seriesScope && seriesScope.blurScope;\n    // Optimization for large dataset\n    if (!seriesScope || lineData.hasItemOption) {\n      var itemModel = lineData.getItemModel(idx);\n      var emphasisModel = itemModel.getModel('emphasis');\n      emphasisLineStyle = emphasisModel.getModel('lineStyle').getLineStyle();\n      blurLineStyle = itemModel.getModel(['blur', 'lineStyle']).getLineStyle();\n      selectLineStyle = itemModel.getModel(['select', 'lineStyle']).getLineStyle();\n      emphasisDisabled = emphasisModel.get('disabled');\n      focus = emphasisModel.get('focus');\n      blurScope = emphasisModel.get('blurScope');\n      labelStatesModels = getLabelStatesModels(itemModel);\n    }\n    var lineStyle = lineData.getItemVisual(idx, 'style');\n    var visualColor = lineStyle.stroke;\n    line.useStyle(lineStyle);\n    line.style.fill = null;\n    line.style.strokeNoScale = true;\n    line.ensureState('emphasis').style = emphasisLineStyle;\n    line.ensureState('blur').style = blurLineStyle;\n    line.ensureState('select').style = selectLineStyle;\n    // Update symbol\n    each(SYMBOL_CATEGORIES, function (symbolCategory) {\n      var symbol = this.childOfName(symbolCategory);\n      if (symbol) {\n        // Share opacity and color with line.\n        symbol.setColor(visualColor);\n        symbol.style.opacity = lineStyle.opacity;\n        for (var i = 0; i < SPECIAL_STATES.length; i++) {\n          var stateName = SPECIAL_STATES[i];\n          var lineState = line.getState(stateName);\n          if (lineState) {\n            var lineStateStyle = lineState.style || {};\n            var state = symbol.ensureState(stateName);\n            var stateStyle = state.style || (state.style = {});\n            if (lineStateStyle.stroke != null) {\n              stateStyle[symbol.__isEmptyBrush ? 'stroke' : 'fill'] = lineStateStyle.stroke;\n            }\n            if (lineStateStyle.opacity != null) {\n              stateStyle.opacity = lineStateStyle.opacity;\n            }\n          }\n        }\n        symbol.markRedraw();\n      }\n    }, this);\n    var rawVal = seriesModel.getRawValue(idx);\n    setLabelStyle(this, labelStatesModels, {\n      labelDataIndex: idx,\n      labelFetcher: {\n        getFormattedLabel: function (dataIndex, stateName) {\n          return seriesModel.getFormattedLabel(dataIndex, stateName, lineData.dataType);\n        }\n      },\n      inheritColor: visualColor || '#000',\n      defaultOpacity: lineStyle.opacity,\n      defaultText: (rawVal == null ? lineData.getName(idx) : isFinite(rawVal) ? round(rawVal) : rawVal) + ''\n    });\n    var label = this.getTextContent();\n    // Always set `textStyle` even if `normalStyle.text` is null, because default\n    // values have to be set on `normalStyle`.\n    if (label) {\n      var labelNormalModel = labelStatesModels.normal;\n      label.__align = label.style.align;\n      label.__verticalAlign = label.style.verticalAlign;\n      // 'start', 'middle', 'end'\n      label.__position = labelNormalModel.get('position') || 'middle';\n      var distance = labelNormalModel.get('distance');\n      if (!isArray(distance)) {\n        distance = [distance, distance];\n      }\n      label.__labelDistance = distance;\n    }\n    this.setTextConfig({\n      position: null,\n      local: true,\n      inside: false // Can't be inside for stroke element.\n    });\n    toggleHoverEmphasis(this, focus, blurScope, emphasisDisabled);\n  };\n  Line.prototype.highlight = function () {\n    enterEmphasis(this);\n  };\n  Line.prototype.downplay = function () {\n    leaveEmphasis(this);\n  };\n  Line.prototype.updateLayout = function (lineData, idx) {\n    this.setLinePoints(lineData.getItemLayout(idx));\n  };\n  Line.prototype.setLinePoints = function (points) {\n    var linePath = this.childOfName('line');\n    setLinePoints(linePath.shape, points);\n    linePath.dirty();\n  };\n  Line.prototype.beforeUpdate = function () {\n    var lineGroup = this;\n    var symbolFrom = lineGroup.childOfName('fromSymbol');\n    var symbolTo = lineGroup.childOfName('toSymbol');\n    var label = lineGroup.getTextContent();\n    // Quick reject\n    if (!symbolFrom && !symbolTo && (!label || label.ignore)) {\n      return;\n    }\n    var invScale = 1;\n    var parentNode = this.parent;\n    while (parentNode) {\n      if (parentNode.scaleX) {\n        invScale /= parentNode.scaleX;\n      }\n      parentNode = parentNode.parent;\n    }\n    var line = lineGroup.childOfName('line');\n    // If line not changed\n    // FIXME Parent scale changed\n    if (!this.__dirty && !line.__dirty) {\n      return;\n    }\n    var percent = line.shape.percent;\n    var fromPos = line.pointAt(0);\n    var toPos = line.pointAt(percent);\n    var d = vector.sub([], toPos, fromPos);\n    vector.normalize(d, d);\n    function setSymbolRotation(symbol, percent) {\n      // Fix #12388\n      // when symbol is set to be 'arrow' in markLine,\n      // symbolRotate value will be ignored, and compulsively use tangent angle.\n      // rotate by default if symbol rotation is not specified\n      var specifiedRotation = symbol.__specifiedRotation;\n      if (specifiedRotation == null) {\n        var tangent = line.tangentAt(percent);\n        symbol.attr('rotation', (percent === 1 ? -1 : 1) * Math.PI / 2 - Math.atan2(tangent[1], tangent[0]));\n      } else {\n        symbol.attr('rotation', specifiedRotation);\n      }\n    }\n    if (symbolFrom) {\n      symbolFrom.setPosition(fromPos);\n      setSymbolRotation(symbolFrom, 0);\n      symbolFrom.scaleX = symbolFrom.scaleY = invScale * percent;\n      symbolFrom.markRedraw();\n    }\n    if (symbolTo) {\n      symbolTo.setPosition(toPos);\n      setSymbolRotation(symbolTo, 1);\n      symbolTo.scaleX = symbolTo.scaleY = invScale * percent;\n      symbolTo.markRedraw();\n    }\n    if (label && !label.ignore) {\n      label.x = label.y = 0;\n      label.originX = label.originY = 0;\n      var textAlign = void 0;\n      var textVerticalAlign = void 0;\n      var distance = label.__labelDistance;\n      var distanceX = distance[0] * invScale;\n      var distanceY = distance[1] * invScale;\n      var halfPercent = percent / 2;\n      var tangent = line.tangentAt(halfPercent);\n      var n = [tangent[1], -tangent[0]];\n      var cp = line.pointAt(halfPercent);\n      if (n[1] > 0) {\n        n[0] = -n[0];\n        n[1] = -n[1];\n      }\n      var dir = tangent[0] < 0 ? -1 : 1;\n      if (label.__position !== 'start' && label.__position !== 'end') {\n        var rotation = -Math.atan2(tangent[1], tangent[0]);\n        if (toPos[0] < fromPos[0]) {\n          rotation = Math.PI + rotation;\n        }\n        label.rotation = rotation;\n      }\n      var dy = void 0;\n      switch (label.__position) {\n        case 'insideStartTop':\n        case 'insideMiddleTop':\n        case 'insideEndTop':\n        case 'middle':\n          dy = -distanceY;\n          textVerticalAlign = 'bottom';\n          break;\n        case 'insideStartBottom':\n        case 'insideMiddleBottom':\n        case 'insideEndBottom':\n          dy = distanceY;\n          textVerticalAlign = 'top';\n          break;\n        default:\n          dy = 0;\n          textVerticalAlign = 'middle';\n      }\n      switch (label.__position) {\n        case 'end':\n          label.x = d[0] * distanceX + toPos[0];\n          label.y = d[1] * distanceY + toPos[1];\n          textAlign = d[0] > 0.8 ? 'left' : d[0] < -0.8 ? 'right' : 'center';\n          textVerticalAlign = d[1] > 0.8 ? 'top' : d[1] < -0.8 ? 'bottom' : 'middle';\n          break;\n        case 'start':\n          label.x = -d[0] * distanceX + fromPos[0];\n          label.y = -d[1] * distanceY + fromPos[1];\n          textAlign = d[0] > 0.8 ? 'right' : d[0] < -0.8 ? 'left' : 'center';\n          textVerticalAlign = d[1] > 0.8 ? 'bottom' : d[1] < -0.8 ? 'top' : 'middle';\n          break;\n        case 'insideStartTop':\n        case 'insideStart':\n        case 'insideStartBottom':\n          label.x = distanceX * dir + fromPos[0];\n          label.y = fromPos[1] + dy;\n          textAlign = tangent[0] < 0 ? 'right' : 'left';\n          label.originX = -distanceX * dir;\n          label.originY = -dy;\n          break;\n        case 'insideMiddleTop':\n        case 'insideMiddle':\n        case 'insideMiddleBottom':\n        case 'middle':\n          label.x = cp[0];\n          label.y = cp[1] + dy;\n          textAlign = 'center';\n          label.originY = -dy;\n          break;\n        case 'insideEndTop':\n        case 'insideEnd':\n        case 'insideEndBottom':\n          label.x = -distanceX * dir + toPos[0];\n          label.y = toPos[1] + dy;\n          textAlign = tangent[0] >= 0 ? 'right' : 'left';\n          label.originX = distanceX * dir;\n          label.originY = -dy;\n          break;\n      }\n      label.scaleX = label.scaleY = invScale;\n      label.setStyle({\n        // Use the user specified text align and baseline first\n        verticalAlign: label.__verticalAlign || textVerticalAlign,\n        align: label.__align || textAlign\n      });\n    }\n  };\n  return Line;\n}(graphic.Group);\nexport default Line;", "map": {"version": 3, "names": ["__extends", "isArray", "each", "vector", "symbolUtil", "ECLinePath", "graphic", "toggleHoverEmphasis", "enterEmphasis", "leaveEmphasis", "SPECIAL_STATES", "getLabelStatesModels", "setLabelStyle", "round", "SYMBOL_CATEGORIES", "makeSymbolTypeKey", "symbolCategory", "makeSymbolTypeValue", "name", "lineData", "idx", "symbolType", "getItemVisual", "symbolSize", "symbolRotate", "symbolOffset", "symbolKeepAspect", "symbolSizeArr", "normalizeSymbolSize", "symbolOffsetArr", "normalizeSymbolOffset", "createSymbol", "symbolPath", "__specifiedRotation", "isNaN", "Math", "PI", "createLine", "points", "line", "subPixelOptimize", "setLinePoints", "shape", "targetShape", "x1", "y1", "x2", "y2", "percent", "cp1", "cpx1", "cpy1", "NaN", "Line", "_super", "seriesScope", "_this", "call", "_createLine", "prototype", "seriesModel", "hostModel", "linePoints", "getItemLayout", "initProps", "add", "symbol", "_updateCommonStl", "updateData", "childOfName", "target", "updateProps", "key", "remove", "get<PERSON>inePath", "childAt", "emphasisLineStyle", "blurLineStyle", "selectLineStyle", "labelStatesModels", "emphasisDisabled", "focus", "blurScope", "hasItemOption", "itemModel", "getItemModel", "emphasisModel", "getModel", "getLineStyle", "get", "lineStyle", "visualColor", "stroke", "useStyle", "style", "fill", "strokeNoScale", "ensureState", "setColor", "opacity", "i", "length", "stateName", "lineState", "getState", "lineStateStyle", "state", "stateStyle", "__isEmptyBrush", "mark<PERSON><PERSON><PERSON>", "rawVal", "getRawValue", "labelDataIndex", "labelFetcher", "getFormattedLabel", "dataIndex", "dataType", "inheritColor", "defaultOpacity", "defaultText", "getName", "isFinite", "label", "getTextContent", "labelNormalModel", "normal", "__align", "align", "__verticalAlign", "verticalAlign", "__position", "distance", "__labelDistance", "setTextConfig", "position", "local", "inside", "highlight", "downplay", "updateLayout", "linePath", "dirty", "beforeUpdate", "lineGroup", "symbolFrom", "symbolTo", "ignore", "invScale", "parentNode", "parent", "scaleX", "__dirty", "fromPos", "pointAt", "toPos", "d", "sub", "normalize", "setSymbolRotation", "specifiedRotation", "tangent", "tangentAt", "attr", "atan2", "setPosition", "scaleY", "x", "y", "originX", "originY", "textAlign", "textVerticalAlign", "distanceX", "distanceY", "halfPercent", "n", "cp", "dir", "rotation", "dy", "setStyle", "Group"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/node_modules/echarts/lib/chart/helper/Line.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { isArray, each } from 'zrender/lib/core/util.js';\nimport * as vector from 'zrender/lib/core/vector.js';\nimport * as symbolUtil from '../../util/symbol.js';\nimport ECLinePath from './LinePath.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, enterEmphasis, leaveEmphasis, SPECIAL_STATES } from '../../util/states.js';\nimport { getLabelStatesModels, setLabelStyle } from '../../label/labelStyle.js';\nimport { round } from '../../util/number.js';\nvar SYMBOL_CATEGORIES = ['fromSymbol', 'toSymbol'];\nfunction makeSymbolTypeKey(symbolCategory) {\n  return '_' + symbolCategory + 'Type';\n}\nfunction makeSymbolTypeValue(name, lineData, idx) {\n  var symbolType = lineData.getItemVisual(idx, name);\n  if (!symbolType || symbolType === 'none') {\n    return symbolType;\n  }\n  var symbolSize = lineData.getItemVisual(idx, name + 'Size');\n  var symbolRotate = lineData.getItemVisual(idx, name + 'Rotate');\n  var symbolOffset = lineData.getItemVisual(idx, name + 'Offset');\n  var symbolKeepAspect = lineData.getItemVisual(idx, name + 'KeepAspect');\n  var symbolSizeArr = symbolUtil.normalizeSymbolSize(symbolSize);\n  var symbolOffsetArr = symbolUtil.normalizeSymbolOffset(symbolOffset || 0, symbolSizeArr);\n  return symbolType + symbolSizeArr + symbolOffsetArr + (symbolRotate || '') + (symbolKeepAspect || '');\n}\n/**\r\n * @inner\r\n */\nfunction createSymbol(name, lineData, idx) {\n  var symbolType = lineData.getItemVisual(idx, name);\n  if (!symbolType || symbolType === 'none') {\n    return;\n  }\n  var symbolSize = lineData.getItemVisual(idx, name + 'Size');\n  var symbolRotate = lineData.getItemVisual(idx, name + 'Rotate');\n  var symbolOffset = lineData.getItemVisual(idx, name + 'Offset');\n  var symbolKeepAspect = lineData.getItemVisual(idx, name + 'KeepAspect');\n  var symbolSizeArr = symbolUtil.normalizeSymbolSize(symbolSize);\n  var symbolOffsetArr = symbolUtil.normalizeSymbolOffset(symbolOffset || 0, symbolSizeArr);\n  var symbolPath = symbolUtil.createSymbol(symbolType, -symbolSizeArr[0] / 2 + symbolOffsetArr[0], -symbolSizeArr[1] / 2 + symbolOffsetArr[1], symbolSizeArr[0], symbolSizeArr[1], null, symbolKeepAspect);\n  symbolPath.__specifiedRotation = symbolRotate == null || isNaN(symbolRotate) ? void 0 : +symbolRotate * Math.PI / 180 || 0;\n  symbolPath.name = name;\n  return symbolPath;\n}\nfunction createLine(points) {\n  var line = new ECLinePath({\n    name: 'line',\n    subPixelOptimize: true\n  });\n  setLinePoints(line.shape, points);\n  return line;\n}\nfunction setLinePoints(targetShape, points) {\n  targetShape.x1 = points[0][0];\n  targetShape.y1 = points[0][1];\n  targetShape.x2 = points[1][0];\n  targetShape.y2 = points[1][1];\n  targetShape.percent = 1;\n  var cp1 = points[2];\n  if (cp1) {\n    targetShape.cpx1 = cp1[0];\n    targetShape.cpy1 = cp1[1];\n  } else {\n    targetShape.cpx1 = NaN;\n    targetShape.cpy1 = NaN;\n  }\n}\nvar Line = /** @class */function (_super) {\n  __extends(Line, _super);\n  function Line(lineData, idx, seriesScope) {\n    var _this = _super.call(this) || this;\n    _this._createLine(lineData, idx, seriesScope);\n    return _this;\n  }\n  Line.prototype._createLine = function (lineData, idx, seriesScope) {\n    var seriesModel = lineData.hostModel;\n    var linePoints = lineData.getItemLayout(idx);\n    var line = createLine(linePoints);\n    line.shape.percent = 0;\n    graphic.initProps(line, {\n      shape: {\n        percent: 1\n      }\n    }, seriesModel, idx);\n    this.add(line);\n    each(SYMBOL_CATEGORIES, function (symbolCategory) {\n      var symbol = createSymbol(symbolCategory, lineData, idx);\n      // symbols must added after line to make sure\n      // it will be updated after line#update.\n      // Or symbol position and rotation update in line#beforeUpdate will be one frame slow\n      this.add(symbol);\n      this[makeSymbolTypeKey(symbolCategory)] = makeSymbolTypeValue(symbolCategory, lineData, idx);\n    }, this);\n    this._updateCommonStl(lineData, idx, seriesScope);\n  };\n  // TODO More strict on the List type in parameters?\n  Line.prototype.updateData = function (lineData, idx, seriesScope) {\n    var seriesModel = lineData.hostModel;\n    var line = this.childOfName('line');\n    var linePoints = lineData.getItemLayout(idx);\n    var target = {\n      shape: {}\n    };\n    setLinePoints(target.shape, linePoints);\n    graphic.updateProps(line, target, seriesModel, idx);\n    each(SYMBOL_CATEGORIES, function (symbolCategory) {\n      var symbolType = makeSymbolTypeValue(symbolCategory, lineData, idx);\n      var key = makeSymbolTypeKey(symbolCategory);\n      // Symbol changed\n      if (this[key] !== symbolType) {\n        this.remove(this.childOfName(symbolCategory));\n        var symbol = createSymbol(symbolCategory, lineData, idx);\n        this.add(symbol);\n      }\n      this[key] = symbolType;\n    }, this);\n    this._updateCommonStl(lineData, idx, seriesScope);\n  };\n  ;\n  Line.prototype.getLinePath = function () {\n    return this.childAt(0);\n  };\n  Line.prototype._updateCommonStl = function (lineData, idx, seriesScope) {\n    var seriesModel = lineData.hostModel;\n    var line = this.childOfName('line');\n    var emphasisLineStyle = seriesScope && seriesScope.emphasisLineStyle;\n    var blurLineStyle = seriesScope && seriesScope.blurLineStyle;\n    var selectLineStyle = seriesScope && seriesScope.selectLineStyle;\n    var labelStatesModels = seriesScope && seriesScope.labelStatesModels;\n    var emphasisDisabled = seriesScope && seriesScope.emphasisDisabled;\n    var focus = seriesScope && seriesScope.focus;\n    var blurScope = seriesScope && seriesScope.blurScope;\n    // Optimization for large dataset\n    if (!seriesScope || lineData.hasItemOption) {\n      var itemModel = lineData.getItemModel(idx);\n      var emphasisModel = itemModel.getModel('emphasis');\n      emphasisLineStyle = emphasisModel.getModel('lineStyle').getLineStyle();\n      blurLineStyle = itemModel.getModel(['blur', 'lineStyle']).getLineStyle();\n      selectLineStyle = itemModel.getModel(['select', 'lineStyle']).getLineStyle();\n      emphasisDisabled = emphasisModel.get('disabled');\n      focus = emphasisModel.get('focus');\n      blurScope = emphasisModel.get('blurScope');\n      labelStatesModels = getLabelStatesModels(itemModel);\n    }\n    var lineStyle = lineData.getItemVisual(idx, 'style');\n    var visualColor = lineStyle.stroke;\n    line.useStyle(lineStyle);\n    line.style.fill = null;\n    line.style.strokeNoScale = true;\n    line.ensureState('emphasis').style = emphasisLineStyle;\n    line.ensureState('blur').style = blurLineStyle;\n    line.ensureState('select').style = selectLineStyle;\n    // Update symbol\n    each(SYMBOL_CATEGORIES, function (symbolCategory) {\n      var symbol = this.childOfName(symbolCategory);\n      if (symbol) {\n        // Share opacity and color with line.\n        symbol.setColor(visualColor);\n        symbol.style.opacity = lineStyle.opacity;\n        for (var i = 0; i < SPECIAL_STATES.length; i++) {\n          var stateName = SPECIAL_STATES[i];\n          var lineState = line.getState(stateName);\n          if (lineState) {\n            var lineStateStyle = lineState.style || {};\n            var state = symbol.ensureState(stateName);\n            var stateStyle = state.style || (state.style = {});\n            if (lineStateStyle.stroke != null) {\n              stateStyle[symbol.__isEmptyBrush ? 'stroke' : 'fill'] = lineStateStyle.stroke;\n            }\n            if (lineStateStyle.opacity != null) {\n              stateStyle.opacity = lineStateStyle.opacity;\n            }\n          }\n        }\n        symbol.markRedraw();\n      }\n    }, this);\n    var rawVal = seriesModel.getRawValue(idx);\n    setLabelStyle(this, labelStatesModels, {\n      labelDataIndex: idx,\n      labelFetcher: {\n        getFormattedLabel: function (dataIndex, stateName) {\n          return seriesModel.getFormattedLabel(dataIndex, stateName, lineData.dataType);\n        }\n      },\n      inheritColor: visualColor || '#000',\n      defaultOpacity: lineStyle.opacity,\n      defaultText: (rawVal == null ? lineData.getName(idx) : isFinite(rawVal) ? round(rawVal) : rawVal) + ''\n    });\n    var label = this.getTextContent();\n    // Always set `textStyle` even if `normalStyle.text` is null, because default\n    // values have to be set on `normalStyle`.\n    if (label) {\n      var labelNormalModel = labelStatesModels.normal;\n      label.__align = label.style.align;\n      label.__verticalAlign = label.style.verticalAlign;\n      // 'start', 'middle', 'end'\n      label.__position = labelNormalModel.get('position') || 'middle';\n      var distance = labelNormalModel.get('distance');\n      if (!isArray(distance)) {\n        distance = [distance, distance];\n      }\n      label.__labelDistance = distance;\n    }\n    this.setTextConfig({\n      position: null,\n      local: true,\n      inside: false // Can't be inside for stroke element.\n    });\n    toggleHoverEmphasis(this, focus, blurScope, emphasisDisabled);\n  };\n  Line.prototype.highlight = function () {\n    enterEmphasis(this);\n  };\n  Line.prototype.downplay = function () {\n    leaveEmphasis(this);\n  };\n  Line.prototype.updateLayout = function (lineData, idx) {\n    this.setLinePoints(lineData.getItemLayout(idx));\n  };\n  Line.prototype.setLinePoints = function (points) {\n    var linePath = this.childOfName('line');\n    setLinePoints(linePath.shape, points);\n    linePath.dirty();\n  };\n  Line.prototype.beforeUpdate = function () {\n    var lineGroup = this;\n    var symbolFrom = lineGroup.childOfName('fromSymbol');\n    var symbolTo = lineGroup.childOfName('toSymbol');\n    var label = lineGroup.getTextContent();\n    // Quick reject\n    if (!symbolFrom && !symbolTo && (!label || label.ignore)) {\n      return;\n    }\n    var invScale = 1;\n    var parentNode = this.parent;\n    while (parentNode) {\n      if (parentNode.scaleX) {\n        invScale /= parentNode.scaleX;\n      }\n      parentNode = parentNode.parent;\n    }\n    var line = lineGroup.childOfName('line');\n    // If line not changed\n    // FIXME Parent scale changed\n    if (!this.__dirty && !line.__dirty) {\n      return;\n    }\n    var percent = line.shape.percent;\n    var fromPos = line.pointAt(0);\n    var toPos = line.pointAt(percent);\n    var d = vector.sub([], toPos, fromPos);\n    vector.normalize(d, d);\n    function setSymbolRotation(symbol, percent) {\n      // Fix #12388\n      // when symbol is set to be 'arrow' in markLine,\n      // symbolRotate value will be ignored, and compulsively use tangent angle.\n      // rotate by default if symbol rotation is not specified\n      var specifiedRotation = symbol.__specifiedRotation;\n      if (specifiedRotation == null) {\n        var tangent = line.tangentAt(percent);\n        symbol.attr('rotation', (percent === 1 ? -1 : 1) * Math.PI / 2 - Math.atan2(tangent[1], tangent[0]));\n      } else {\n        symbol.attr('rotation', specifiedRotation);\n      }\n    }\n    if (symbolFrom) {\n      symbolFrom.setPosition(fromPos);\n      setSymbolRotation(symbolFrom, 0);\n      symbolFrom.scaleX = symbolFrom.scaleY = invScale * percent;\n      symbolFrom.markRedraw();\n    }\n    if (symbolTo) {\n      symbolTo.setPosition(toPos);\n      setSymbolRotation(symbolTo, 1);\n      symbolTo.scaleX = symbolTo.scaleY = invScale * percent;\n      symbolTo.markRedraw();\n    }\n    if (label && !label.ignore) {\n      label.x = label.y = 0;\n      label.originX = label.originY = 0;\n      var textAlign = void 0;\n      var textVerticalAlign = void 0;\n      var distance = label.__labelDistance;\n      var distanceX = distance[0] * invScale;\n      var distanceY = distance[1] * invScale;\n      var halfPercent = percent / 2;\n      var tangent = line.tangentAt(halfPercent);\n      var n = [tangent[1], -tangent[0]];\n      var cp = line.pointAt(halfPercent);\n      if (n[1] > 0) {\n        n[0] = -n[0];\n        n[1] = -n[1];\n      }\n      var dir = tangent[0] < 0 ? -1 : 1;\n      if (label.__position !== 'start' && label.__position !== 'end') {\n        var rotation = -Math.atan2(tangent[1], tangent[0]);\n        if (toPos[0] < fromPos[0]) {\n          rotation = Math.PI + rotation;\n        }\n        label.rotation = rotation;\n      }\n      var dy = void 0;\n      switch (label.__position) {\n        case 'insideStartTop':\n        case 'insideMiddleTop':\n        case 'insideEndTop':\n        case 'middle':\n          dy = -distanceY;\n          textVerticalAlign = 'bottom';\n          break;\n        case 'insideStartBottom':\n        case 'insideMiddleBottom':\n        case 'insideEndBottom':\n          dy = distanceY;\n          textVerticalAlign = 'top';\n          break;\n        default:\n          dy = 0;\n          textVerticalAlign = 'middle';\n      }\n      switch (label.__position) {\n        case 'end':\n          label.x = d[0] * distanceX + toPos[0];\n          label.y = d[1] * distanceY + toPos[1];\n          textAlign = d[0] > 0.8 ? 'left' : d[0] < -0.8 ? 'right' : 'center';\n          textVerticalAlign = d[1] > 0.8 ? 'top' : d[1] < -0.8 ? 'bottom' : 'middle';\n          break;\n        case 'start':\n          label.x = -d[0] * distanceX + fromPos[0];\n          label.y = -d[1] * distanceY + fromPos[1];\n          textAlign = d[0] > 0.8 ? 'right' : d[0] < -0.8 ? 'left' : 'center';\n          textVerticalAlign = d[1] > 0.8 ? 'bottom' : d[1] < -0.8 ? 'top' : 'middle';\n          break;\n        case 'insideStartTop':\n        case 'insideStart':\n        case 'insideStartBottom':\n          label.x = distanceX * dir + fromPos[0];\n          label.y = fromPos[1] + dy;\n          textAlign = tangent[0] < 0 ? 'right' : 'left';\n          label.originX = -distanceX * dir;\n          label.originY = -dy;\n          break;\n        case 'insideMiddleTop':\n        case 'insideMiddle':\n        case 'insideMiddleBottom':\n        case 'middle':\n          label.x = cp[0];\n          label.y = cp[1] + dy;\n          textAlign = 'center';\n          label.originY = -dy;\n          break;\n        case 'insideEndTop':\n        case 'insideEnd':\n        case 'insideEndBottom':\n          label.x = -distanceX * dir + toPos[0];\n          label.y = toPos[1] + dy;\n          textAlign = tangent[0] >= 0 ? 'right' : 'left';\n          label.originX = distanceX * dir;\n          label.originY = -dy;\n          break;\n      }\n      label.scaleX = label.scaleY = invScale;\n      label.setStyle({\n        // Use the user specified text align and baseline first\n        verticalAlign: label.__verticalAlign || textVerticalAlign,\n        align: label.__align || textAlign\n      });\n    }\n  };\n  return Line;\n}(graphic.Group);\nexport default Line;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,OAAO,EAAEC,IAAI,QAAQ,0BAA0B;AACxD,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,mBAAmB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,cAAc,QAAQ,sBAAsB;AACxG,SAASC,oBAAoB,EAAEC,aAAa,QAAQ,2BAA2B;AAC/E,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,IAAIC,iBAAiB,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC;AAClD,SAASC,iBAAiBA,CAACC,cAAc,EAAE;EACzC,OAAO,GAAG,GAAGA,cAAc,GAAG,MAAM;AACtC;AACA,SAASC,mBAAmBA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAE;EAChD,IAAIC,UAAU,GAAGF,QAAQ,CAACG,aAAa,CAACF,GAAG,EAAEF,IAAI,CAAC;EAClD,IAAI,CAACG,UAAU,IAAIA,UAAU,KAAK,MAAM,EAAE;IACxC,OAAOA,UAAU;EACnB;EACA,IAAIE,UAAU,GAAGJ,QAAQ,CAACG,aAAa,CAACF,GAAG,EAAEF,IAAI,GAAG,MAAM,CAAC;EAC3D,IAAIM,YAAY,GAAGL,QAAQ,CAACG,aAAa,CAACF,GAAG,EAAEF,IAAI,GAAG,QAAQ,CAAC;EAC/D,IAAIO,YAAY,GAAGN,QAAQ,CAACG,aAAa,CAACF,GAAG,EAAEF,IAAI,GAAG,QAAQ,CAAC;EAC/D,IAAIQ,gBAAgB,GAAGP,QAAQ,CAACG,aAAa,CAACF,GAAG,EAAEF,IAAI,GAAG,YAAY,CAAC;EACvE,IAAIS,aAAa,GAAGvB,UAAU,CAACwB,mBAAmB,CAACL,UAAU,CAAC;EAC9D,IAAIM,eAAe,GAAGzB,UAAU,CAAC0B,qBAAqB,CAACL,YAAY,IAAI,CAAC,EAAEE,aAAa,CAAC;EACxF,OAAON,UAAU,GAAGM,aAAa,GAAGE,eAAe,IAAIL,YAAY,IAAI,EAAE,CAAC,IAAIE,gBAAgB,IAAI,EAAE,CAAC;AACvG;AACA;AACA;AACA;AACA,SAASK,YAAYA,CAACb,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAE;EACzC,IAAIC,UAAU,GAAGF,QAAQ,CAACG,aAAa,CAACF,GAAG,EAAEF,IAAI,CAAC;EAClD,IAAI,CAACG,UAAU,IAAIA,UAAU,KAAK,MAAM,EAAE;IACxC;EACF;EACA,IAAIE,UAAU,GAAGJ,QAAQ,CAACG,aAAa,CAACF,GAAG,EAAEF,IAAI,GAAG,MAAM,CAAC;EAC3D,IAAIM,YAAY,GAAGL,QAAQ,CAACG,aAAa,CAACF,GAAG,EAAEF,IAAI,GAAG,QAAQ,CAAC;EAC/D,IAAIO,YAAY,GAAGN,QAAQ,CAACG,aAAa,CAACF,GAAG,EAAEF,IAAI,GAAG,QAAQ,CAAC;EAC/D,IAAIQ,gBAAgB,GAAGP,QAAQ,CAACG,aAAa,CAACF,GAAG,EAAEF,IAAI,GAAG,YAAY,CAAC;EACvE,IAAIS,aAAa,GAAGvB,UAAU,CAACwB,mBAAmB,CAACL,UAAU,CAAC;EAC9D,IAAIM,eAAe,GAAGzB,UAAU,CAAC0B,qBAAqB,CAACL,YAAY,IAAI,CAAC,EAAEE,aAAa,CAAC;EACxF,IAAIK,UAAU,GAAG5B,UAAU,CAAC2B,YAAY,CAACV,UAAU,EAAE,CAACM,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGE,eAAe,CAAC,CAAC,CAAC,EAAE,CAACF,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGE,eAAe,CAAC,CAAC,CAAC,EAAEF,aAAa,CAAC,CAAC,CAAC,EAAEA,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,EAAED,gBAAgB,CAAC;EACxMM,UAAU,CAACC,mBAAmB,GAAGT,YAAY,IAAI,IAAI,IAAIU,KAAK,CAACV,YAAY,CAAC,GAAG,KAAK,CAAC,GAAG,CAACA,YAAY,GAAGW,IAAI,CAACC,EAAE,GAAG,GAAG,IAAI,CAAC;EAC1HJ,UAAU,CAACd,IAAI,GAAGA,IAAI;EACtB,OAAOc,UAAU;AACnB;AACA,SAASK,UAAUA,CAACC,MAAM,EAAE;EAC1B,IAAIC,IAAI,GAAG,IAAIlC,UAAU,CAAC;IACxBa,IAAI,EAAE,MAAM;IACZsB,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACFC,aAAa,CAACF,IAAI,CAACG,KAAK,EAAEJ,MAAM,CAAC;EACjC,OAAOC,IAAI;AACb;AACA,SAASE,aAAaA,CAACE,WAAW,EAAEL,MAAM,EAAE;EAC1CK,WAAW,CAACC,EAAE,GAAGN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7BK,WAAW,CAACE,EAAE,GAAGP,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7BK,WAAW,CAACG,EAAE,GAAGR,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7BK,WAAW,CAACI,EAAE,GAAGT,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7BK,WAAW,CAACK,OAAO,GAAG,CAAC;EACvB,IAAIC,GAAG,GAAGX,MAAM,CAAC,CAAC,CAAC;EACnB,IAAIW,GAAG,EAAE;IACPN,WAAW,CAACO,IAAI,GAAGD,GAAG,CAAC,CAAC,CAAC;IACzBN,WAAW,CAACQ,IAAI,GAAGF,GAAG,CAAC,CAAC,CAAC;EAC3B,CAAC,MAAM;IACLN,WAAW,CAACO,IAAI,GAAGE,GAAG;IACtBT,WAAW,CAACQ,IAAI,GAAGC,GAAG;EACxB;AACF;AACA,IAAIC,IAAI,GAAG,aAAa,UAAUC,MAAM,EAAE;EACxCtD,SAAS,CAACqD,IAAI,EAAEC,MAAM,CAAC;EACvB,SAASD,IAAIA,CAAClC,QAAQ,EAAEC,GAAG,EAAEmC,WAAW,EAAE;IACxC,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,WAAW,CAACvC,QAAQ,EAAEC,GAAG,EAAEmC,WAAW,CAAC;IAC7C,OAAOC,KAAK;EACd;EACAH,IAAI,CAACM,SAAS,CAACD,WAAW,GAAG,UAAUvC,QAAQ,EAAEC,GAAG,EAAEmC,WAAW,EAAE;IACjE,IAAIK,WAAW,GAAGzC,QAAQ,CAAC0C,SAAS;IACpC,IAAIC,UAAU,GAAG3C,QAAQ,CAAC4C,aAAa,CAAC3C,GAAG,CAAC;IAC5C,IAAImB,IAAI,GAAGF,UAAU,CAACyB,UAAU,CAAC;IACjCvB,IAAI,CAACG,KAAK,CAACM,OAAO,GAAG,CAAC;IACtB1C,OAAO,CAAC0D,SAAS,CAACzB,IAAI,EAAE;MACtBG,KAAK,EAAE;QACLM,OAAO,EAAE;MACX;IACF,CAAC,EAAEY,WAAW,EAAExC,GAAG,CAAC;IACpB,IAAI,CAAC6C,GAAG,CAAC1B,IAAI,CAAC;IACdrC,IAAI,CAACY,iBAAiB,EAAE,UAAUE,cAAc,EAAE;MAChD,IAAIkD,MAAM,GAAGnC,YAAY,CAACf,cAAc,EAAEG,QAAQ,EAAEC,GAAG,CAAC;MACxD;MACA;MACA;MACA,IAAI,CAAC6C,GAAG,CAACC,MAAM,CAAC;MAChB,IAAI,CAACnD,iBAAiB,CAACC,cAAc,CAAC,CAAC,GAAGC,mBAAmB,CAACD,cAAc,EAAEG,QAAQ,EAAEC,GAAG,CAAC;IAC9F,CAAC,EAAE,IAAI,CAAC;IACR,IAAI,CAAC+C,gBAAgB,CAAChD,QAAQ,EAAEC,GAAG,EAAEmC,WAAW,CAAC;EACnD,CAAC;EACD;EACAF,IAAI,CAACM,SAAS,CAACS,UAAU,GAAG,UAAUjD,QAAQ,EAAEC,GAAG,EAAEmC,WAAW,EAAE;IAChE,IAAIK,WAAW,GAAGzC,QAAQ,CAAC0C,SAAS;IACpC,IAAItB,IAAI,GAAG,IAAI,CAAC8B,WAAW,CAAC,MAAM,CAAC;IACnC,IAAIP,UAAU,GAAG3C,QAAQ,CAAC4C,aAAa,CAAC3C,GAAG,CAAC;IAC5C,IAAIkD,MAAM,GAAG;MACX5B,KAAK,EAAE,CAAC;IACV,CAAC;IACDD,aAAa,CAAC6B,MAAM,CAAC5B,KAAK,EAAEoB,UAAU,CAAC;IACvCxD,OAAO,CAACiE,WAAW,CAAChC,IAAI,EAAE+B,MAAM,EAAEV,WAAW,EAAExC,GAAG,CAAC;IACnDlB,IAAI,CAACY,iBAAiB,EAAE,UAAUE,cAAc,EAAE;MAChD,IAAIK,UAAU,GAAGJ,mBAAmB,CAACD,cAAc,EAAEG,QAAQ,EAAEC,GAAG,CAAC;MACnE,IAAIoD,GAAG,GAAGzD,iBAAiB,CAACC,cAAc,CAAC;MAC3C;MACA,IAAI,IAAI,CAACwD,GAAG,CAAC,KAAKnD,UAAU,EAAE;QAC5B,IAAI,CAACoD,MAAM,CAAC,IAAI,CAACJ,WAAW,CAACrD,cAAc,CAAC,CAAC;QAC7C,IAAIkD,MAAM,GAAGnC,YAAY,CAACf,cAAc,EAAEG,QAAQ,EAAEC,GAAG,CAAC;QACxD,IAAI,CAAC6C,GAAG,CAACC,MAAM,CAAC;MAClB;MACA,IAAI,CAACM,GAAG,CAAC,GAAGnD,UAAU;IACxB,CAAC,EAAE,IAAI,CAAC;IACR,IAAI,CAAC8C,gBAAgB,CAAChD,QAAQ,EAAEC,GAAG,EAAEmC,WAAW,CAAC;EACnD,CAAC;EACD;EACAF,IAAI,CAACM,SAAS,CAACe,WAAW,GAAG,YAAY;IACvC,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;EACxB,CAAC;EACDtB,IAAI,CAACM,SAAS,CAACQ,gBAAgB,GAAG,UAAUhD,QAAQ,EAAEC,GAAG,EAAEmC,WAAW,EAAE;IACtE,IAAIK,WAAW,GAAGzC,QAAQ,CAAC0C,SAAS;IACpC,IAAItB,IAAI,GAAG,IAAI,CAAC8B,WAAW,CAAC,MAAM,CAAC;IACnC,IAAIO,iBAAiB,GAAGrB,WAAW,IAAIA,WAAW,CAACqB,iBAAiB;IACpE,IAAIC,aAAa,GAAGtB,WAAW,IAAIA,WAAW,CAACsB,aAAa;IAC5D,IAAIC,eAAe,GAAGvB,WAAW,IAAIA,WAAW,CAACuB,eAAe;IAChE,IAAIC,iBAAiB,GAAGxB,WAAW,IAAIA,WAAW,CAACwB,iBAAiB;IACpE,IAAIC,gBAAgB,GAAGzB,WAAW,IAAIA,WAAW,CAACyB,gBAAgB;IAClE,IAAIC,KAAK,GAAG1B,WAAW,IAAIA,WAAW,CAAC0B,KAAK;IAC5C,IAAIC,SAAS,GAAG3B,WAAW,IAAIA,WAAW,CAAC2B,SAAS;IACpD;IACA,IAAI,CAAC3B,WAAW,IAAIpC,QAAQ,CAACgE,aAAa,EAAE;MAC1C,IAAIC,SAAS,GAAGjE,QAAQ,CAACkE,YAAY,CAACjE,GAAG,CAAC;MAC1C,IAAIkE,aAAa,GAAGF,SAAS,CAACG,QAAQ,CAAC,UAAU,CAAC;MAClDX,iBAAiB,GAAGU,aAAa,CAACC,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;MACtEX,aAAa,GAAGO,SAAS,CAACG,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;MACxEV,eAAe,GAAGM,SAAS,CAACG,QAAQ,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;MAC5ER,gBAAgB,GAAGM,aAAa,CAACG,GAAG,CAAC,UAAU,CAAC;MAChDR,KAAK,GAAGK,aAAa,CAACG,GAAG,CAAC,OAAO,CAAC;MAClCP,SAAS,GAAGI,aAAa,CAACG,GAAG,CAAC,WAAW,CAAC;MAC1CV,iBAAiB,GAAGpE,oBAAoB,CAACyE,SAAS,CAAC;IACrD;IACA,IAAIM,SAAS,GAAGvE,QAAQ,CAACG,aAAa,CAACF,GAAG,EAAE,OAAO,CAAC;IACpD,IAAIuE,WAAW,GAAGD,SAAS,CAACE,MAAM;IAClCrD,IAAI,CAACsD,QAAQ,CAACH,SAAS,CAAC;IACxBnD,IAAI,CAACuD,KAAK,CAACC,IAAI,GAAG,IAAI;IACtBxD,IAAI,CAACuD,KAAK,CAACE,aAAa,GAAG,IAAI;IAC/BzD,IAAI,CAAC0D,WAAW,CAAC,UAAU,CAAC,CAACH,KAAK,GAAGlB,iBAAiB;IACtDrC,IAAI,CAAC0D,WAAW,CAAC,MAAM,CAAC,CAACH,KAAK,GAAGjB,aAAa;IAC9CtC,IAAI,CAAC0D,WAAW,CAAC,QAAQ,CAAC,CAACH,KAAK,GAAGhB,eAAe;IAClD;IACA5E,IAAI,CAACY,iBAAiB,EAAE,UAAUE,cAAc,EAAE;MAChD,IAAIkD,MAAM,GAAG,IAAI,CAACG,WAAW,CAACrD,cAAc,CAAC;MAC7C,IAAIkD,MAAM,EAAE;QACV;QACAA,MAAM,CAACgC,QAAQ,CAACP,WAAW,CAAC;QAC5BzB,MAAM,CAAC4B,KAAK,CAACK,OAAO,GAAGT,SAAS,CAACS,OAAO;QACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1F,cAAc,CAAC2F,MAAM,EAAED,CAAC,EAAE,EAAE;UAC9C,IAAIE,SAAS,GAAG5F,cAAc,CAAC0F,CAAC,CAAC;UACjC,IAAIG,SAAS,GAAGhE,IAAI,CAACiE,QAAQ,CAACF,SAAS,CAAC;UACxC,IAAIC,SAAS,EAAE;YACb,IAAIE,cAAc,GAAGF,SAAS,CAACT,KAAK,IAAI,CAAC,CAAC;YAC1C,IAAIY,KAAK,GAAGxC,MAAM,CAAC+B,WAAW,CAACK,SAAS,CAAC;YACzC,IAAIK,UAAU,GAAGD,KAAK,CAACZ,KAAK,KAAKY,KAAK,CAACZ,KAAK,GAAG,CAAC,CAAC,CAAC;YAClD,IAAIW,cAAc,CAACb,MAAM,IAAI,IAAI,EAAE;cACjCe,UAAU,CAACzC,MAAM,CAAC0C,cAAc,GAAG,QAAQ,GAAG,MAAM,CAAC,GAAGH,cAAc,CAACb,MAAM;YAC/E;YACA,IAAIa,cAAc,CAACN,OAAO,IAAI,IAAI,EAAE;cAClCQ,UAAU,CAACR,OAAO,GAAGM,cAAc,CAACN,OAAO;YAC7C;UACF;QACF;QACAjC,MAAM,CAAC2C,UAAU,CAAC,CAAC;MACrB;IACF,CAAC,EAAE,IAAI,CAAC;IACR,IAAIC,MAAM,GAAGlD,WAAW,CAACmD,WAAW,CAAC3F,GAAG,CAAC;IACzCR,aAAa,CAAC,IAAI,EAAEmE,iBAAiB,EAAE;MACrCiC,cAAc,EAAE5F,GAAG;MACnB6F,YAAY,EAAE;QACZC,iBAAiB,EAAE,SAAAA,CAAUC,SAAS,EAAEb,SAAS,EAAE;UACjD,OAAO1C,WAAW,CAACsD,iBAAiB,CAACC,SAAS,EAAEb,SAAS,EAAEnF,QAAQ,CAACiG,QAAQ,CAAC;QAC/E;MACF,CAAC;MACDC,YAAY,EAAE1B,WAAW,IAAI,MAAM;MACnC2B,cAAc,EAAE5B,SAAS,CAACS,OAAO;MACjCoB,WAAW,EAAE,CAACT,MAAM,IAAI,IAAI,GAAG3F,QAAQ,CAACqG,OAAO,CAACpG,GAAG,CAAC,GAAGqG,QAAQ,CAACX,MAAM,CAAC,GAAGjG,KAAK,CAACiG,MAAM,CAAC,GAAGA,MAAM,IAAI;IACtG,CAAC,CAAC;IACF,IAAIY,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACjC;IACA;IACA,IAAID,KAAK,EAAE;MACT,IAAIE,gBAAgB,GAAG7C,iBAAiB,CAAC8C,MAAM;MAC/CH,KAAK,CAACI,OAAO,GAAGJ,KAAK,CAAC5B,KAAK,CAACiC,KAAK;MACjCL,KAAK,CAACM,eAAe,GAAGN,KAAK,CAAC5B,KAAK,CAACmC,aAAa;MACjD;MACAP,KAAK,CAACQ,UAAU,GAAGN,gBAAgB,CAACnC,GAAG,CAAC,UAAU,CAAC,IAAI,QAAQ;MAC/D,IAAI0C,QAAQ,GAAGP,gBAAgB,CAACnC,GAAG,CAAC,UAAU,CAAC;MAC/C,IAAI,CAACxF,OAAO,CAACkI,QAAQ,CAAC,EAAE;QACtBA,QAAQ,GAAG,CAACA,QAAQ,EAAEA,QAAQ,CAAC;MACjC;MACAT,KAAK,CAACU,eAAe,GAAGD,QAAQ;IAClC;IACA,IAAI,CAACE,aAAa,CAAC;MACjBC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,KAAK,CAAC;IAChB,CAAC,CAAC;IACFjI,mBAAmB,CAAC,IAAI,EAAE0E,KAAK,EAAEC,SAAS,EAAEF,gBAAgB,CAAC;EAC/D,CAAC;EACD3B,IAAI,CAACM,SAAS,CAAC8E,SAAS,GAAG,YAAY;IACrCjI,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EACD6C,IAAI,CAACM,SAAS,CAAC+E,QAAQ,GAAG,YAAY;IACpCjI,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EACD4C,IAAI,CAACM,SAAS,CAACgF,YAAY,GAAG,UAAUxH,QAAQ,EAAEC,GAAG,EAAE;IACrD,IAAI,CAACqB,aAAa,CAACtB,QAAQ,CAAC4C,aAAa,CAAC3C,GAAG,CAAC,CAAC;EACjD,CAAC;EACDiC,IAAI,CAACM,SAAS,CAAClB,aAAa,GAAG,UAAUH,MAAM,EAAE;IAC/C,IAAIsG,QAAQ,GAAG,IAAI,CAACvE,WAAW,CAAC,MAAM,CAAC;IACvC5B,aAAa,CAACmG,QAAQ,CAAClG,KAAK,EAAEJ,MAAM,CAAC;IACrCsG,QAAQ,CAACC,KAAK,CAAC,CAAC;EAClB,CAAC;EACDxF,IAAI,CAACM,SAAS,CAACmF,YAAY,GAAG,YAAY;IACxC,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,UAAU,GAAGD,SAAS,CAAC1E,WAAW,CAAC,YAAY,CAAC;IACpD,IAAI4E,QAAQ,GAAGF,SAAS,CAAC1E,WAAW,CAAC,UAAU,CAAC;IAChD,IAAIqD,KAAK,GAAGqB,SAAS,CAACpB,cAAc,CAAC,CAAC;IACtC;IACA,IAAI,CAACqB,UAAU,IAAI,CAACC,QAAQ,KAAK,CAACvB,KAAK,IAAIA,KAAK,CAACwB,MAAM,CAAC,EAAE;MACxD;IACF;IACA,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,UAAU,GAAG,IAAI,CAACC,MAAM;IAC5B,OAAOD,UAAU,EAAE;MACjB,IAAIA,UAAU,CAACE,MAAM,EAAE;QACrBH,QAAQ,IAAIC,UAAU,CAACE,MAAM;MAC/B;MACAF,UAAU,GAAGA,UAAU,CAACC,MAAM;IAChC;IACA,IAAI9G,IAAI,GAAGwG,SAAS,CAAC1E,WAAW,CAAC,MAAM,CAAC;IACxC;IACA;IACA,IAAI,CAAC,IAAI,CAACkF,OAAO,IAAI,CAAChH,IAAI,CAACgH,OAAO,EAAE;MAClC;IACF;IACA,IAAIvG,OAAO,GAAGT,IAAI,CAACG,KAAK,CAACM,OAAO;IAChC,IAAIwG,OAAO,GAAGjH,IAAI,CAACkH,OAAO,CAAC,CAAC,CAAC;IAC7B,IAAIC,KAAK,GAAGnH,IAAI,CAACkH,OAAO,CAACzG,OAAO,CAAC;IACjC,IAAI2G,CAAC,GAAGxJ,MAAM,CAACyJ,GAAG,CAAC,EAAE,EAAEF,KAAK,EAAEF,OAAO,CAAC;IACtCrJ,MAAM,CAAC0J,SAAS,CAACF,CAAC,EAAEA,CAAC,CAAC;IACtB,SAASG,iBAAiBA,CAAC5F,MAAM,EAAElB,OAAO,EAAE;MAC1C;MACA;MACA;MACA;MACA,IAAI+G,iBAAiB,GAAG7F,MAAM,CAACjC,mBAAmB;MAClD,IAAI8H,iBAAiB,IAAI,IAAI,EAAE;QAC7B,IAAIC,OAAO,GAAGzH,IAAI,CAAC0H,SAAS,CAACjH,OAAO,CAAC;QACrCkB,MAAM,CAACgG,IAAI,CAAC,UAAU,EAAE,CAAClH,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIb,IAAI,CAACC,EAAE,GAAG,CAAC,GAAGD,IAAI,CAACgI,KAAK,CAACH,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MACtG,CAAC,MAAM;QACL9F,MAAM,CAACgG,IAAI,CAAC,UAAU,EAAEH,iBAAiB,CAAC;MAC5C;IACF;IACA,IAAIf,UAAU,EAAE;MACdA,UAAU,CAACoB,WAAW,CAACZ,OAAO,CAAC;MAC/BM,iBAAiB,CAACd,UAAU,EAAE,CAAC,CAAC;MAChCA,UAAU,CAACM,MAAM,GAAGN,UAAU,CAACqB,MAAM,GAAGlB,QAAQ,GAAGnG,OAAO;MAC1DgG,UAAU,CAACnC,UAAU,CAAC,CAAC;IACzB;IACA,IAAIoC,QAAQ,EAAE;MACZA,QAAQ,CAACmB,WAAW,CAACV,KAAK,CAAC;MAC3BI,iBAAiB,CAACb,QAAQ,EAAE,CAAC,CAAC;MAC9BA,QAAQ,CAACK,MAAM,GAAGL,QAAQ,CAACoB,MAAM,GAAGlB,QAAQ,GAAGnG,OAAO;MACtDiG,QAAQ,CAACpC,UAAU,CAAC,CAAC;IACvB;IACA,IAAIa,KAAK,IAAI,CAACA,KAAK,CAACwB,MAAM,EAAE;MAC1BxB,KAAK,CAAC4C,CAAC,GAAG5C,KAAK,CAAC6C,CAAC,GAAG,CAAC;MACrB7C,KAAK,CAAC8C,OAAO,GAAG9C,KAAK,CAAC+C,OAAO,GAAG,CAAC;MACjC,IAAIC,SAAS,GAAG,KAAK,CAAC;MACtB,IAAIC,iBAAiB,GAAG,KAAK,CAAC;MAC9B,IAAIxC,QAAQ,GAAGT,KAAK,CAACU,eAAe;MACpC,IAAIwC,SAAS,GAAGzC,QAAQ,CAAC,CAAC,CAAC,GAAGgB,QAAQ;MACtC,IAAI0B,SAAS,GAAG1C,QAAQ,CAAC,CAAC,CAAC,GAAGgB,QAAQ;MACtC,IAAI2B,WAAW,GAAG9H,OAAO,GAAG,CAAC;MAC7B,IAAIgH,OAAO,GAAGzH,IAAI,CAAC0H,SAAS,CAACa,WAAW,CAAC;MACzC,IAAIC,CAAC,GAAG,CAACf,OAAO,CAAC,CAAC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC,CAAC,CAAC;MACjC,IAAIgB,EAAE,GAAGzI,IAAI,CAACkH,OAAO,CAACqB,WAAW,CAAC;MAClC,IAAIC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;QACZA,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,CAAC,CAAC,CAAC,CAAC;QACZA,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,CAAC,CAAC,CAAC,CAAC;MACd;MACA,IAAIE,GAAG,GAAGjB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACjC,IAAItC,KAAK,CAACQ,UAAU,KAAK,OAAO,IAAIR,KAAK,CAACQ,UAAU,KAAK,KAAK,EAAE;QAC9D,IAAIgD,QAAQ,GAAG,CAAC/I,IAAI,CAACgI,KAAK,CAACH,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;QAClD,IAAIN,KAAK,CAAC,CAAC,CAAC,GAAGF,OAAO,CAAC,CAAC,CAAC,EAAE;UACzB0B,QAAQ,GAAG/I,IAAI,CAACC,EAAE,GAAG8I,QAAQ;QAC/B;QACAxD,KAAK,CAACwD,QAAQ,GAAGA,QAAQ;MAC3B;MACA,IAAIC,EAAE,GAAG,KAAK,CAAC;MACf,QAAQzD,KAAK,CAACQ,UAAU;QACtB,KAAK,gBAAgB;QACrB,KAAK,iBAAiB;QACtB,KAAK,cAAc;QACnB,KAAK,QAAQ;UACXiD,EAAE,GAAG,CAACN,SAAS;UACfF,iBAAiB,GAAG,QAAQ;UAC5B;QACF,KAAK,mBAAmB;QACxB,KAAK,oBAAoB;QACzB,KAAK,iBAAiB;UACpBQ,EAAE,GAAGN,SAAS;UACdF,iBAAiB,GAAG,KAAK;UACzB;QACF;UACEQ,EAAE,GAAG,CAAC;UACNR,iBAAiB,GAAG,QAAQ;MAChC;MACA,QAAQjD,KAAK,CAACQ,UAAU;QACtB,KAAK,KAAK;UACRR,KAAK,CAAC4C,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAC,GAAGiB,SAAS,GAAGlB,KAAK,CAAC,CAAC,CAAC;UACrChC,KAAK,CAAC6C,CAAC,GAAGZ,CAAC,CAAC,CAAC,CAAC,GAAGkB,SAAS,GAAGnB,KAAK,CAAC,CAAC,CAAC;UACrCgB,SAAS,GAAGf,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,OAAO,GAAG,QAAQ;UAClEgB,iBAAiB,GAAGhB,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,QAAQ,GAAG,QAAQ;UAC1E;QACF,KAAK,OAAO;UACVjC,KAAK,CAAC4C,CAAC,GAAG,CAACX,CAAC,CAAC,CAAC,CAAC,GAAGiB,SAAS,GAAGpB,OAAO,CAAC,CAAC,CAAC;UACxC9B,KAAK,CAAC6C,CAAC,GAAG,CAACZ,CAAC,CAAC,CAAC,CAAC,GAAGkB,SAAS,GAAGrB,OAAO,CAAC,CAAC,CAAC;UACxCkB,SAAS,GAAGf,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,OAAO,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,MAAM,GAAG,QAAQ;UAClEgB,iBAAiB,GAAGhB,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,QAAQ;UAC1E;QACF,KAAK,gBAAgB;QACrB,KAAK,aAAa;QAClB,KAAK,mBAAmB;UACtBjC,KAAK,CAAC4C,CAAC,GAAGM,SAAS,GAAGK,GAAG,GAAGzB,OAAO,CAAC,CAAC,CAAC;UACtC9B,KAAK,CAAC6C,CAAC,GAAGf,OAAO,CAAC,CAAC,CAAC,GAAG2B,EAAE;UACzBT,SAAS,GAAGV,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;UAC7CtC,KAAK,CAAC8C,OAAO,GAAG,CAACI,SAAS,GAAGK,GAAG;UAChCvD,KAAK,CAAC+C,OAAO,GAAG,CAACU,EAAE;UACnB;QACF,KAAK,iBAAiB;QACtB,KAAK,cAAc;QACnB,KAAK,oBAAoB;QACzB,KAAK,QAAQ;UACXzD,KAAK,CAAC4C,CAAC,GAAGU,EAAE,CAAC,CAAC,CAAC;UACftD,KAAK,CAAC6C,CAAC,GAAGS,EAAE,CAAC,CAAC,CAAC,GAAGG,EAAE;UACpBT,SAAS,GAAG,QAAQ;UACpBhD,KAAK,CAAC+C,OAAO,GAAG,CAACU,EAAE;UACnB;QACF,KAAK,cAAc;QACnB,KAAK,WAAW;QAChB,KAAK,iBAAiB;UACpBzD,KAAK,CAAC4C,CAAC,GAAG,CAACM,SAAS,GAAGK,GAAG,GAAGvB,KAAK,CAAC,CAAC,CAAC;UACrChC,KAAK,CAAC6C,CAAC,GAAGb,KAAK,CAAC,CAAC,CAAC,GAAGyB,EAAE;UACvBT,SAAS,GAAGV,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,GAAG,MAAM;UAC9CtC,KAAK,CAAC8C,OAAO,GAAGI,SAAS,GAAGK,GAAG;UAC/BvD,KAAK,CAAC+C,OAAO,GAAG,CAACU,EAAE;UACnB;MACJ;MACAzD,KAAK,CAAC4B,MAAM,GAAG5B,KAAK,CAAC2C,MAAM,GAAGlB,QAAQ;MACtCzB,KAAK,CAAC0D,QAAQ,CAAC;QACb;QACAnD,aAAa,EAAEP,KAAK,CAACM,eAAe,IAAI2C,iBAAiB;QACzD5C,KAAK,EAAEL,KAAK,CAACI,OAAO,IAAI4C;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC;EACD,OAAOrH,IAAI;AACb,CAAC,CAAC/C,OAAO,CAAC+K,KAAK,CAAC;AAChB,eAAehI,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}