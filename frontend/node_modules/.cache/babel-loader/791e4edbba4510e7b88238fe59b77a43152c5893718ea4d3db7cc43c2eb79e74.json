{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"id\", \"inputElement\", \"autoFocus\", \"autoComplete\", \"editable\", \"activeDescendantId\", \"value\", \"open\", \"attrs\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { warning } from \"rc-util/es/warning\";\nimport composeProps from \"rc-util/es/composeProps\";\nvar Input = function Input(props, ref) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    inputElement = props.inputElement,\n    autoFocus = props.autoFocus,\n    autoComplete = props.autoComplete,\n    editable = props.editable,\n    activeDescendantId = props.activeDescendantId,\n    value = props.value,\n    open = props.open,\n    attrs = props.attrs,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var inputNode = inputElement || /*#__PURE__*/React.createElement(\"input\", null);\n  var _inputNode = inputNode,\n    originRef = _inputNode.ref,\n    originProps = _inputNode.props;\n  warning(!('maxLength' in inputNode.props), \"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled.\");\n  inputNode = /*#__PURE__*/React.cloneElement(inputNode, _objectSpread(_objectSpread(_objectSpread({\n    type: 'search'\n  }, composeProps(restProps, originProps, true)), {}, {\n    // Override over origin props\n    id: id,\n    ref: composeRef(ref, originRef),\n    autoComplete: autoComplete || 'off',\n    autoFocus: autoFocus,\n    className: classNames(\"\".concat(prefixCls, \"-selection-search-input\"), originProps === null || originProps === void 0 ? void 0 : originProps.className),\n    role: 'combobox',\n    'aria-expanded': open || false,\n    'aria-haspopup': 'listbox',\n    'aria-owns': \"\".concat(id, \"_list\"),\n    'aria-autocomplete': 'list',\n    'aria-controls': \"\".concat(id, \"_list\"),\n    'aria-activedescendant': open ? activeDescendantId : undefined\n  }, attrs), {}, {\n    value: editable ? value : '',\n    readOnly: !editable,\n    unselectable: !editable ? 'on' : null,\n    style: _objectSpread(_objectSpread({}, originProps.style), {}, {\n      opacity: editable ? null : 0\n    })\n  }));\n  return inputNode;\n};\nvar RefInput = /*#__PURE__*/React.forwardRef(Input);\nif (process.env.NODE_ENV !== 'production') {\n  RefInput.displayName = 'Input';\n}\nexport default RefInput;", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutProperties", "_excluded", "React", "classNames", "composeRef", "warning", "composeProps", "Input", "props", "ref", "prefixCls", "id", "inputElement", "autoFocus", "autoComplete", "editable", "activeDescendantId", "value", "open", "attrs", "restProps", "inputNode", "createElement", "_inputNode", "originRef", "originProps", "cloneElement", "type", "className", "concat", "role", "undefined", "readOnly", "unselectable", "style", "opacity", "RefInput", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/node_modules/rc-select/es/Selector/Input.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"id\", \"inputElement\", \"autoFocus\", \"autoComplete\", \"editable\", \"activeDescendantId\", \"value\", \"open\", \"attrs\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { warning } from \"rc-util/es/warning\";\nimport composeProps from \"rc-util/es/composeProps\";\nvar Input = function Input(props, ref) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    inputElement = props.inputElement,\n    autoFocus = props.autoFocus,\n    autoComplete = props.autoComplete,\n    editable = props.editable,\n    activeDescendantId = props.activeDescendantId,\n    value = props.value,\n    open = props.open,\n    attrs = props.attrs,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var inputNode = inputElement || /*#__PURE__*/React.createElement(\"input\", null);\n  var _inputNode = inputNode,\n    originRef = _inputNode.ref,\n    originProps = _inputNode.props;\n  warning(!('maxLength' in inputNode.props), \"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled.\");\n  inputNode = /*#__PURE__*/React.cloneElement(inputNode, _objectSpread(_objectSpread(_objectSpread({\n    type: 'search'\n  }, composeProps(restProps, originProps, true)), {}, {\n    // Override over origin props\n    id: id,\n    ref: composeRef(ref, originRef),\n    autoComplete: autoComplete || 'off',\n    autoFocus: autoFocus,\n    className: classNames(\"\".concat(prefixCls, \"-selection-search-input\"), originProps === null || originProps === void 0 ? void 0 : originProps.className),\n    role: 'combobox',\n    'aria-expanded': open || false,\n    'aria-haspopup': 'listbox',\n    'aria-owns': \"\".concat(id, \"_list\"),\n    'aria-autocomplete': 'list',\n    'aria-controls': \"\".concat(id, \"_list\"),\n    'aria-activedescendant': open ? activeDescendantId : undefined\n  }, attrs), {}, {\n    value: editable ? value : '',\n    readOnly: !editable,\n    unselectable: !editable ? 'on' : null,\n    style: _objectSpread(_objectSpread({}, originProps.style), {}, {\n      opacity: editable ? null : 0\n    })\n  }));\n  return inputNode;\n};\nvar RefInput = /*#__PURE__*/React.forwardRef(Input);\nif (process.env.NODE_ENV !== 'production') {\n  RefInput.displayName = 'Input';\n}\nexport default RefInput;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,cAAc,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,oBAAoB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AAC5I,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,YAAY,MAAM,yBAAyB;AAClD,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrC,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,EAAE,GAAGH,KAAK,CAACG,EAAE;IACbC,YAAY,GAAGJ,KAAK,CAACI,YAAY;IACjCC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,YAAY,GAAGN,KAAK,CAACM,YAAY;IACjCC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,kBAAkB,GAAGR,KAAK,CAACQ,kBAAkB;IAC7CC,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,IAAI,GAAGV,KAAK,CAACU,IAAI;IACjBC,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBC,SAAS,GAAGpB,wBAAwB,CAACQ,KAAK,EAAEP,SAAS,CAAC;EACxD,IAAIoB,SAAS,GAAGT,YAAY,IAAI,aAAaV,KAAK,CAACoB,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC;EAC/E,IAAIC,UAAU,GAAGF,SAAS;IACxBG,SAAS,GAAGD,UAAU,CAACd,GAAG;IAC1BgB,WAAW,GAAGF,UAAU,CAACf,KAAK;EAChCH,OAAO,CAAC,EAAE,WAAW,IAAIgB,SAAS,CAACb,KAAK,CAAC,EAAE,uGAAuG,CAAC;EACnJa,SAAS,GAAG,aAAanB,KAAK,CAACwB,YAAY,CAACL,SAAS,EAAEtB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;IAC/F4B,IAAI,EAAE;EACR,CAAC,EAAErB,YAAY,CAACc,SAAS,EAAEK,WAAW,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAClD;IACAd,EAAE,EAAEA,EAAE;IACNF,GAAG,EAAEL,UAAU,CAACK,GAAG,EAAEe,SAAS,CAAC;IAC/BV,YAAY,EAAEA,YAAY,IAAI,KAAK;IACnCD,SAAS,EAAEA,SAAS;IACpBe,SAAS,EAAEzB,UAAU,CAAC,EAAE,CAAC0B,MAAM,CAACnB,SAAS,EAAE,yBAAyB,CAAC,EAAEe,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACG,SAAS,CAAC;IACvJE,IAAI,EAAE,UAAU;IAChB,eAAe,EAAEZ,IAAI,IAAI,KAAK;IAC9B,eAAe,EAAE,SAAS;IAC1B,WAAW,EAAE,EAAE,CAACW,MAAM,CAAClB,EAAE,EAAE,OAAO,CAAC;IACnC,mBAAmB,EAAE,MAAM;IAC3B,eAAe,EAAE,EAAE,CAACkB,MAAM,CAAClB,EAAE,EAAE,OAAO,CAAC;IACvC,uBAAuB,EAAEO,IAAI,GAAGF,kBAAkB,GAAGe;EACvD,CAAC,EAAEZ,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IACbF,KAAK,EAAEF,QAAQ,GAAGE,KAAK,GAAG,EAAE;IAC5Be,QAAQ,EAAE,CAACjB,QAAQ;IACnBkB,YAAY,EAAE,CAAClB,QAAQ,GAAG,IAAI,GAAG,IAAI;IACrCmB,KAAK,EAAEnC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0B,WAAW,CAACS,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC7DC,OAAO,EAAEpB,QAAQ,GAAG,IAAI,GAAG;IAC7B,CAAC;EACH,CAAC,CAAC,CAAC;EACH,OAAOM,SAAS;AAClB,CAAC;AACD,IAAIe,QAAQ,GAAG,aAAalC,KAAK,CAACmC,UAAU,CAAC9B,KAAK,CAAC;AACnD,IAAI+B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,QAAQ,CAACK,WAAW,GAAG,OAAO;AAChC;AACA,eAAeL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}