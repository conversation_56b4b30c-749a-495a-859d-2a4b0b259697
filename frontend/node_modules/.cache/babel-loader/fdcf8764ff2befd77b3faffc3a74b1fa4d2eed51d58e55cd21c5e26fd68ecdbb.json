{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { createSymbol, normalizeSymbolOffset, normalizeSymbolSize } from '../../util/symbol.js';\nimport { Group } from '../../util/graphic.js';\nimport { enterEmphasis, leaveEmphasis, toggleHoverEmphasis } from '../../util/states.js';\nimport SymbolClz from './Symbol.js';\nfunction updateRipplePath(rippleGroup, effectCfg) {\n  var color = effectCfg.rippleEffectColor || effectCfg.color;\n  rippleGroup.eachChild(function (ripplePath) {\n    ripplePath.attr({\n      z: effectCfg.z,\n      zlevel: effectCfg.zlevel,\n      style: {\n        stroke: effectCfg.brushType === 'stroke' ? color : null,\n        fill: effectCfg.brushType === 'fill' ? color : null\n      }\n    });\n  });\n}\nvar EffectSymbol = /** @class */function (_super) {\n  __extends(EffectSymbol, _super);\n  function EffectSymbol(data, idx) {\n    var _this = _super.call(this) || this;\n    var symbol = new SymbolClz(data, idx);\n    var rippleGroup = new Group();\n    _this.add(symbol);\n    _this.add(rippleGroup);\n    _this.updateData(data, idx);\n    return _this;\n  }\n  EffectSymbol.prototype.stopEffectAnimation = function () {\n    this.childAt(1).removeAll();\n  };\n  EffectSymbol.prototype.startEffectAnimation = function (effectCfg) {\n    var symbolType = effectCfg.symbolType;\n    var color = effectCfg.color;\n    var rippleNumber = effectCfg.rippleNumber;\n    var rippleGroup = this.childAt(1);\n    for (var i = 0; i < rippleNumber; i++) {\n      // If width/height are set too small (e.g., set to 1) on ios10\n      // and macOS Sierra, a circle stroke become a rect, no matter what\n      // the scale is set. So we set width/height as 2. See #4136.\n      var ripplePath = createSymbol(symbolType, -1, -1, 2, 2, color);\n      ripplePath.attr({\n        style: {\n          strokeNoScale: true\n        },\n        z2: 99,\n        silent: true,\n        scaleX: 0.5,\n        scaleY: 0.5\n      });\n      var delay = -i / rippleNumber * effectCfg.period + effectCfg.effectOffset;\n      ripplePath.animate('', true).when(effectCfg.period, {\n        scaleX: effectCfg.rippleScale / 2,\n        scaleY: effectCfg.rippleScale / 2\n      }).delay(delay).start();\n      ripplePath.animateStyle(true).when(effectCfg.period, {\n        opacity: 0\n      }).delay(delay).start();\n      rippleGroup.add(ripplePath);\n    }\n    updateRipplePath(rippleGroup, effectCfg);\n  };\n  /**\r\n   * Update effect symbol\r\n   */\n  EffectSymbol.prototype.updateEffectAnimation = function (effectCfg) {\n    var oldEffectCfg = this._effectCfg;\n    var rippleGroup = this.childAt(1);\n    // Must reinitialize effect if following configuration changed\n    var DIFFICULT_PROPS = ['symbolType', 'period', 'rippleScale', 'rippleNumber'];\n    for (var i = 0; i < DIFFICULT_PROPS.length; i++) {\n      var propName = DIFFICULT_PROPS[i];\n      if (oldEffectCfg[propName] !== effectCfg[propName]) {\n        this.stopEffectAnimation();\n        this.startEffectAnimation(effectCfg);\n        return;\n      }\n    }\n    updateRipplePath(rippleGroup, effectCfg);\n  };\n  /**\r\n   * Highlight symbol\r\n   */\n  EffectSymbol.prototype.highlight = function () {\n    enterEmphasis(this);\n  };\n  /**\r\n   * Downplay symbol\r\n   */\n  EffectSymbol.prototype.downplay = function () {\n    leaveEmphasis(this);\n  };\n  EffectSymbol.prototype.getSymbolType = function () {\n    var symbol = this.childAt(0);\n    return symbol && symbol.getSymbolType();\n  };\n  /**\r\n   * Update symbol properties\r\n   */\n  EffectSymbol.prototype.updateData = function (data, idx) {\n    var _this = this;\n    var seriesModel = data.hostModel;\n    this.childAt(0).updateData(data, idx);\n    var rippleGroup = this.childAt(1);\n    var itemModel = data.getItemModel(idx);\n    var symbolType = data.getItemVisual(idx, 'symbol');\n    var symbolSize = normalizeSymbolSize(data.getItemVisual(idx, 'symbolSize'));\n    var symbolStyle = data.getItemVisual(idx, 'style');\n    var color = symbolStyle && symbolStyle.fill;\n    var emphasisModel = itemModel.getModel('emphasis');\n    rippleGroup.setScale(symbolSize);\n    rippleGroup.traverse(function (ripplePath) {\n      ripplePath.setStyle('fill', color);\n    });\n    var symbolOffset = normalizeSymbolOffset(data.getItemVisual(idx, 'symbolOffset'), symbolSize);\n    if (symbolOffset) {\n      rippleGroup.x = symbolOffset[0];\n      rippleGroup.y = symbolOffset[1];\n    }\n    var symbolRotate = data.getItemVisual(idx, 'symbolRotate');\n    rippleGroup.rotation = (symbolRotate || 0) * Math.PI / 180 || 0;\n    var effectCfg = {};\n    effectCfg.showEffectOn = seriesModel.get('showEffectOn');\n    effectCfg.rippleScale = itemModel.get(['rippleEffect', 'scale']);\n    effectCfg.brushType = itemModel.get(['rippleEffect', 'brushType']);\n    effectCfg.period = itemModel.get(['rippleEffect', 'period']) * 1000;\n    effectCfg.effectOffset = idx / data.count();\n    effectCfg.z = seriesModel.getShallow('z') || 0;\n    effectCfg.zlevel = seriesModel.getShallow('zlevel') || 0;\n    effectCfg.symbolType = symbolType;\n    effectCfg.color = color;\n    effectCfg.rippleEffectColor = itemModel.get(['rippleEffect', 'color']);\n    effectCfg.rippleNumber = itemModel.get(['rippleEffect', 'number']);\n    if (effectCfg.showEffectOn === 'render') {\n      this._effectCfg ? this.updateEffectAnimation(effectCfg) : this.startEffectAnimation(effectCfg);\n      this._effectCfg = effectCfg;\n    } else {\n      // Not keep old effect config\n      this._effectCfg = null;\n      this.stopEffectAnimation();\n      this.onHoverStateChange = function (toState) {\n        if (toState === 'emphasis') {\n          if (effectCfg.showEffectOn !== 'render') {\n            _this.startEffectAnimation(effectCfg);\n          }\n        } else if (toState === 'normal') {\n          if (effectCfg.showEffectOn !== 'render') {\n            _this.stopEffectAnimation();\n          }\n        }\n      };\n    }\n    this._effectCfg = effectCfg;\n    toggleHoverEmphasis(this, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  };\n  ;\n  EffectSymbol.prototype.fadeOut = function (cb) {\n    cb && cb();\n  };\n  ;\n  return EffectSymbol;\n}(Group);\nexport default EffectSymbol;", "map": {"version": 3, "names": ["__extends", "createSymbol", "normalizeSymbolOffset", "normalizeSymbolSize", "Group", "enterEmphasis", "leaveEmphasis", "toggleHoverEmphasis", "SymbolClz", "updateRipplePath", "rippleGroup", "effectCfg", "color", "rippleEffectColor", "<PERSON><PERSON><PERSON><PERSON>", "ripple<PERSON>ath", "attr", "z", "zlevel", "style", "stroke", "brushType", "fill", "EffectSymbol", "_super", "data", "idx", "_this", "call", "symbol", "add", "updateData", "prototype", "stopEffectAnimation", "childAt", "removeAll", "startEffectAnimation", "symbolType", "rippleNumber", "i", "strokeNoScale", "z2", "silent", "scaleX", "scaleY", "delay", "period", "effectOffset", "animate", "when", "rippleScale", "start", "animateStyle", "opacity", "updateEffectAnimation", "oldEffectCfg", "_effectCfg", "DIFFICULT_PROPS", "length", "propName", "highlight", "downplay", "getSymbolType", "seriesModel", "hostModel", "itemModel", "getItemModel", "getItemVisual", "symbolSize", "symbolStyle", "emphasisModel", "getModel", "setScale", "traverse", "setStyle", "symbolOffset", "x", "y", "symbolRotate", "rotation", "Math", "PI", "showEffectOn", "get", "count", "getShallow", "onHoverStateChange", "toState", "fadeOut", "cb"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/node_modules/echarts/lib/chart/helper/EffectSymbol.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { createSymbol, normalizeSymbolOffset, normalizeSymbolSize } from '../../util/symbol.js';\nimport { Group } from '../../util/graphic.js';\nimport { enterEmphasis, leaveEmphasis, toggleHoverEmphasis } from '../../util/states.js';\nimport SymbolClz from './Symbol.js';\nfunction updateRipplePath(rippleGroup, effectCfg) {\n  var color = effectCfg.rippleEffectColor || effectCfg.color;\n  rippleGroup.eachChild(function (ripplePath) {\n    ripplePath.attr({\n      z: effectCfg.z,\n      zlevel: effectCfg.zlevel,\n      style: {\n        stroke: effectCfg.brushType === 'stroke' ? color : null,\n        fill: effectCfg.brushType === 'fill' ? color : null\n      }\n    });\n  });\n}\nvar EffectSymbol = /** @class */function (_super) {\n  __extends(EffectSymbol, _super);\n  function EffectSymbol(data, idx) {\n    var _this = _super.call(this) || this;\n    var symbol = new SymbolClz(data, idx);\n    var rippleGroup = new Group();\n    _this.add(symbol);\n    _this.add(rippleGroup);\n    _this.updateData(data, idx);\n    return _this;\n  }\n  EffectSymbol.prototype.stopEffectAnimation = function () {\n    this.childAt(1).removeAll();\n  };\n  EffectSymbol.prototype.startEffectAnimation = function (effectCfg) {\n    var symbolType = effectCfg.symbolType;\n    var color = effectCfg.color;\n    var rippleNumber = effectCfg.rippleNumber;\n    var rippleGroup = this.childAt(1);\n    for (var i = 0; i < rippleNumber; i++) {\n      // If width/height are set too small (e.g., set to 1) on ios10\n      // and macOS Sierra, a circle stroke become a rect, no matter what\n      // the scale is set. So we set width/height as 2. See #4136.\n      var ripplePath = createSymbol(symbolType, -1, -1, 2, 2, color);\n      ripplePath.attr({\n        style: {\n          strokeNoScale: true\n        },\n        z2: 99,\n        silent: true,\n        scaleX: 0.5,\n        scaleY: 0.5\n      });\n      var delay = -i / rippleNumber * effectCfg.period + effectCfg.effectOffset;\n      ripplePath.animate('', true).when(effectCfg.period, {\n        scaleX: effectCfg.rippleScale / 2,\n        scaleY: effectCfg.rippleScale / 2\n      }).delay(delay).start();\n      ripplePath.animateStyle(true).when(effectCfg.period, {\n        opacity: 0\n      }).delay(delay).start();\n      rippleGroup.add(ripplePath);\n    }\n    updateRipplePath(rippleGroup, effectCfg);\n  };\n  /**\r\n   * Update effect symbol\r\n   */\n  EffectSymbol.prototype.updateEffectAnimation = function (effectCfg) {\n    var oldEffectCfg = this._effectCfg;\n    var rippleGroup = this.childAt(1);\n    // Must reinitialize effect if following configuration changed\n    var DIFFICULT_PROPS = ['symbolType', 'period', 'rippleScale', 'rippleNumber'];\n    for (var i = 0; i < DIFFICULT_PROPS.length; i++) {\n      var propName = DIFFICULT_PROPS[i];\n      if (oldEffectCfg[propName] !== effectCfg[propName]) {\n        this.stopEffectAnimation();\n        this.startEffectAnimation(effectCfg);\n        return;\n      }\n    }\n    updateRipplePath(rippleGroup, effectCfg);\n  };\n  /**\r\n   * Highlight symbol\r\n   */\n  EffectSymbol.prototype.highlight = function () {\n    enterEmphasis(this);\n  };\n  /**\r\n   * Downplay symbol\r\n   */\n  EffectSymbol.prototype.downplay = function () {\n    leaveEmphasis(this);\n  };\n  EffectSymbol.prototype.getSymbolType = function () {\n    var symbol = this.childAt(0);\n    return symbol && symbol.getSymbolType();\n  };\n  /**\r\n   * Update symbol properties\r\n   */\n  EffectSymbol.prototype.updateData = function (data, idx) {\n    var _this = this;\n    var seriesModel = data.hostModel;\n    this.childAt(0).updateData(data, idx);\n    var rippleGroup = this.childAt(1);\n    var itemModel = data.getItemModel(idx);\n    var symbolType = data.getItemVisual(idx, 'symbol');\n    var symbolSize = normalizeSymbolSize(data.getItemVisual(idx, 'symbolSize'));\n    var symbolStyle = data.getItemVisual(idx, 'style');\n    var color = symbolStyle && symbolStyle.fill;\n    var emphasisModel = itemModel.getModel('emphasis');\n    rippleGroup.setScale(symbolSize);\n    rippleGroup.traverse(function (ripplePath) {\n      ripplePath.setStyle('fill', color);\n    });\n    var symbolOffset = normalizeSymbolOffset(data.getItemVisual(idx, 'symbolOffset'), symbolSize);\n    if (symbolOffset) {\n      rippleGroup.x = symbolOffset[0];\n      rippleGroup.y = symbolOffset[1];\n    }\n    var symbolRotate = data.getItemVisual(idx, 'symbolRotate');\n    rippleGroup.rotation = (symbolRotate || 0) * Math.PI / 180 || 0;\n    var effectCfg = {};\n    effectCfg.showEffectOn = seriesModel.get('showEffectOn');\n    effectCfg.rippleScale = itemModel.get(['rippleEffect', 'scale']);\n    effectCfg.brushType = itemModel.get(['rippleEffect', 'brushType']);\n    effectCfg.period = itemModel.get(['rippleEffect', 'period']) * 1000;\n    effectCfg.effectOffset = idx / data.count();\n    effectCfg.z = seriesModel.getShallow('z') || 0;\n    effectCfg.zlevel = seriesModel.getShallow('zlevel') || 0;\n    effectCfg.symbolType = symbolType;\n    effectCfg.color = color;\n    effectCfg.rippleEffectColor = itemModel.get(['rippleEffect', 'color']);\n    effectCfg.rippleNumber = itemModel.get(['rippleEffect', 'number']);\n    if (effectCfg.showEffectOn === 'render') {\n      this._effectCfg ? this.updateEffectAnimation(effectCfg) : this.startEffectAnimation(effectCfg);\n      this._effectCfg = effectCfg;\n    } else {\n      // Not keep old effect config\n      this._effectCfg = null;\n      this.stopEffectAnimation();\n      this.onHoverStateChange = function (toState) {\n        if (toState === 'emphasis') {\n          if (effectCfg.showEffectOn !== 'render') {\n            _this.startEffectAnimation(effectCfg);\n          }\n        } else if (toState === 'normal') {\n          if (effectCfg.showEffectOn !== 'render') {\n            _this.stopEffectAnimation();\n          }\n        }\n      };\n    }\n    this._effectCfg = effectCfg;\n    toggleHoverEmphasis(this, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  };\n  ;\n  EffectSymbol.prototype.fadeOut = function (cb) {\n    cb && cb();\n  };\n  ;\n  return EffectSymbol;\n}(Group);\nexport default EffectSymbol;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,YAAY,EAAEC,qBAAqB,EAAEC,mBAAmB,QAAQ,sBAAsB;AAC/F,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,aAAa,EAAEC,aAAa,EAAEC,mBAAmB,QAAQ,sBAAsB;AACxF,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,gBAAgBA,CAACC,WAAW,EAAEC,SAAS,EAAE;EAChD,IAAIC,KAAK,GAAGD,SAAS,CAACE,iBAAiB,IAAIF,SAAS,CAACC,KAAK;EAC1DF,WAAW,CAACI,SAAS,CAAC,UAAUC,UAAU,EAAE;IAC1CA,UAAU,CAACC,IAAI,CAAC;MACdC,CAAC,EAAEN,SAAS,CAACM,CAAC;MACdC,MAAM,EAAEP,SAAS,CAACO,MAAM;MACxBC,KAAK,EAAE;QACLC,MAAM,EAAET,SAAS,CAACU,SAAS,KAAK,QAAQ,GAAGT,KAAK,GAAG,IAAI;QACvDU,IAAI,EAAEX,SAAS,CAACU,SAAS,KAAK,MAAM,GAAGT,KAAK,GAAG;MACjD;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,IAAIW,YAAY,GAAG,aAAa,UAAUC,MAAM,EAAE;EAChDxB,SAAS,CAACuB,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAACE,IAAI,EAAEC,GAAG,EAAE;IAC/B,IAAIC,KAAK,GAAGH,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrC,IAAIC,MAAM,GAAG,IAAIrB,SAAS,CAACiB,IAAI,EAAEC,GAAG,CAAC;IACrC,IAAIhB,WAAW,GAAG,IAAIN,KAAK,CAAC,CAAC;IAC7BuB,KAAK,CAACG,GAAG,CAACD,MAAM,CAAC;IACjBF,KAAK,CAACG,GAAG,CAACpB,WAAW,CAAC;IACtBiB,KAAK,CAACI,UAAU,CAACN,IAAI,EAAEC,GAAG,CAAC;IAC3B,OAAOC,KAAK;EACd;EACAJ,YAAY,CAACS,SAAS,CAACC,mBAAmB,GAAG,YAAY;IACvD,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC;EAC7B,CAAC;EACDZ,YAAY,CAACS,SAAS,CAACI,oBAAoB,GAAG,UAAUzB,SAAS,EAAE;IACjE,IAAI0B,UAAU,GAAG1B,SAAS,CAAC0B,UAAU;IACrC,IAAIzB,KAAK,GAAGD,SAAS,CAACC,KAAK;IAC3B,IAAI0B,YAAY,GAAG3B,SAAS,CAAC2B,YAAY;IACzC,IAAI5B,WAAW,GAAG,IAAI,CAACwB,OAAO,CAAC,CAAC,CAAC;IACjC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,EAAEC,CAAC,EAAE,EAAE;MACrC;MACA;MACA;MACA,IAAIxB,UAAU,GAAGd,YAAY,CAACoC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEzB,KAAK,CAAC;MAC9DG,UAAU,CAACC,IAAI,CAAC;QACdG,KAAK,EAAE;UACLqB,aAAa,EAAE;QACjB,CAAC;QACDC,EAAE,EAAE,EAAE;QACNC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,GAAG;QACXC,MAAM,EAAE;MACV,CAAC,CAAC;MACF,IAAIC,KAAK,GAAG,CAACN,CAAC,GAAGD,YAAY,GAAG3B,SAAS,CAACmC,MAAM,GAAGnC,SAAS,CAACoC,YAAY;MACzEhC,UAAU,CAACiC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,CAACC,IAAI,CAACtC,SAAS,CAACmC,MAAM,EAAE;QAClDH,MAAM,EAAEhC,SAAS,CAACuC,WAAW,GAAG,CAAC;QACjCN,MAAM,EAAEjC,SAAS,CAACuC,WAAW,GAAG;MAClC,CAAC,CAAC,CAACL,KAAK,CAACA,KAAK,CAAC,CAACM,KAAK,CAAC,CAAC;MACvBpC,UAAU,CAACqC,YAAY,CAAC,IAAI,CAAC,CAACH,IAAI,CAACtC,SAAS,CAACmC,MAAM,EAAE;QACnDO,OAAO,EAAE;MACX,CAAC,CAAC,CAACR,KAAK,CAACA,KAAK,CAAC,CAACM,KAAK,CAAC,CAAC;MACvBzC,WAAW,CAACoB,GAAG,CAACf,UAAU,CAAC;IAC7B;IACAN,gBAAgB,CAACC,WAAW,EAAEC,SAAS,CAAC;EAC1C,CAAC;EACD;AACF;AACA;EACEY,YAAY,CAACS,SAAS,CAACsB,qBAAqB,GAAG,UAAU3C,SAAS,EAAE;IAClE,IAAI4C,YAAY,GAAG,IAAI,CAACC,UAAU;IAClC,IAAI9C,WAAW,GAAG,IAAI,CAACwB,OAAO,CAAC,CAAC,CAAC;IACjC;IACA,IAAIuB,eAAe,GAAG,CAAC,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE,cAAc,CAAC;IAC7E,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,eAAe,CAACC,MAAM,EAAEnB,CAAC,EAAE,EAAE;MAC/C,IAAIoB,QAAQ,GAAGF,eAAe,CAAClB,CAAC,CAAC;MACjC,IAAIgB,YAAY,CAACI,QAAQ,CAAC,KAAKhD,SAAS,CAACgD,QAAQ,CAAC,EAAE;QAClD,IAAI,CAAC1B,mBAAmB,CAAC,CAAC;QAC1B,IAAI,CAACG,oBAAoB,CAACzB,SAAS,CAAC;QACpC;MACF;IACF;IACAF,gBAAgB,CAACC,WAAW,EAAEC,SAAS,CAAC;EAC1C,CAAC;EACD;AACF;AACA;EACEY,YAAY,CAACS,SAAS,CAAC4B,SAAS,GAAG,YAAY;IAC7CvD,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EACD;AACF;AACA;EACEkB,YAAY,CAACS,SAAS,CAAC6B,QAAQ,GAAG,YAAY;IAC5CvD,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EACDiB,YAAY,CAACS,SAAS,CAAC8B,aAAa,GAAG,YAAY;IACjD,IAAIjC,MAAM,GAAG,IAAI,CAACK,OAAO,CAAC,CAAC,CAAC;IAC5B,OAAOL,MAAM,IAAIA,MAAM,CAACiC,aAAa,CAAC,CAAC;EACzC,CAAC;EACD;AACF;AACA;EACEvC,YAAY,CAACS,SAAS,CAACD,UAAU,GAAG,UAAUN,IAAI,EAAEC,GAAG,EAAE;IACvD,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIoC,WAAW,GAAGtC,IAAI,CAACuC,SAAS;IAChC,IAAI,CAAC9B,OAAO,CAAC,CAAC,CAAC,CAACH,UAAU,CAACN,IAAI,EAAEC,GAAG,CAAC;IACrC,IAAIhB,WAAW,GAAG,IAAI,CAACwB,OAAO,CAAC,CAAC,CAAC;IACjC,IAAI+B,SAAS,GAAGxC,IAAI,CAACyC,YAAY,CAACxC,GAAG,CAAC;IACtC,IAAIW,UAAU,GAAGZ,IAAI,CAAC0C,aAAa,CAACzC,GAAG,EAAE,QAAQ,CAAC;IAClD,IAAI0C,UAAU,GAAGjE,mBAAmB,CAACsB,IAAI,CAAC0C,aAAa,CAACzC,GAAG,EAAE,YAAY,CAAC,CAAC;IAC3E,IAAI2C,WAAW,GAAG5C,IAAI,CAAC0C,aAAa,CAACzC,GAAG,EAAE,OAAO,CAAC;IAClD,IAAId,KAAK,GAAGyD,WAAW,IAAIA,WAAW,CAAC/C,IAAI;IAC3C,IAAIgD,aAAa,GAAGL,SAAS,CAACM,QAAQ,CAAC,UAAU,CAAC;IAClD7D,WAAW,CAAC8D,QAAQ,CAACJ,UAAU,CAAC;IAChC1D,WAAW,CAAC+D,QAAQ,CAAC,UAAU1D,UAAU,EAAE;MACzCA,UAAU,CAAC2D,QAAQ,CAAC,MAAM,EAAE9D,KAAK,CAAC;IACpC,CAAC,CAAC;IACF,IAAI+D,YAAY,GAAGzE,qBAAqB,CAACuB,IAAI,CAAC0C,aAAa,CAACzC,GAAG,EAAE,cAAc,CAAC,EAAE0C,UAAU,CAAC;IAC7F,IAAIO,YAAY,EAAE;MAChBjE,WAAW,CAACkE,CAAC,GAAGD,YAAY,CAAC,CAAC,CAAC;MAC/BjE,WAAW,CAACmE,CAAC,GAAGF,YAAY,CAAC,CAAC,CAAC;IACjC;IACA,IAAIG,YAAY,GAAGrD,IAAI,CAAC0C,aAAa,CAACzC,GAAG,EAAE,cAAc,CAAC;IAC1DhB,WAAW,CAACqE,QAAQ,GAAG,CAACD,YAAY,IAAI,CAAC,IAAIE,IAAI,CAACC,EAAE,GAAG,GAAG,IAAI,CAAC;IAC/D,IAAItE,SAAS,GAAG,CAAC,CAAC;IAClBA,SAAS,CAACuE,YAAY,GAAGnB,WAAW,CAACoB,GAAG,CAAC,cAAc,CAAC;IACxDxE,SAAS,CAACuC,WAAW,GAAGe,SAAS,CAACkB,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IAChExE,SAAS,CAACU,SAAS,GAAG4C,SAAS,CAACkB,GAAG,CAAC,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;IAClExE,SAAS,CAACmC,MAAM,GAAGmB,SAAS,CAACkB,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,GAAG,IAAI;IACnExE,SAAS,CAACoC,YAAY,GAAGrB,GAAG,GAAGD,IAAI,CAAC2D,KAAK,CAAC,CAAC;IAC3CzE,SAAS,CAACM,CAAC,GAAG8C,WAAW,CAACsB,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC;IAC9C1E,SAAS,CAACO,MAAM,GAAG6C,WAAW,CAACsB,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC;IACxD1E,SAAS,CAAC0B,UAAU,GAAGA,UAAU;IACjC1B,SAAS,CAACC,KAAK,GAAGA,KAAK;IACvBD,SAAS,CAACE,iBAAiB,GAAGoD,SAAS,CAACkB,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IACtExE,SAAS,CAAC2B,YAAY,GAAG2B,SAAS,CAACkB,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;IAClE,IAAIxE,SAAS,CAACuE,YAAY,KAAK,QAAQ,EAAE;MACvC,IAAI,CAAC1B,UAAU,GAAG,IAAI,CAACF,qBAAqB,CAAC3C,SAAS,CAAC,GAAG,IAAI,CAACyB,oBAAoB,CAACzB,SAAS,CAAC;MAC9F,IAAI,CAAC6C,UAAU,GAAG7C,SAAS;IAC7B,CAAC,MAAM;MACL;MACA,IAAI,CAAC6C,UAAU,GAAG,IAAI;MACtB,IAAI,CAACvB,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACqD,kBAAkB,GAAG,UAAUC,OAAO,EAAE;QAC3C,IAAIA,OAAO,KAAK,UAAU,EAAE;UAC1B,IAAI5E,SAAS,CAACuE,YAAY,KAAK,QAAQ,EAAE;YACvCvD,KAAK,CAACS,oBAAoB,CAACzB,SAAS,CAAC;UACvC;QACF,CAAC,MAAM,IAAI4E,OAAO,KAAK,QAAQ,EAAE;UAC/B,IAAI5E,SAAS,CAACuE,YAAY,KAAK,QAAQ,EAAE;YACvCvD,KAAK,CAACM,mBAAmB,CAAC,CAAC;UAC7B;QACF;MACF,CAAC;IACH;IACA,IAAI,CAACuB,UAAU,GAAG7C,SAAS;IAC3BJ,mBAAmB,CAAC,IAAI,EAAE+D,aAAa,CAACa,GAAG,CAAC,OAAO,CAAC,EAAEb,aAAa,CAACa,GAAG,CAAC,WAAW,CAAC,EAAEb,aAAa,CAACa,GAAG,CAAC,UAAU,CAAC,CAAC;EACtH,CAAC;EACD;EACA5D,YAAY,CAACS,SAAS,CAACwD,OAAO,GAAG,UAAUC,EAAE,EAAE;IAC7CA,EAAE,IAAIA,EAAE,CAAC,CAAC;EACZ,CAAC;EACD;EACA,OAAOlE,YAAY;AACrB,CAAC,CAACnB,KAAK,CAAC;AACR,eAAemB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}