{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport function getNodeGlobalScale(seriesModel) {\n  var coordSys = seriesModel.coordinateSystem;\n  if (coordSys.type !== 'view') {\n    return 1;\n  }\n  var nodeScaleRatio = seriesModel.option.nodeScaleRatio;\n  var groupZoom = coordSys.scaleX;\n  // Scale node when zoom changes\n  var roamZoom = coordSys.getZoom();\n  var nodeScale = (roamZoom - 1) * nodeScaleRatio + 1;\n  return nodeScale / groupZoom;\n}\nexport function getSymbolSize(node) {\n  var symbolSize = node.getVisual('symbolSize');\n  if (symbolSize instanceof Array) {\n    symbolSize = (symbolSize[0] + symbolSize[1]) / 2;\n  }\n  return +symbolSize;\n}", "map": {"version": 3, "names": ["getNodeGlobalScale", "seriesModel", "coordSys", "coordinateSystem", "type", "nodeScaleRatio", "option", "groupZoom", "scaleX", "roamZoom", "getZoom", "nodeScale", "getSymbolSize", "node", "symbolSize", "getVisual", "Array"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/node_modules/echarts/lib/chart/graph/graphHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport function getNodeGlobalScale(seriesModel) {\n  var coordSys = seriesModel.coordinateSystem;\n  if (coordSys.type !== 'view') {\n    return 1;\n  }\n  var nodeScaleRatio = seriesModel.option.nodeScaleRatio;\n  var groupZoom = coordSys.scaleX;\n  // Scale node when zoom changes\n  var roamZoom = coordSys.getZoom();\n  var nodeScale = (roamZoom - 1) * nodeScaleRatio + 1;\n  return nodeScale / groupZoom;\n}\nexport function getSymbolSize(node) {\n  var symbolSize = node.getVisual('symbolSize');\n  if (symbolSize instanceof Array) {\n    symbolSize = (symbolSize[0] + symbolSize[1]) / 2;\n  }\n  return +symbolSize;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,kBAAkBA,CAACC,WAAW,EAAE;EAC9C,IAAIC,QAAQ,GAAGD,WAAW,CAACE,gBAAgB;EAC3C,IAAID,QAAQ,CAACE,IAAI,KAAK,MAAM,EAAE;IAC5B,OAAO,CAAC;EACV;EACA,IAAIC,cAAc,GAAGJ,WAAW,CAACK,MAAM,CAACD,cAAc;EACtD,IAAIE,SAAS,GAAGL,QAAQ,CAACM,MAAM;EAC/B;EACA,IAAIC,QAAQ,GAAGP,QAAQ,CAACQ,OAAO,CAAC,CAAC;EACjC,IAAIC,SAAS,GAAG,CAACF,QAAQ,GAAG,CAAC,IAAIJ,cAAc,GAAG,CAAC;EACnD,OAAOM,SAAS,GAAGJ,SAAS;AAC9B;AACA,OAAO,SAASK,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAIC,UAAU,GAAGD,IAAI,CAACE,SAAS,CAAC,YAAY,CAAC;EAC7C,IAAID,UAAU,YAAYE,KAAK,EAAE;IAC/BF,UAAU,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC;EAClD;EACA,OAAO,CAACA,UAAU;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}