{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/MarketSentiment.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Table, Tag, Spin, Alert, Select, DatePicker } from 'antd';\nimport { LineChartOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';\nimport ReactECharts from 'echarts-for-react';\nimport moment from 'moment';\nimport apiService from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  Option\n} = Select;\nconst MarketSentiment = () => {\n  _s();\n  var _currentSentiment$sen, _currentSentiment$mar, _ref;\n  const [loading, setLoading] = useState(true);\n  const [currentSentiment, setCurrentSentiment] = useState(null);\n  const [sentimentHistory, setSentimentHistory] = useState([]);\n  const [timeRange, setTimeRange] = useState(30);\n  useEffect(() => {\n    loadSentimentData();\n  }, [timeRange]);\n  const loadSentimentData = async () => {\n    try {\n      setLoading(true);\n      const [current, history] = await Promise.allSettled([apiService.getCurrentSentiment(), apiService.getSentimentHistory(timeRange)]);\n      if (current.status === 'fulfilled') {\n        var _current$value;\n        console.log('Current sentiment data:', current.value);\n        setCurrentSentiment(((_current$value = current.value) === null || _current$value === void 0 ? void 0 : _current$value.data) || current.value);\n      } else {\n        // 模拟数据\n        setCurrentSentiment({\n          sentiment_score: 75.5,\n          market_temperature: 68.2,\n          trend_direction: 'bullish',\n          volatility_index: 0.234,\n          sentiment_phase: 'high_tide',\n          phase_description: '市场情绪高涨，投资者积极性较高',\n          risk_level: 'medium',\n          entry_timing: 'good'\n        });\n      }\n      if (history.status === 'fulfilled') {\n        var _history$value;\n        console.log('Sentiment history data:', history.value);\n        const historyData = ((_history$value = history.value) === null || _history$value === void 0 ? void 0 : _history$value.data) || history.value;\n        setSentimentHistory(Array.isArray(historyData) ? historyData : []);\n      } else {\n        // 生成模拟历史数据\n        const mockHistory = [];\n        for (let i = timeRange; i >= 0; i--) {\n          const date = moment().subtract(i, 'days');\n          mockHistory.push({\n            date: date.format('YYYY-MM-DD'),\n            sentiment_score: 50 + Math.random() * 40,\n            market_temperature: 40 + Math.random() * 40,\n            limit_up_count: Math.floor(Math.random() * 100),\n            limit_down_count: Math.floor(Math.random() * 50),\n            volatility_index: Math.random() * 0.5\n          });\n        }\n        setSentimentHistory(mockHistory);\n      }\n    } catch (error) {\n      console.error('加载情绪数据失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 情绪历史趋势图配置\n  const getSentimentTrendOption = () => {\n    const dates = sentimentHistory.map(item => item.date);\n    const sentimentScores = sentimentHistory.map(item => item.sentiment_score);\n    const temperatures = sentimentHistory.map(item => item.market_temperature);\n    return {\n      title: {\n        text: '市场情绪趋势',\n        left: 'center'\n      },\n      tooltip: {\n        trigger: 'axis',\n        axisPointer: {\n          type: 'cross'\n        }\n      },\n      legend: {\n        data: ['情绪指数', '市场温度'],\n        top: 30\n      },\n      grid: {\n        left: '3%',\n        right: '4%',\n        bottom: '3%',\n        containLabel: true\n      },\n      xAxis: {\n        type: 'category',\n        data: dates,\n        axisLabel: {\n          formatter: value => moment(value).format('MM-DD')\n        }\n      },\n      yAxis: [{\n        type: 'value',\n        name: '情绪指数',\n        min: 0,\n        max: 100,\n        position: 'left'\n      }, {\n        type: 'value',\n        name: '市场温度',\n        min: 0,\n        max: 100,\n        position: 'right'\n      }],\n      series: [{\n        name: '情绪指数',\n        type: 'line',\n        yAxisIndex: 0,\n        data: sentimentScores,\n        smooth: true,\n        lineStyle: {\n          color: '#1890ff'\n        },\n        areaStyle: {\n          color: {\n            type: 'linear',\n            x: 0,\n            y: 0,\n            x2: 0,\n            y2: 1,\n            colorStops: [{\n              offset: 0,\n              color: 'rgba(24, 144, 255, 0.3)'\n            }, {\n              offset: 1,\n              color: 'rgba(24, 144, 255, 0.1)'\n            }]\n          }\n        }\n      }, {\n        name: '市场温度',\n        type: 'line',\n        yAxisIndex: 1,\n        data: temperatures,\n        smooth: true,\n        lineStyle: {\n          color: '#ff4d4f'\n        }\n      }]\n    };\n  };\n\n  // 涨跌停分布图配置\n  const getLimitDistributionOption = () => {\n    const dates = sentimentHistory.map(item => item.date);\n    const limitUp = sentimentHistory.map(item => item.limit_up_count);\n    const limitDown = sentimentHistory.map(item => -item.limit_down_count); // 负数显示\n\n    return {\n      title: {\n        text: '涨跌停分布',\n        left: 'center'\n      },\n      tooltip: {\n        trigger: 'axis',\n        formatter: params => {\n          let result = params[0].axisValue + '<br/>';\n          params.forEach(param => {\n            const value = Math.abs(param.value);\n            const name = param.seriesName;\n            result += `${param.marker}${name}: ${value}只<br/>`;\n          });\n          return result;\n        }\n      },\n      legend: {\n        data: ['涨停', '跌停'],\n        top: 30\n      },\n      grid: {\n        left: '3%',\n        right: '4%',\n        bottom: '3%',\n        containLabel: true\n      },\n      xAxis: {\n        type: 'category',\n        data: dates,\n        axisLabel: {\n          formatter: value => moment(value).format('MM-DD')\n        }\n      },\n      yAxis: {\n        type: 'value',\n        axisLabel: {\n          formatter: value => Math.abs(value)\n        }\n      },\n      series: [{\n        name: '涨停',\n        type: 'bar',\n        data: limitUp,\n        itemStyle: {\n          color: '#ff4d4f'\n        }\n      }, {\n        name: '跌停',\n        type: 'bar',\n        data: limitDown,\n        itemStyle: {\n          color: '#52c41a'\n        }\n      }]\n    };\n  };\n  const getSentimentPhaseColor = phase => {\n    switch (phase) {\n      case 'freezing':\n        return 'blue';\n      case 'warming':\n        return 'orange';\n      case 'high_tide':\n        return 'red';\n      case 'retreat':\n        return 'purple';\n      case 'chaos':\n        return 'gray';\n      default:\n        return 'default';\n    }\n  };\n  const getSentimentPhaseName = phase => {\n    switch (phase) {\n      case 'freezing':\n        return '冰点期';\n      case 'warming':\n        return '回暖期';\n      case 'high_tide':\n        return '高潮期';\n      case 'retreat':\n        return '退潮期';\n      case 'chaos':\n        return '混沌期';\n      default:\n        return '未知';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 16\n        },\n        children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u5E02\\u573A\\u60C5\\u7EEA\\u6570\\u636E...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                fontWeight: 'bold',\n                color: '#1890ff'\n              },\n              children: (currentSentiment === null || currentSentiment === void 0 ? void 0 : (_currentSentiment$sen = currentSentiment.sentiment_score) === null || _currentSentiment$sen === void 0 ? void 0 : _currentSentiment$sen.toFixed(1)) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#666',\n                marginTop: 8\n              },\n              children: \"\\u60C5\\u7EEA\\u6307\\u6570\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: getSentimentPhaseColor(currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.sentiment_phase),\n              style: {\n                marginTop: 8\n              },\n              children: getSentimentPhaseName(currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.sentiment_phase)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                fontWeight: 'bold',\n                color: '#ff4d4f'\n              },\n              children: [(currentSentiment === null || currentSentiment === void 0 ? void 0 : (_currentSentiment$mar = currentSentiment.market_temperature) === null || _currentSentiment$mar === void 0 ? void 0 : _currentSentiment$mar.toFixed(1)) || 0, \"\\xB0C\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#666',\n                marginTop: 8\n              },\n              children: \"\\u5E02\\u573A\\u6E29\\u5EA6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8,\n                color: '#666'\n              },\n              children: (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.trend_direction) === 'bullish' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ArrowUpOutlined, {\n                  style: {\n                    color: '#ff4d4f'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this), \" \\u770B\\u6DA8\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ArrowDownOutlined, {\n                  style: {\n                    color: '#52c41a'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 21\n                }, this), \" \\u770B\\u8DCC\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '48px',\n                fontWeight: 'bold',\n                color: '#faad14'\n              },\n              children: [((_ref = (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.volatility_index) * 100) === null || _ref === void 0 ? void 0 : _ref.toFixed(1)) || 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#666',\n                marginTop: 8\n              },\n              children: \"\\u6CE2\\u52A8\\u7387\\u6307\\u6570\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Tag, {\n                color: (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.entry_timing) === 'good' ? 'green' : 'orange',\n                children: (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.entry_timing) === 'good' ? '适宜入场' : '谨慎入场'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          value: timeRange,\n          onChange: setTimeRange,\n          style: {\n            width: 120\n          },\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: 7,\n            children: \"\\u8FD17\\u5929\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: 15,\n            children: \"\\u8FD115\\u5929\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: 30,\n            children: \"\\u8FD130\\u5929\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: 60,\n            children: \"\\u8FD160\\u5929\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u60C5\\u7EEA\\u8D8B\\u52BF\\u5206\\u6790\",\n          className: \"dashboard-card\",\n          children: /*#__PURE__*/_jsxDEV(ReactECharts, {\n            option: getSentimentTrendOption(),\n            style: {\n              height: '400px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u6DA8\\u8DCC\\u505C\\u5206\\u5E03\",\n          className: \"dashboard-card\",\n          children: /*#__PURE__*/_jsxDEV(ReactECharts, {\n            option: getLimitDistributionOption(),\n            style: {\n              height: '400px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      style: {\n        marginTop: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u60C5\\u7EEA\\u5206\\u6790\\u8BF4\\u660E\",\n          className: \"dashboard-card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              lineHeight: '1.8'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u5F53\\u524D\\u9636\\u6BB5\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 18\n              }, this), currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.phase_description]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u98CE\\u9669\\u7B49\\u7EA7\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 18\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.risk_level) === 'high' ? 'red' : (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.risk_level) === 'medium' ? 'orange' : 'green',\n                children: (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.risk_level) === 'high' ? '高风险' : (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.risk_level) === 'medium' ? '中等风险' : '低风险'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u64CD\\u4F5C\\u5EFA\\u8BAE\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 18\n              }, this), (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.sentiment_phase) === 'high_tide' && '市场情绪高涨，注意风险控制，适当减仓', (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.sentiment_phase) === 'warming' && '市场回暖，可适当增加仓位', (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.sentiment_phase) === 'freezing' && '市场冰点，寻找优质标的建仓机会', (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.sentiment_phase) === 'retreat' && '市场退潮，保持谨慎，控制仓位', (currentSentiment === null || currentSentiment === void 0 ? void 0 : currentSentiment.sentiment_phase) === 'chaos' && '市场混沌，观望为主，等待明确信号']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 261,\n    columnNumber: 5\n  }, this);\n};\n_s(MarketSentiment, \"4YDbupYlT6YfSt5rLD8V/Yb0s6A=\");\n_c = MarketSentiment;\nexport default MarketSentiment;\nvar _c;\n$RefreshReg$(_c, \"MarketSentiment\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Table", "Tag", "Spin", "<PERSON><PERSON>", "Select", "DatePicker", "LineChartOutlined", "ArrowUpOutlined", "ArrowDownOutlined", "ReactECharts", "moment", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RangePicker", "Option", "MarketSentiment", "_s", "_currentSentiment$sen", "_currentSentiment$mar", "_ref", "loading", "setLoading", "currentSentiment", "setCurrentSentiment", "sentimentHistory", "setSentimentHistory", "timeRange", "setTimeRange", "loadSentimentData", "current", "history", "Promise", "allSettled", "getCurrentSentiment", "getSentimentHistory", "status", "_current$value", "console", "log", "value", "data", "sentiment_score", "market_temperature", "trend_direction", "volatility_index", "sentiment_phase", "phase_description", "risk_level", "entry_timing", "_history$value", "historyData", "Array", "isArray", "mockHistory", "i", "date", "subtract", "push", "format", "Math", "random", "limit_up_count", "floor", "limit_down_count", "error", "getSentimentTrendOption", "dates", "map", "item", "sentimentScores", "temperatures", "title", "text", "left", "tooltip", "trigger", "axisPointer", "type", "legend", "top", "grid", "right", "bottom", "containLabel", "xAxis", "axisLabel", "formatter", "yAxis", "name", "min", "max", "position", "series", "yAxisIndex", "smooth", "lineStyle", "color", "areaStyle", "x", "y", "x2", "y2", "colorStops", "offset", "getLimitDistributionOption", "limitUp", "limitDown", "params", "result", "axisValue", "for<PERSON>ach", "param", "abs", "seriesName", "marker", "itemStyle", "getSentimentPhaseColor", "phase", "getSentimentPhaseName", "style", "textAlign", "padding", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "gutter", "marginBottom", "xs", "sm", "fontSize", "fontWeight", "toFixed", "onChange", "width", "lg", "className", "option", "height", "span", "lineHeight", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/MarketSentiment.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Table, Tag, Spin, Alert, Select, DatePicker } from 'antd';\nimport { LineChartOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';\nimport ReactECharts from 'echarts-for-react';\nimport moment from 'moment';\nimport apiService from '../services/api';\n\nconst { RangePicker } = DatePicker;\nconst { Option } = Select;\n\nconst MarketSentiment = () => {\n  const [loading, setLoading] = useState(true);\n  const [currentSentiment, setCurrentSentiment] = useState(null);\n  const [sentimentHistory, setSentimentHistory] = useState([]);\n  const [timeRange, setTimeRange] = useState(30);\n\n  useEffect(() => {\n    loadSentimentData();\n  }, [timeRange]);\n\n  const loadSentimentData = async () => {\n    try {\n      setLoading(true);\n      \n      const [current, history] = await Promise.allSettled([\n        apiService.getCurrentSentiment(),\n        apiService.getSentimentHistory(timeRange)\n      ]);\n\n      if (current.status === 'fulfilled') {\n        console.log('Current sentiment data:', current.value);\n        setCurrentSentiment(current.value?.data || current.value);\n      } else {\n        // 模拟数据\n        setCurrentSentiment({\n          sentiment_score: 75.5,\n          market_temperature: 68.2,\n          trend_direction: 'bullish',\n          volatility_index: 0.234,\n          sentiment_phase: 'high_tide',\n          phase_description: '市场情绪高涨，投资者积极性较高',\n          risk_level: 'medium',\n          entry_timing: 'good'\n        });\n      }\n\n      if (history.status === 'fulfilled') {\n        console.log('Sentiment history data:', history.value);\n        const historyData = history.value?.data || history.value;\n        setSentimentHistory(Array.isArray(historyData) ? historyData : []);\n      } else {\n        // 生成模拟历史数据\n        const mockHistory = [];\n        for (let i = timeRange; i >= 0; i--) {\n          const date = moment().subtract(i, 'days');\n          mockHistory.push({\n            date: date.format('YYYY-MM-DD'),\n            sentiment_score: 50 + Math.random() * 40,\n            market_temperature: 40 + Math.random() * 40,\n            limit_up_count: Math.floor(Math.random() * 100),\n            limit_down_count: Math.floor(Math.random() * 50),\n            volatility_index: Math.random() * 0.5\n          });\n        }\n        setSentimentHistory(mockHistory);\n      }\n    } catch (error) {\n      console.error('加载情绪数据失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 情绪历史趋势图配置\n  const getSentimentTrendOption = () => {\n    const dates = sentimentHistory.map(item => item.date);\n    const sentimentScores = sentimentHistory.map(item => item.sentiment_score);\n    const temperatures = sentimentHistory.map(item => item.market_temperature);\n\n    return {\n      title: {\n        text: '市场情绪趋势',\n        left: 'center'\n      },\n      tooltip: {\n        trigger: 'axis',\n        axisPointer: {\n          type: 'cross'\n        }\n      },\n      legend: {\n        data: ['情绪指数', '市场温度'],\n        top: 30\n      },\n      grid: {\n        left: '3%',\n        right: '4%',\n        bottom: '3%',\n        containLabel: true\n      },\n      xAxis: {\n        type: 'category',\n        data: dates,\n        axisLabel: {\n          formatter: (value) => moment(value).format('MM-DD')\n        }\n      },\n      yAxis: [\n        {\n          type: 'value',\n          name: '情绪指数',\n          min: 0,\n          max: 100,\n          position: 'left'\n        },\n        {\n          type: 'value',\n          name: '市场温度',\n          min: 0,\n          max: 100,\n          position: 'right'\n        }\n      ],\n      series: [\n        {\n          name: '情绪指数',\n          type: 'line',\n          yAxisIndex: 0,\n          data: sentimentScores,\n          smooth: true,\n          lineStyle: {\n            color: '#1890ff'\n          },\n          areaStyle: {\n            color: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 0,\n              y2: 1,\n              colorStops: [\n                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },\n                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }\n              ]\n            }\n          }\n        },\n        {\n          name: '市场温度',\n          type: 'line',\n          yAxisIndex: 1,\n          data: temperatures,\n          smooth: true,\n          lineStyle: {\n            color: '#ff4d4f'\n          }\n        }\n      ]\n    };\n  };\n\n  // 涨跌停分布图配置\n  const getLimitDistributionOption = () => {\n    const dates = sentimentHistory.map(item => item.date);\n    const limitUp = sentimentHistory.map(item => item.limit_up_count);\n    const limitDown = sentimentHistory.map(item => -item.limit_down_count); // 负数显示\n\n    return {\n      title: {\n        text: '涨跌停分布',\n        left: 'center'\n      },\n      tooltip: {\n        trigger: 'axis',\n        formatter: (params) => {\n          let result = params[0].axisValue + '<br/>';\n          params.forEach(param => {\n            const value = Math.abs(param.value);\n            const name = param.seriesName;\n            result += `${param.marker}${name}: ${value}只<br/>`;\n          });\n          return result;\n        }\n      },\n      legend: {\n        data: ['涨停', '跌停'],\n        top: 30\n      },\n      grid: {\n        left: '3%',\n        right: '4%',\n        bottom: '3%',\n        containLabel: true\n      },\n      xAxis: {\n        type: 'category',\n        data: dates,\n        axisLabel: {\n          formatter: (value) => moment(value).format('MM-DD')\n        }\n      },\n      yAxis: {\n        type: 'value',\n        axisLabel: {\n          formatter: (value) => Math.abs(value)\n        }\n      },\n      series: [\n        {\n          name: '涨停',\n          type: 'bar',\n          data: limitUp,\n          itemStyle: {\n            color: '#ff4d4f'\n          }\n        },\n        {\n          name: '跌停',\n          type: 'bar',\n          data: limitDown,\n          itemStyle: {\n            color: '#52c41a'\n          }\n        }\n      ]\n    };\n  };\n\n  const getSentimentPhaseColor = (phase) => {\n    switch (phase) {\n      case 'freezing': return 'blue';\n      case 'warming': return 'orange';\n      case 'high_tide': return 'red';\n      case 'retreat': return 'purple';\n      case 'chaos': return 'gray';\n      default: return 'default';\n    }\n  };\n\n  const getSentimentPhaseName = (phase) => {\n    switch (phase) {\n      case 'freezing': return '冰点期';\n      case 'warming': return '回暖期';\n      case 'high_tide': return '高潮期';\n      case 'retreat': return '退潮期';\n      case 'chaos': return '混沌期';\n      default: return '未知';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: 16 }}>正在加载市场情绪数据...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* 当前情绪状态 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={8}>\n          <Card>\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontSize: '48px', fontWeight: 'bold', color: '#1890ff' }}>\n                {currentSentiment?.sentiment_score?.toFixed(1) || 0}\n              </div>\n              <div style={{ color: '#666', marginTop: 8 }}>情绪指数</div>\n              <Tag \n                color={getSentimentPhaseColor(currentSentiment?.sentiment_phase)}\n                style={{ marginTop: 8 }}\n              >\n                {getSentimentPhaseName(currentSentiment?.sentiment_phase)}\n              </Tag>\n            </div>\n          </Card>\n        </Col>\n        <Col xs={24} sm={8}>\n          <Card>\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontSize: '48px', fontWeight: 'bold', color: '#ff4d4f' }}>\n                {currentSentiment?.market_temperature?.toFixed(1) || 0}°C\n              </div>\n              <div style={{ color: '#666', marginTop: 8 }}>市场温度</div>\n              <div style={{ marginTop: 8, color: '#666' }}>\n                {currentSentiment?.trend_direction === 'bullish' ?\n                  <><ArrowUpOutlined style={{ color: '#ff4d4f' }} /> 看涨</> :\n                  <><ArrowDownOutlined style={{ color: '#52c41a' }} /> 看跌</>\n                }\n              </div>\n            </div>\n          </Card>\n        </Col>\n        <Col xs={24} sm={8}>\n          <Card>\n            <div style={{ textAlign: 'center' }}>\n              <div style={{ fontSize: '48px', fontWeight: 'bold', color: '#faad14' }}>\n                {(currentSentiment?.volatility_index * 100)?.toFixed(1) || 0}%\n              </div>\n              <div style={{ color: '#666', marginTop: 8 }}>波动率指数</div>\n              <div style={{ marginTop: 8 }}>\n                <Tag color={currentSentiment?.entry_timing === 'good' ? 'green' : 'orange'}>\n                  {currentSentiment?.entry_timing === 'good' ? '适宜入场' : '谨慎入场'}\n                </Tag>\n              </div>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 时间范围选择 */}\n      <Row style={{ marginBottom: 16 }}>\n        <Col>\n          <Select\n            value={timeRange}\n            onChange={setTimeRange}\n            style={{ width: 120 }}\n          >\n            <Option value={7}>近7天</Option>\n            <Option value={15}>近15天</Option>\n            <Option value={30}>近30天</Option>\n            <Option value={60}>近60天</Option>\n          </Select>\n        </Col>\n      </Row>\n\n      {/* 趋势图表 */}\n      <Row gutter={[16, 16]}>\n        <Col xs={24} lg={12}>\n          <Card title=\"情绪趋势分析\" className=\"dashboard-card\">\n            <ReactECharts \n              option={getSentimentTrendOption()} \n              style={{ height: '400px' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} lg={12}>\n          <Card title=\"涨跌停分布\" className=\"dashboard-card\">\n            <ReactECharts \n              option={getLimitDistributionOption()} \n              style={{ height: '400px' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 情绪分析说明 */}\n      <Row style={{ marginTop: 24 }}>\n        <Col span={24}>\n          <Card title=\"情绪分析说明\" className=\"dashboard-card\">\n            <div style={{ lineHeight: '1.8' }}>\n              <p><strong>当前阶段：</strong>{currentSentiment?.phase_description}</p>\n              <p><strong>风险等级：</strong>\n                <Tag color={\n                  currentSentiment?.risk_level === 'high' ? 'red' :\n                  currentSentiment?.risk_level === 'medium' ? 'orange' : 'green'\n                }>\n                  {currentSentiment?.risk_level === 'high' ? '高风险' :\n                   currentSentiment?.risk_level === 'medium' ? '中等风险' : '低风险'}\n                </Tag>\n              </p>\n              <p><strong>操作建议：</strong>\n                {currentSentiment?.sentiment_phase === 'high_tide' && '市场情绪高涨，注意风险控制，适当减仓'}\n                {currentSentiment?.sentiment_phase === 'warming' && '市场回暖，可适当增加仓位'}\n                {currentSentiment?.sentiment_phase === 'freezing' && '市场冰点，寻找优质标的建仓机会'}\n                {currentSentiment?.sentiment_phase === 'retreat' && '市场退潮，保持谨慎，控制仓位'}\n                {currentSentiment?.sentiment_phase === 'chaos' && '市场混沌，观望为主，等待明确信号'}\n              </p>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default MarketSentiment;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,QAAQ,MAAM;AAClF,SAASC,iBAAiB,EAAEC,eAAe,EAAEC,iBAAiB,QAAQ,mBAAmB;AACzF,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAOC,UAAU,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzC,MAAM;EAAEC;AAAY,CAAC,GAAGX,UAAU;AAClC,MAAM;EAAEY;AAAO,CAAC,GAAGb,MAAM;AAEzB,MAAMc,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,IAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAE9CC,SAAS,CAAC,MAAM;IACdmC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;EAEf,MAAME,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM,CAACQ,OAAO,EAAEC,OAAO,CAAC,GAAG,MAAMC,OAAO,CAACC,UAAU,CAAC,CAClDxB,UAAU,CAACyB,mBAAmB,CAAC,CAAC,EAChCzB,UAAU,CAAC0B,mBAAmB,CAACR,SAAS,CAAC,CAC1C,CAAC;MAEF,IAAIG,OAAO,CAACM,MAAM,KAAK,WAAW,EAAE;QAAA,IAAAC,cAAA;QAClCC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAET,OAAO,CAACU,KAAK,CAAC;QACrDhB,mBAAmB,CAAC,EAAAa,cAAA,GAAAP,OAAO,CAACU,KAAK,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,IAAI,KAAIX,OAAO,CAACU,KAAK,CAAC;MAC3D,CAAC,MAAM;QACL;QACAhB,mBAAmB,CAAC;UAClBkB,eAAe,EAAE,IAAI;UACrBC,kBAAkB,EAAE,IAAI;UACxBC,eAAe,EAAE,SAAS;UAC1BC,gBAAgB,EAAE,KAAK;UACvBC,eAAe,EAAE,WAAW;UAC5BC,iBAAiB,EAAE,iBAAiB;UACpCC,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE;QAChB,CAAC,CAAC;MACJ;MAEA,IAAIlB,OAAO,CAACK,MAAM,KAAK,WAAW,EAAE;QAAA,IAAAc,cAAA;QAClCZ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAER,OAAO,CAACS,KAAK,CAAC;QACrD,MAAMW,WAAW,GAAG,EAAAD,cAAA,GAAAnB,OAAO,CAACS,KAAK,cAAAU,cAAA,uBAAbA,cAAA,CAAeT,IAAI,KAAIV,OAAO,CAACS,KAAK;QACxDd,mBAAmB,CAAC0B,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,GAAGA,WAAW,GAAG,EAAE,CAAC;MACpE,CAAC,MAAM;QACL;QACA,MAAMG,WAAW,GAAG,EAAE;QACtB,KAAK,IAAIC,CAAC,GAAG5B,SAAS,EAAE4B,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UACnC,MAAMC,IAAI,GAAGhD,MAAM,CAAC,CAAC,CAACiD,QAAQ,CAACF,CAAC,EAAE,MAAM,CAAC;UACzCD,WAAW,CAACI,IAAI,CAAC;YACfF,IAAI,EAAEA,IAAI,CAACG,MAAM,CAAC,YAAY,CAAC;YAC/BjB,eAAe,EAAE,EAAE,GAAGkB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;YACxClB,kBAAkB,EAAE,EAAE,GAAGiB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;YAC3CC,cAAc,EAAEF,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;YAC/CG,gBAAgB,EAAEJ,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;YAChDhB,gBAAgB,EAAEe,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;UACpC,CAAC,CAAC;QACJ;QACAnC,mBAAmB,CAAC4B,WAAW,CAAC;MAClC;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MACd3B,OAAO,CAAC2B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACR3C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4C,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,KAAK,GAAG1C,gBAAgB,CAAC2C,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACb,IAAI,CAAC;IACrD,MAAMc,eAAe,GAAG7C,gBAAgB,CAAC2C,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC3B,eAAe,CAAC;IAC1E,MAAM6B,YAAY,GAAG9C,gBAAgB,CAAC2C,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC1B,kBAAkB,CAAC;IAE1E,OAAO;MACL6B,KAAK,EAAE;QACLC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE;MACR,CAAC;MACDC,OAAO,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,WAAW,EAAE;UACXC,IAAI,EAAE;QACR;MACF,CAAC;MACDC,MAAM,EAAE;QACNtC,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;QACtBuC,GAAG,EAAE;MACP,CAAC;MACDC,IAAI,EAAE;QACJP,IAAI,EAAE,IAAI;QACVQ,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,YAAY,EAAE;MAChB,CAAC;MACDC,KAAK,EAAE;QACLP,IAAI,EAAE,UAAU;QAChBrC,IAAI,EAAE0B,KAAK;QACXmB,SAAS,EAAE;UACTC,SAAS,EAAG/C,KAAK,IAAKhC,MAAM,CAACgC,KAAK,CAAC,CAACmB,MAAM,CAAC,OAAO;QACpD;MACF,CAAC;MACD6B,KAAK,EAAE,CACL;QACEV,IAAI,EAAE,OAAO;QACbW,IAAI,EAAE,MAAM;QACZC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,GAAG;QACRC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEd,IAAI,EAAE,OAAO;QACbW,IAAI,EAAE,MAAM;QACZC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,GAAG;QACRC,QAAQ,EAAE;MACZ,CAAC,CACF;MACDC,MAAM,EAAE,CACN;QACEJ,IAAI,EAAE,MAAM;QACZX,IAAI,EAAE,MAAM;QACZgB,UAAU,EAAE,CAAC;QACbrD,IAAI,EAAE6B,eAAe;QACrByB,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE;UACTC,KAAK,EAAE;QACT,CAAC;QACDC,SAAS,EAAE;UACTD,KAAK,EAAE;YACLnB,IAAI,EAAE,QAAQ;YACdqB,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJC,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,CAAC;YACLC,UAAU,EAAE,CACV;cAAEC,MAAM,EAAE,CAAC;cAAEP,KAAK,EAAE;YAA0B,CAAC,EAC/C;cAAEO,MAAM,EAAE,CAAC;cAAEP,KAAK,EAAE;YAA0B,CAAC;UAEnD;QACF;MACF,CAAC,EACD;QACER,IAAI,EAAE,MAAM;QACZX,IAAI,EAAE,MAAM;QACZgB,UAAU,EAAE,CAAC;QACbrD,IAAI,EAAE8B,YAAY;QAClBwB,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE;UACTC,KAAK,EAAE;QACT;MACF,CAAC;IAEL,CAAC;EACH,CAAC;;EAED;EACA,MAAMQ,0BAA0B,GAAGA,CAAA,KAAM;IACvC,MAAMtC,KAAK,GAAG1C,gBAAgB,CAAC2C,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACb,IAAI,CAAC;IACrD,MAAMkD,OAAO,GAAGjF,gBAAgB,CAAC2C,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACP,cAAc,CAAC;IACjE,MAAM6C,SAAS,GAAGlF,gBAAgB,CAAC2C,GAAG,CAACC,IAAI,IAAI,CAACA,IAAI,CAACL,gBAAgB,CAAC,CAAC,CAAC;;IAExE,OAAO;MACLQ,KAAK,EAAE;QACLC,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE;MACR,CAAC;MACDC,OAAO,EAAE;QACPC,OAAO,EAAE,MAAM;QACfW,SAAS,EAAGqB,MAAM,IAAK;UACrB,IAAIC,MAAM,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACE,SAAS,GAAG,OAAO;UAC1CF,MAAM,CAACG,OAAO,CAACC,KAAK,IAAI;YACtB,MAAMxE,KAAK,GAAGoB,IAAI,CAACqD,GAAG,CAACD,KAAK,CAACxE,KAAK,CAAC;YACnC,MAAMiD,IAAI,GAAGuB,KAAK,CAACE,UAAU;YAC7BL,MAAM,IAAI,GAAGG,KAAK,CAACG,MAAM,GAAG1B,IAAI,KAAKjD,KAAK,QAAQ;UACpD,CAAC,CAAC;UACF,OAAOqE,MAAM;QACf;MACF,CAAC;MACD9B,MAAM,EAAE;QACNtC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QAClBuC,GAAG,EAAE;MACP,CAAC;MACDC,IAAI,EAAE;QACJP,IAAI,EAAE,IAAI;QACVQ,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,YAAY,EAAE;MAChB,CAAC;MACDC,KAAK,EAAE;QACLP,IAAI,EAAE,UAAU;QAChBrC,IAAI,EAAE0B,KAAK;QACXmB,SAAS,EAAE;UACTC,SAAS,EAAG/C,KAAK,IAAKhC,MAAM,CAACgC,KAAK,CAAC,CAACmB,MAAM,CAAC,OAAO;QACpD;MACF,CAAC;MACD6B,KAAK,EAAE;QACLV,IAAI,EAAE,OAAO;QACbQ,SAAS,EAAE;UACTC,SAAS,EAAG/C,KAAK,IAAKoB,IAAI,CAACqD,GAAG,CAACzE,KAAK;QACtC;MACF,CAAC;MACDqD,MAAM,EAAE,CACN;QACEJ,IAAI,EAAE,IAAI;QACVX,IAAI,EAAE,KAAK;QACXrC,IAAI,EAAEiE,OAAO;QACbU,SAAS,EAAE;UACTnB,KAAK,EAAE;QACT;MACF,CAAC,EACD;QACER,IAAI,EAAE,IAAI;QACVX,IAAI,EAAE,KAAK;QACXrC,IAAI,EAAEkE,SAAS;QACfS,SAAS,EAAE;UACTnB,KAAK,EAAE;QACT;MACF,CAAC;IAEL,CAAC;EACH,CAAC;EAED,MAAMoB,sBAAsB,GAAIC,KAAK,IAAK;IACxC,QAAQA,KAAK;MACX,KAAK,UAAU;QAAE,OAAO,MAAM;MAC9B,KAAK,SAAS;QAAE,OAAO,QAAQ;MAC/B,KAAK,WAAW;QAAE,OAAO,KAAK;MAC9B,KAAK,SAAS;QAAE,OAAO,QAAQ;MAC/B,KAAK,OAAO;QAAE,OAAO,MAAM;MAC3B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,qBAAqB,GAAID,KAAK,IAAK;IACvC,QAAQA,KAAK;MACX,KAAK,UAAU;QAAE,OAAO,KAAK;MAC7B,KAAK,SAAS;QAAE,OAAO,KAAK;MAC5B,KAAK,WAAW;QAAE,OAAO,KAAK;MAC9B,KAAK,SAAS;QAAE,OAAO,KAAK;MAC5B,KAAK,OAAO;QAAE,OAAO,KAAK;MAC1B;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,IAAIjG,OAAO,EAAE;IACX,oBACEV,OAAA;MAAK6G,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,gBACnDhH,OAAA,CAACX,IAAI;QAAC4H,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBrH,OAAA;QAAK6G,KAAK,EAAE;UAAES,SAAS,EAAE;QAAG,CAAE;QAAAN,QAAA,EAAC;MAAa;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAEV;EAEA,oBACErH,OAAA;IAAAgH,QAAA,gBAEEhH,OAAA,CAAChB,GAAG;MAACuI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACV,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAG,CAAE;MAAAR,QAAA,gBACjDhH,OAAA,CAACf,GAAG;QAACwI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACjBhH,OAAA,CAACd,IAAI;UAAA8H,QAAA,eACHhH,OAAA;YAAK6G,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAE,QAAA,gBAClChH,OAAA;cAAK6G,KAAK,EAAE;gBAAEc,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE,MAAM;gBAAEtC,KAAK,EAAE;cAAU,CAAE;cAAA0B,QAAA,EACpE,CAAApG,gBAAgB,aAAhBA,gBAAgB,wBAAAL,qBAAA,GAAhBK,gBAAgB,CAAEmB,eAAe,cAAAxB,qBAAA,uBAAjCA,qBAAA,CAAmCsH,OAAO,CAAC,CAAC,CAAC,KAAI;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNrH,OAAA;cAAK6G,KAAK,EAAE;gBAAEvB,KAAK,EAAE,MAAM;gBAAEgC,SAAS,EAAE;cAAE,CAAE;cAAAN,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvDrH,OAAA,CAACZ,GAAG;cACFkG,KAAK,EAAEoB,sBAAsB,CAAC9F,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuB,eAAe,CAAE;cACjE0E,KAAK,EAAE;gBAAES,SAAS,EAAE;cAAE,CAAE;cAAAN,QAAA,EAEvBJ,qBAAqB,CAAChG,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuB,eAAe;YAAC;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrH,OAAA,CAACf,GAAG;QAACwI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACjBhH,OAAA,CAACd,IAAI;UAAA8H,QAAA,eACHhH,OAAA;YAAK6G,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAE,QAAA,gBAClChH,OAAA;cAAK6G,KAAK,EAAE;gBAAEc,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE,MAAM;gBAAEtC,KAAK,EAAE;cAAU,CAAE;cAAA0B,QAAA,GACpE,CAAApG,gBAAgB,aAAhBA,gBAAgB,wBAAAJ,qBAAA,GAAhBI,gBAAgB,CAAEoB,kBAAkB,cAAAxB,qBAAA,uBAApCA,qBAAA,CAAsCqH,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,OACzD;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrH,OAAA;cAAK6G,KAAK,EAAE;gBAAEvB,KAAK,EAAE,MAAM;gBAAEgC,SAAS,EAAE;cAAE,CAAE;cAAAN,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvDrH,OAAA;cAAK6G,KAAK,EAAE;gBAAES,SAAS,EAAE,CAAC;gBAAEhC,KAAK,EAAE;cAAO,CAAE;cAAA0B,QAAA,EACzC,CAAApG,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqB,eAAe,MAAK,SAAS,gBAC9CjC,OAAA,CAAAE,SAAA;gBAAA8G,QAAA,gBAAEhH,OAAA,CAACN,eAAe;kBAACmH,KAAK,EAAE;oBAAEvB,KAAK,EAAE;kBAAU;gBAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAAG;cAAA,eAAE,CAAC,gBACxDrH,OAAA,CAAAE,SAAA;gBAAA8G,QAAA,gBAAEhH,OAAA,CAACL,iBAAiB;kBAACkH,KAAK,EAAE;oBAAEvB,KAAK,EAAE;kBAAU;gBAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAAG;cAAA,eAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrH,OAAA,CAACf,GAAG;QAACwI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACjBhH,OAAA,CAACd,IAAI;UAAA8H,QAAA,eACHhH,OAAA;YAAK6G,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAE,QAAA,gBAClChH,OAAA;cAAK6G,KAAK,EAAE;gBAAEc,QAAQ,EAAE,MAAM;gBAAEC,UAAU,EAAE,MAAM;gBAAEtC,KAAK,EAAE;cAAU,CAAE;cAAA0B,QAAA,GACpE,EAAAvG,IAAA,GAAC,CAAAG,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEsB,gBAAgB,IAAG,GAAG,cAAAzB,IAAA,uBAAzCA,IAAA,CAA4CoH,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,GAC/D;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrH,OAAA;cAAK6G,KAAK,EAAE;gBAAEvB,KAAK,EAAE,MAAM;gBAAEgC,SAAS,EAAE;cAAE,CAAE;cAAAN,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxDrH,OAAA;cAAK6G,KAAK,EAAE;gBAAES,SAAS,EAAE;cAAE,CAAE;cAAAN,QAAA,eAC3BhH,OAAA,CAACZ,GAAG;gBAACkG,KAAK,EAAE,CAAA1E,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE0B,YAAY,MAAK,MAAM,GAAG,OAAO,GAAG,QAAS;gBAAA0E,QAAA,EACxE,CAAApG,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE0B,YAAY,MAAK,MAAM,GAAG,MAAM,GAAG;cAAM;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrH,OAAA,CAAChB,GAAG;MAAC6H,KAAK,EAAE;QAAEW,YAAY,EAAE;MAAG,CAAE;MAAAR,QAAA,eAC/BhH,OAAA,CAACf,GAAG;QAAA+H,QAAA,eACFhH,OAAA,CAACT,MAAM;UACLsC,KAAK,EAAEb,SAAU;UACjB8G,QAAQ,EAAE7G,YAAa;UACvB4F,KAAK,EAAE;YAAEkB,KAAK,EAAE;UAAI,CAAE;UAAAf,QAAA,gBAEtBhH,OAAA,CAACI,MAAM;YAACyB,KAAK,EAAE,CAAE;YAAAmF,QAAA,EAAC;UAAG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC9BrH,OAAA,CAACI,MAAM;YAACyB,KAAK,EAAE,EAAG;YAAAmF,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChCrH,OAAA,CAACI,MAAM;YAACyB,KAAK,EAAE,EAAG;YAAAmF,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChCrH,OAAA,CAACI,MAAM;YAACyB,KAAK,EAAE,EAAG;YAAAmF,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrH,OAAA,CAAChB,GAAG;MAACuI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAP,QAAA,gBACpBhH,OAAA,CAACf,GAAG;QAACwI,EAAE,EAAE,EAAG;QAACO,EAAE,EAAE,EAAG;QAAAhB,QAAA,eAClBhH,OAAA,CAACd,IAAI;UAAC2E,KAAK,EAAC,sCAAQ;UAACoE,SAAS,EAAC,gBAAgB;UAAAjB,QAAA,eAC7ChH,OAAA,CAACJ,YAAY;YACXsI,MAAM,EAAE3E,uBAAuB,CAAC,CAAE;YAClCsD,KAAK,EAAE;cAAEsB,MAAM,EAAE;YAAQ;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrH,OAAA,CAACf,GAAG;QAACwI,EAAE,EAAE,EAAG;QAACO,EAAE,EAAE,EAAG;QAAAhB,QAAA,eAClBhH,OAAA,CAACd,IAAI;UAAC2E,KAAK,EAAC,gCAAO;UAACoE,SAAS,EAAC,gBAAgB;UAAAjB,QAAA,eAC5ChH,OAAA,CAACJ,YAAY;YACXsI,MAAM,EAAEpC,0BAA0B,CAAC,CAAE;YACrCe,KAAK,EAAE;cAAEsB,MAAM,EAAE;YAAQ;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrH,OAAA,CAAChB,GAAG;MAAC6H,KAAK,EAAE;QAAES,SAAS,EAAE;MAAG,CAAE;MAAAN,QAAA,eAC5BhH,OAAA,CAACf,GAAG;QAACmJ,IAAI,EAAE,EAAG;QAAApB,QAAA,eACZhH,OAAA,CAACd,IAAI;UAAC2E,KAAK,EAAC,sCAAQ;UAACoE,SAAS,EAAC,gBAAgB;UAAAjB,QAAA,eAC7ChH,OAAA;YAAK6G,KAAK,EAAE;cAAEwB,UAAU,EAAE;YAAM,CAAE;YAAArB,QAAA,gBAChChH,OAAA;cAAAgH,QAAA,gBAAGhH,OAAA;gBAAAgH,QAAA,EAAQ;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAACzG,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEwB,iBAAiB;YAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClErH,OAAA;cAAAgH,QAAA,gBAAGhH,OAAA;gBAAAgH,QAAA,EAAQ;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvBrH,OAAA,CAACZ,GAAG;gBAACkG,KAAK,EACR,CAAA1E,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyB,UAAU,MAAK,MAAM,GAAG,KAAK,GAC/C,CAAAzB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyB,UAAU,MAAK,QAAQ,GAAG,QAAQ,GAAG,OACxD;gBAAA2E,QAAA,EACE,CAAApG,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyB,UAAU,MAAK,MAAM,GAAG,KAAK,GAC/C,CAAAzB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyB,UAAU,MAAK,QAAQ,GAAG,MAAM,GAAG;cAAK;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACJrH,OAAA;cAAAgH,QAAA,gBAAGhH,OAAA;gBAAAgH,QAAA,EAAQ;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACtB,CAAAzG,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuB,eAAe,MAAK,WAAW,IAAI,oBAAoB,EACzE,CAAAvB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuB,eAAe,MAAK,SAAS,IAAI,cAAc,EACjE,CAAAvB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuB,eAAe,MAAK,UAAU,IAAI,iBAAiB,EACrE,CAAAvB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuB,eAAe,MAAK,SAAS,IAAI,gBAAgB,EACnE,CAAAvB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuB,eAAe,MAAK,OAAO,IAAI,kBAAkB;YAAA;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/G,EAAA,CA9WID,eAAe;AAAAiI,EAAA,GAAfjI,eAAe;AAgXrB,eAAeA,eAAe;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}