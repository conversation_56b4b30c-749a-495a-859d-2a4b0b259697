{"ast": null, "code": "import * as curve from '../core/curve.js';\nexport function containStroke(x0, y0, x1, y1, x2, y2, x3, y3, lineWidth, x, y) {\n  if (lineWidth === 0) {\n    return false;\n  }\n  var _l = lineWidth;\n  if (y > y0 + _l && y > y1 + _l && y > y2 + _l && y > y3 + _l || y < y0 - _l && y < y1 - _l && y < y2 - _l && y < y3 - _l || x > x0 + _l && x > x1 + _l && x > x2 + _l && x > x3 + _l || x < x0 - _l && x < x1 - _l && x < x2 - _l && x < x3 - _l) {\n    return false;\n  }\n  var d = curve.cubicProjectPoint(x0, y0, x1, y1, x2, y2, x3, y3, x, y, null);\n  return d <= _l / 2;\n}", "map": {"version": 3, "names": ["curve", "containStroke", "x0", "y0", "x1", "y1", "x2", "y2", "x3", "y3", "lineWidth", "x", "y", "_l", "d", "cubicProjectPoint"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/node_modules/zrender/lib/contain/cubic.js"], "sourcesContent": ["import * as curve from '../core/curve.js';\nexport function containStroke(x0, y0, x1, y1, x2, y2, x3, y3, lineWidth, x, y) {\n    if (lineWidth === 0) {\n        return false;\n    }\n    var _l = lineWidth;\n    if ((y > y0 + _l && y > y1 + _l && y > y2 + _l && y > y3 + _l)\n        || (y < y0 - _l && y < y1 - _l && y < y2 - _l && y < y3 - _l)\n        || (x > x0 + _l && x > x1 + _l && x > x2 + _l && x > x3 + _l)\n        || (x < x0 - _l && x < x1 - _l && x < x2 - _l && x < x3 - _l)) {\n        return false;\n    }\n    var d = curve.cubicProjectPoint(x0, y0, x1, y1, x2, y2, x3, y3, x, y, null);\n    return d <= _l / 2;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,kBAAkB;AACzC,OAAO,SAASC,aAAaA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,SAAS,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC3E,IAAIF,SAAS,KAAK,CAAC,EAAE;IACjB,OAAO,KAAK;EAChB;EACA,IAAIG,EAAE,GAAGH,SAAS;EAClB,IAAKE,CAAC,GAAGT,EAAE,GAAGU,EAAE,IAAID,CAAC,GAAGP,EAAE,GAAGQ,EAAE,IAAID,CAAC,GAAGL,EAAE,GAAGM,EAAE,IAAID,CAAC,GAAGH,EAAE,GAAGI,EAAE,IACrDD,CAAC,GAAGT,EAAE,GAAGU,EAAE,IAAID,CAAC,GAAGP,EAAE,GAAGQ,EAAE,IAAID,CAAC,GAAGL,EAAE,GAAGM,EAAE,IAAID,CAAC,GAAGH,EAAE,GAAGI,EAAG,IACzDF,CAAC,GAAGT,EAAE,GAAGW,EAAE,IAAIF,CAAC,GAAGP,EAAE,GAAGS,EAAE,IAAIF,CAAC,GAAGL,EAAE,GAAGO,EAAE,IAAIF,CAAC,GAAGH,EAAE,GAAGK,EAAG,IACzDF,CAAC,GAAGT,EAAE,GAAGW,EAAE,IAAIF,CAAC,GAAGP,EAAE,GAAGS,EAAE,IAAIF,CAAC,GAAGL,EAAE,GAAGO,EAAE,IAAIF,CAAC,GAAGH,EAAE,GAAGK,EAAG,EAAE;IAC/D,OAAO,KAAK;EAChB;EACA,IAAIC,CAAC,GAAGd,KAAK,CAACe,iBAAiB,CAACb,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEE,CAAC,EAAEC,CAAC,EAAE,IAAI,CAAC;EAC3E,OAAOE,CAAC,IAAID,EAAE,GAAG,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}