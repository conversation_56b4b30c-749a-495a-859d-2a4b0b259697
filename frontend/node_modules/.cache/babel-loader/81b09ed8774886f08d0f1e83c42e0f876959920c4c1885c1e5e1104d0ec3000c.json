{"ast": null, "code": "import axios from 'axios';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'development' ? 'http://localhost:8080/api/v1' : '/api/v1',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  // 可以在这里添加认证token等\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  console.error('API请求错误:', error);\n  return Promise.reject(error);\n});\n\n// API服务类\nclass ApiService {\n  // 系统健康检查\n  async getHealth() {\n    return api.get('/health');\n  }\n\n  // 市场情绪相关API\n  async getCurrentSentiment() {\n    return api.get('/sentiment/current');\n  }\n  async getSentimentHistory(days = 30) {\n    return api.get(`/sentiment/history?days=${days}`);\n  }\n  async getSentimentAnalysis() {\n    return api.get('/sentiment/analysis');\n  }\n\n  // 题材分析相关API\n  async getThemeRanking(limit = 20) {\n    return api.get(`/themes/ranking?limit=${limit}`);\n  }\n  async getThemeDetail(themeId) {\n    return api.get(`/themes/${themeId}`);\n  }\n  async getThemeLifecycle(themeId) {\n    return api.get(`/themes/${themeId}/lifecycle`);\n  }\n\n  // 龙头股相关API\n  async getLeaderRanking(limit = 50) {\n    return api.get(`/leaders/ranking?limit=${limit}`);\n  }\n  async getLeaderAnalysis(symbol) {\n    return api.get(`/leaders/${symbol}/analysis`);\n  }\n  async getLeaderComparison(symbols) {\n    return api.post('/leaders/comparison', {\n      symbols\n    });\n  }\n  async getThemeLeadersSummary() {\n    return api.get('/leaders/theme-summary');\n  }\n\n  // 交易信号相关API\n  async getLatestSignals(limit = 100) {\n    return api.get(`/signals/latest?limit=${limit}`);\n  }\n  async getStockSignal(symbol) {\n    return api.get(`/signals/${symbol}`);\n  }\n  async getSignalSummary() {\n    return api.get('/signals/summary');\n  }\n\n  // 持仓管理相关API\n  async getCurrentPositions() {\n    return api.get('/positions/current');\n  }\n  async getPortfolioMetrics() {\n    return api.get('/positions/metrics');\n  }\n  async openPosition(data) {\n    return api.post('/positions/open', data);\n  }\n  async closePosition(positionId) {\n    return api.post(`/positions/${positionId}/close`);\n  }\n  async getRiskControl() {\n    return api.get('/positions/risk-control');\n  }\n\n  // 实时数据相关API\n  async getRealtimeMarketData() {\n    return api.get('/market/realtime');\n  }\n  async getStockRealtimeData(symbol) {\n    return api.get(`/market/stock/${symbol}/realtime`);\n  }\n  async getNewsData(limit = 20) {\n    return api.get(`/market/news?limit=${limit}`);\n  }\n}\n\n// 创建API服务实例\nconst apiService = new ApiService();\n\n// 导出API服务和axios实例\nexport { apiService, api };\nexport default apiService;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "process", "env", "NODE_ENV", "timeout", "headers", "interceptors", "request", "use", "config", "error", "Promise", "reject", "response", "data", "console", "ApiService", "getHealth", "get", "getCurrentSentiment", "getSentimentHistory", "days", "getSentimentAnalysis", "getThemeRanking", "limit", "getThemeDetail", "themeId", "getThemeLifecycle", "getLeaderRanking", "getLeaderAnalysis", "symbol", "getLeaderComparison", "symbols", "post", "getThemeLeadersSummary", "getLatestSignals", "getStockSignal", "getSignalSummary", "getCurrentPositions", "getPortfolioMetrics", "openPosition", "closePosition", "positionId", "getRiskControl", "getRealtimeMarketData", "getStockRealtimeData", "getNewsData", "apiService"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: process.env.NODE_ENV === 'development'\n    ? 'http://localhost:8080/api/v1'\n    : '/api/v1',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    // 可以在这里添加认证token等\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    console.error('API请求错误:', error);\n    return Promise.reject(error);\n  }\n);\n\n// API服务类\nclass ApiService {\n  // 系统健康检查\n  async getHealth() {\n    return api.get('/health');\n  }\n\n  // 市场情绪相关API\n  async getCurrentSentiment() {\n    return api.get('/sentiment/current');\n  }\n\n  async getSentimentHistory(days = 30) {\n    return api.get(`/sentiment/history?days=${days}`);\n  }\n\n  async getSentimentAnalysis() {\n    return api.get('/sentiment/analysis');\n  }\n\n  // 题材分析相关API\n  async getThemeRanking(limit = 20) {\n    return api.get(`/themes/ranking?limit=${limit}`);\n  }\n\n  async getThemeDetail(themeId) {\n    return api.get(`/themes/${themeId}`);\n  }\n\n  async getThemeLifecycle(themeId) {\n    return api.get(`/themes/${themeId}/lifecycle`);\n  }\n\n  // 龙头股相关API\n  async getLeaderRanking(limit = 50) {\n    return api.get(`/leaders/ranking?limit=${limit}`);\n  }\n\n  async getLeaderAnalysis(symbol) {\n    return api.get(`/leaders/${symbol}/analysis`);\n  }\n\n  async getLeaderComparison(symbols) {\n    return api.post('/leaders/comparison', { symbols });\n  }\n\n  async getThemeLeadersSummary() {\n    return api.get('/leaders/theme-summary');\n  }\n\n  // 交易信号相关API\n  async getLatestSignals(limit = 100) {\n    return api.get(`/signals/latest?limit=${limit}`);\n  }\n\n  async getStockSignal(symbol) {\n    return api.get(`/signals/${symbol}`);\n  }\n\n  async getSignalSummary() {\n    return api.get('/signals/summary');\n  }\n\n  // 持仓管理相关API\n  async getCurrentPositions() {\n    return api.get('/positions/current');\n  }\n\n  async getPortfolioMetrics() {\n    return api.get('/positions/metrics');\n  }\n\n  async openPosition(data) {\n    return api.post('/positions/open', data);\n  }\n\n  async closePosition(positionId) {\n    return api.post(`/positions/${positionId}/close`);\n  }\n\n  async getRiskControl() {\n    return api.get('/positions/risk-control');\n  }\n\n  // 实时数据相关API\n  async getRealtimeMarketData() {\n    return api.get('/market/realtime');\n  }\n\n  async getStockRealtimeData(symbol) {\n    return api.get(`/market/stock/${symbol}/realtime`);\n  }\n\n  async getNewsData(limit = 20) {\n    return api.get(`/market/news?limit=${limit}`);\n  }\n}\n\n// 创建API服务实例\nconst apiService = new ApiService();\n\n// 导出API服务和axios实例\nexport { apiService, api };\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,GAC3C,8BAA8B,GAC9B,SAAS;EACbC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACA,OAAOA,MAAM;AACf,CAAC,EACAC,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACQ,YAAY,CAACO,QAAQ,CAACL,GAAG,CAC1BK,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAJ,KAAK,IAAK;EACTK,OAAO,CAACL,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAChC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,MAAMM,UAAU,CAAC;EACf;EACA,MAAMC,SAASA,CAAA,EAAG;IAChB,OAAOnB,GAAG,CAACoB,GAAG,CAAC,SAAS,CAAC;EAC3B;;EAEA;EACA,MAAMC,mBAAmBA,CAAA,EAAG;IAC1B,OAAOrB,GAAG,CAACoB,GAAG,CAAC,oBAAoB,CAAC;EACtC;EAEA,MAAME,mBAAmBA,CAACC,IAAI,GAAG,EAAE,EAAE;IACnC,OAAOvB,GAAG,CAACoB,GAAG,CAAC,2BAA2BG,IAAI,EAAE,CAAC;EACnD;EAEA,MAAMC,oBAAoBA,CAAA,EAAG;IAC3B,OAAOxB,GAAG,CAACoB,GAAG,CAAC,qBAAqB,CAAC;EACvC;;EAEA;EACA,MAAMK,eAAeA,CAACC,KAAK,GAAG,EAAE,EAAE;IAChC,OAAO1B,GAAG,CAACoB,GAAG,CAAC,yBAAyBM,KAAK,EAAE,CAAC;EAClD;EAEA,MAAMC,cAAcA,CAACC,OAAO,EAAE;IAC5B,OAAO5B,GAAG,CAACoB,GAAG,CAAC,WAAWQ,OAAO,EAAE,CAAC;EACtC;EAEA,MAAMC,iBAAiBA,CAACD,OAAO,EAAE;IAC/B,OAAO5B,GAAG,CAACoB,GAAG,CAAC,WAAWQ,OAAO,YAAY,CAAC;EAChD;;EAEA;EACA,MAAME,gBAAgBA,CAACJ,KAAK,GAAG,EAAE,EAAE;IACjC,OAAO1B,GAAG,CAACoB,GAAG,CAAC,0BAA0BM,KAAK,EAAE,CAAC;EACnD;EAEA,MAAMK,iBAAiBA,CAACC,MAAM,EAAE;IAC9B,OAAOhC,GAAG,CAACoB,GAAG,CAAC,YAAYY,MAAM,WAAW,CAAC;EAC/C;EAEA,MAAMC,mBAAmBA,CAACC,OAAO,EAAE;IACjC,OAAOlC,GAAG,CAACmC,IAAI,CAAC,qBAAqB,EAAE;MAAED;IAAQ,CAAC,CAAC;EACrD;EAEA,MAAME,sBAAsBA,CAAA,EAAG;IAC7B,OAAOpC,GAAG,CAACoB,GAAG,CAAC,wBAAwB,CAAC;EAC1C;;EAEA;EACA,MAAMiB,gBAAgBA,CAACX,KAAK,GAAG,GAAG,EAAE;IAClC,OAAO1B,GAAG,CAACoB,GAAG,CAAC,yBAAyBM,KAAK,EAAE,CAAC;EAClD;EAEA,MAAMY,cAAcA,CAACN,MAAM,EAAE;IAC3B,OAAOhC,GAAG,CAACoB,GAAG,CAAC,YAAYY,MAAM,EAAE,CAAC;EACtC;EAEA,MAAMO,gBAAgBA,CAAA,EAAG;IACvB,OAAOvC,GAAG,CAACoB,GAAG,CAAC,kBAAkB,CAAC;EACpC;;EAEA;EACA,MAAMoB,mBAAmBA,CAAA,EAAG;IAC1B,OAAOxC,GAAG,CAACoB,GAAG,CAAC,oBAAoB,CAAC;EACtC;EAEA,MAAMqB,mBAAmBA,CAAA,EAAG;IAC1B,OAAOzC,GAAG,CAACoB,GAAG,CAAC,oBAAoB,CAAC;EACtC;EAEA,MAAMsB,YAAYA,CAAC1B,IAAI,EAAE;IACvB,OAAOhB,GAAG,CAACmC,IAAI,CAAC,iBAAiB,EAAEnB,IAAI,CAAC;EAC1C;EAEA,MAAM2B,aAAaA,CAACC,UAAU,EAAE;IAC9B,OAAO5C,GAAG,CAACmC,IAAI,CAAC,cAAcS,UAAU,QAAQ,CAAC;EACnD;EAEA,MAAMC,cAAcA,CAAA,EAAG;IACrB,OAAO7C,GAAG,CAACoB,GAAG,CAAC,yBAAyB,CAAC;EAC3C;;EAEA;EACA,MAAM0B,qBAAqBA,CAAA,EAAG;IAC5B,OAAO9C,GAAG,CAACoB,GAAG,CAAC,kBAAkB,CAAC;EACpC;EAEA,MAAM2B,oBAAoBA,CAACf,MAAM,EAAE;IACjC,OAAOhC,GAAG,CAACoB,GAAG,CAAC,iBAAiBY,MAAM,WAAW,CAAC;EACpD;EAEA,MAAMgB,WAAWA,CAACtB,KAAK,GAAG,EAAE,EAAE;IAC5B,OAAO1B,GAAG,CAACoB,GAAG,CAAC,sBAAsBM,KAAK,EAAE,CAAC;EAC/C;AACF;;AAEA;AACA,MAAMuB,UAAU,GAAG,IAAI/B,UAAU,CAAC,CAAC;;AAEnC;AACA,SAAS+B,UAAU,EAAEjD,GAAG;AACxB,eAAeiD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}