{"ast": null, "code": "import { min as v2Min, max as v2Max, scale as v2Scale, distance as v2Distance, add as v2Add, clone as v2Clone, sub as v2Sub } from '../../core/vector.js';\nexport default function smoothBezier(points, smooth, isLoop, constraint) {\n  var cps = [];\n  var v = [];\n  var v1 = [];\n  var v2 = [];\n  var prevPoint;\n  var nextPoint;\n  var min;\n  var max;\n  if (constraint) {\n    min = [Infinity, Infinity];\n    max = [-Infinity, -Infinity];\n    for (var i = 0, len = points.length; i < len; i++) {\n      v2Min(min, min, points[i]);\n      v2Max(max, max, points[i]);\n    }\n    v2Min(min, min, constraint[0]);\n    v2Max(max, max, constraint[1]);\n  }\n  for (var i = 0, len = points.length; i < len; i++) {\n    var point = points[i];\n    if (isLoop) {\n      prevPoint = points[i ? i - 1 : len - 1];\n      nextPoint = points[(i + 1) % len];\n    } else {\n      if (i === 0 || i === len - 1) {\n        cps.push(v2Clone(points[i]));\n        continue;\n      } else {\n        prevPoint = points[i - 1];\n        nextPoint = points[i + 1];\n      }\n    }\n    v2Sub(v, nextPoint, prevPoint);\n    v2Scale(v, v, smooth);\n    var d0 = v2Distance(point, prevPoint);\n    var d1 = v2Distance(point, nextPoint);\n    var sum = d0 + d1;\n    if (sum !== 0) {\n      d0 /= sum;\n      d1 /= sum;\n    }\n    v2Scale(v1, v, -d0);\n    v2Scale(v2, v, d1);\n    var cp0 = v2Add([], point, v1);\n    var cp1 = v2Add([], point, v2);\n    if (constraint) {\n      v2Max(cp0, cp0, min);\n      v2Min(cp0, cp0, max);\n      v2Max(cp1, cp1, min);\n      v2Min(cp1, cp1, max);\n    }\n    cps.push(cp0);\n    cps.push(cp1);\n  }\n  if (isLoop) {\n    cps.push(cps.shift());\n  }\n  return cps;\n}", "map": {"version": 3, "names": ["min", "v2Min", "max", "v2Max", "scale", "v2Scale", "distance", "v2Distance", "add", "v2Add", "clone", "v2Clone", "sub", "v2Sub", "smoothBezier", "points", "smooth", "isLoop", "constraint", "cps", "v", "v1", "v2", "prevPoint", "nextPoint", "Infinity", "i", "len", "length", "point", "push", "d0", "d1", "sum", "cp0", "cp1", "shift"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/node_modules/zrender/lib/graphic/helper/smoothBezier.js"], "sourcesContent": ["import { min as v2Min, max as v2Max, scale as v2Scale, distance as v2Distance, add as v2Add, clone as v2Clone, sub as v2Sub } from '../../core/vector.js';\nexport default function smoothBezier(points, smooth, isLoop, constraint) {\n    var cps = [];\n    var v = [];\n    var v1 = [];\n    var v2 = [];\n    var prevPoint;\n    var nextPoint;\n    var min;\n    var max;\n    if (constraint) {\n        min = [Infinity, Infinity];\n        max = [-Infinity, -Infinity];\n        for (var i = 0, len = points.length; i < len; i++) {\n            v2Min(min, min, points[i]);\n            v2Max(max, max, points[i]);\n        }\n        v2Min(min, min, constraint[0]);\n        v2Max(max, max, constraint[1]);\n    }\n    for (var i = 0, len = points.length; i < len; i++) {\n        var point = points[i];\n        if (isLoop) {\n            prevPoint = points[i ? i - 1 : len - 1];\n            nextPoint = points[(i + 1) % len];\n        }\n        else {\n            if (i === 0 || i === len - 1) {\n                cps.push(v2Clone(points[i]));\n                continue;\n            }\n            else {\n                prevPoint = points[i - 1];\n                nextPoint = points[i + 1];\n            }\n        }\n        v2Sub(v, nextPoint, prevPoint);\n        v2Scale(v, v, smooth);\n        var d0 = v2Distance(point, prevPoint);\n        var d1 = v2Distance(point, nextPoint);\n        var sum = d0 + d1;\n        if (sum !== 0) {\n            d0 /= sum;\n            d1 /= sum;\n        }\n        v2Scale(v1, v, -d0);\n        v2Scale(v2, v, d1);\n        var cp0 = v2Add([], point, v1);\n        var cp1 = v2Add([], point, v2);\n        if (constraint) {\n            v2Max(cp0, cp0, min);\n            v2Min(cp0, cp0, max);\n            v2Max(cp1, cp1, min);\n            v2Min(cp1, cp1, max);\n        }\n        cps.push(cp0);\n        cps.push(cp1);\n    }\n    if (isLoop) {\n        cps.push(cps.shift());\n    }\n    return cps;\n}\n"], "mappings": "AAAA,SAASA,GAAG,IAAIC,KAAK,EAAEC,GAAG,IAAIC,KAAK,EAAEC,KAAK,IAAIC,OAAO,EAAEC,QAAQ,IAAIC,UAAU,EAAEC,GAAG,IAAIC,KAAK,EAAEC,KAAK,IAAIC,OAAO,EAAEC,GAAG,IAAIC,KAAK,QAAQ,sBAAsB;AACzJ,eAAe,SAASC,YAAYA,CAACC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAE;EACrE,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIC,CAAC,GAAG,EAAE;EACV,IAAIC,EAAE,GAAG,EAAE;EACX,IAAIC,EAAE,GAAG,EAAE;EACX,IAAIC,SAAS;EACb,IAAIC,SAAS;EACb,IAAIxB,GAAG;EACP,IAAIE,GAAG;EACP,IAAIgB,UAAU,EAAE;IACZlB,GAAG,GAAG,CAACyB,QAAQ,EAAEA,QAAQ,CAAC;IAC1BvB,GAAG,GAAG,CAAC,CAACuB,QAAQ,EAAE,CAACA,QAAQ,CAAC;IAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGZ,MAAM,CAACa,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAC/CzB,KAAK,CAACD,GAAG,EAAEA,GAAG,EAAEe,MAAM,CAACW,CAAC,CAAC,CAAC;MAC1BvB,KAAK,CAACD,GAAG,EAAEA,GAAG,EAAEa,MAAM,CAACW,CAAC,CAAC,CAAC;IAC9B;IACAzB,KAAK,CAACD,GAAG,EAAEA,GAAG,EAAEkB,UAAU,CAAC,CAAC,CAAC,CAAC;IAC9Bf,KAAK,CAACD,GAAG,EAAEA,GAAG,EAAEgB,UAAU,CAAC,CAAC,CAAC,CAAC;EAClC;EACA,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGZ,MAAM,CAACa,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC/C,IAAIG,KAAK,GAAGd,MAAM,CAACW,CAAC,CAAC;IACrB,IAAIT,MAAM,EAAE;MACRM,SAAS,GAAGR,MAAM,CAACW,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGC,GAAG,GAAG,CAAC,CAAC;MACvCH,SAAS,GAAGT,MAAM,CAAC,CAACW,CAAC,GAAG,CAAC,IAAIC,GAAG,CAAC;IACrC,CAAC,MACI;MACD,IAAID,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAKC,GAAG,GAAG,CAAC,EAAE;QAC1BR,GAAG,CAACW,IAAI,CAACnB,OAAO,CAACI,MAAM,CAACW,CAAC,CAAC,CAAC,CAAC;QAC5B;MACJ,CAAC,MACI;QACDH,SAAS,GAAGR,MAAM,CAACW,CAAC,GAAG,CAAC,CAAC;QACzBF,SAAS,GAAGT,MAAM,CAACW,CAAC,GAAG,CAAC,CAAC;MAC7B;IACJ;IACAb,KAAK,CAACO,CAAC,EAAEI,SAAS,EAAED,SAAS,CAAC;IAC9BlB,OAAO,CAACe,CAAC,EAAEA,CAAC,EAAEJ,MAAM,CAAC;IACrB,IAAIe,EAAE,GAAGxB,UAAU,CAACsB,KAAK,EAAEN,SAAS,CAAC;IACrC,IAAIS,EAAE,GAAGzB,UAAU,CAACsB,KAAK,EAAEL,SAAS,CAAC;IACrC,IAAIS,GAAG,GAAGF,EAAE,GAAGC,EAAE;IACjB,IAAIC,GAAG,KAAK,CAAC,EAAE;MACXF,EAAE,IAAIE,GAAG;MACTD,EAAE,IAAIC,GAAG;IACb;IACA5B,OAAO,CAACgB,EAAE,EAAED,CAAC,EAAE,CAACW,EAAE,CAAC;IACnB1B,OAAO,CAACiB,EAAE,EAAEF,CAAC,EAAEY,EAAE,CAAC;IAClB,IAAIE,GAAG,GAAGzB,KAAK,CAAC,EAAE,EAAEoB,KAAK,EAAER,EAAE,CAAC;IAC9B,IAAIc,GAAG,GAAG1B,KAAK,CAAC,EAAE,EAAEoB,KAAK,EAAEP,EAAE,CAAC;IAC9B,IAAIJ,UAAU,EAAE;MACZf,KAAK,CAAC+B,GAAG,EAAEA,GAAG,EAAElC,GAAG,CAAC;MACpBC,KAAK,CAACiC,GAAG,EAAEA,GAAG,EAAEhC,GAAG,CAAC;MACpBC,KAAK,CAACgC,GAAG,EAAEA,GAAG,EAAEnC,GAAG,CAAC;MACpBC,KAAK,CAACkC,GAAG,EAAEA,GAAG,EAAEjC,GAAG,CAAC;IACxB;IACAiB,GAAG,CAACW,IAAI,CAACI,GAAG,CAAC;IACbf,GAAG,CAACW,IAAI,CAACK,GAAG,CAAC;EACjB;EACA,IAAIlB,MAAM,EAAE;IACRE,GAAG,CAACW,IAAI,CAACX,GAAG,CAACiB,KAAK,CAAC,CAAC,CAAC;EACzB;EACA,OAAOjB,GAAG;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}