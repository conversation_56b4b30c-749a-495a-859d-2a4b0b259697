{"ast": null, "code": "import { create as v2Create, distSquare as v2DistSquare } from './vector.js';\nvar mathPow = Math.pow;\nvar mathSqrt = Math.sqrt;\nvar EPSILON = 1e-8;\nvar EPSILON_NUMERIC = 1e-4;\nvar THREE_SQRT = mathSqrt(3);\nvar ONE_THIRD = 1 / 3;\nvar _v0 = v2Create();\nvar _v1 = v2Create();\nvar _v2 = v2Create();\nfunction isAroundZero(val) {\n  return val > -EPSILON && val < EPSILON;\n}\nfunction isNotAroundZero(val) {\n  return val > EPSILON || val < -EPSILON;\n}\nexport function cubicAt(p0, p1, p2, p3, t) {\n  var onet = 1 - t;\n  return onet * onet * (onet * p0 + 3 * t * p1) + t * t * (t * p3 + 3 * onet * p2);\n}\nexport function cubicDerivativeAt(p0, p1, p2, p3, t) {\n  var onet = 1 - t;\n  return 3 * (((p1 - p0) * onet + 2 * (p2 - p1) * t) * onet + (p3 - p2) * t * t);\n}\nexport function cubicRootAt(p0, p1, p2, p3, val, roots) {\n  var a = p3 + 3 * (p1 - p2) - p0;\n  var b = 3 * (p2 - p1 * 2 + p0);\n  var c = 3 * (p1 - p0);\n  var d = p0 - val;\n  var A = b * b - 3 * a * c;\n  var B = b * c - 9 * a * d;\n  var C = c * c - 3 * b * d;\n  var n = 0;\n  if (isAroundZero(A) && isAroundZero(B)) {\n    if (isAroundZero(b)) {\n      roots[0] = 0;\n    } else {\n      var t1 = -c / b;\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n    }\n  } else {\n    var disc = B * B - 4 * A * C;\n    if (isAroundZero(disc)) {\n      var K = B / A;\n      var t1 = -b / a + K;\n      var t2 = -K / 2;\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n      if (t2 >= 0 && t2 <= 1) {\n        roots[n++] = t2;\n      }\n    } else if (disc > 0) {\n      var discSqrt = mathSqrt(disc);\n      var Y1 = A * b + 1.5 * a * (-B + discSqrt);\n      var Y2 = A * b + 1.5 * a * (-B - discSqrt);\n      if (Y1 < 0) {\n        Y1 = -mathPow(-Y1, ONE_THIRD);\n      } else {\n        Y1 = mathPow(Y1, ONE_THIRD);\n      }\n      if (Y2 < 0) {\n        Y2 = -mathPow(-Y2, ONE_THIRD);\n      } else {\n        Y2 = mathPow(Y2, ONE_THIRD);\n      }\n      var t1 = (-b - (Y1 + Y2)) / (3 * a);\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n    } else {\n      var T = (2 * A * b - 3 * a * B) / (2 * mathSqrt(A * A * A));\n      var theta = Math.acos(T) / 3;\n      var ASqrt = mathSqrt(A);\n      var tmp = Math.cos(theta);\n      var t1 = (-b - 2 * ASqrt * tmp) / (3 * a);\n      var t2 = (-b + ASqrt * (tmp + THREE_SQRT * Math.sin(theta))) / (3 * a);\n      var t3 = (-b + ASqrt * (tmp - THREE_SQRT * Math.sin(theta))) / (3 * a);\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n      if (t2 >= 0 && t2 <= 1) {\n        roots[n++] = t2;\n      }\n      if (t3 >= 0 && t3 <= 1) {\n        roots[n++] = t3;\n      }\n    }\n  }\n  return n;\n}\nexport function cubicExtrema(p0, p1, p2, p3, extrema) {\n  var b = 6 * p2 - 12 * p1 + 6 * p0;\n  var a = 9 * p1 + 3 * p3 - 3 * p0 - 9 * p2;\n  var c = 3 * p1 - 3 * p0;\n  var n = 0;\n  if (isAroundZero(a)) {\n    if (isNotAroundZero(b)) {\n      var t1 = -c / b;\n      if (t1 >= 0 && t1 <= 1) {\n        extrema[n++] = t1;\n      }\n    }\n  } else {\n    var disc = b * b - 4 * a * c;\n    if (isAroundZero(disc)) {\n      extrema[0] = -b / (2 * a);\n    } else if (disc > 0) {\n      var discSqrt = mathSqrt(disc);\n      var t1 = (-b + discSqrt) / (2 * a);\n      var t2 = (-b - discSqrt) / (2 * a);\n      if (t1 >= 0 && t1 <= 1) {\n        extrema[n++] = t1;\n      }\n      if (t2 >= 0 && t2 <= 1) {\n        extrema[n++] = t2;\n      }\n    }\n  }\n  return n;\n}\nexport function cubicSubdivide(p0, p1, p2, p3, t, out) {\n  var p01 = (p1 - p0) * t + p0;\n  var p12 = (p2 - p1) * t + p1;\n  var p23 = (p3 - p2) * t + p2;\n  var p012 = (p12 - p01) * t + p01;\n  var p123 = (p23 - p12) * t + p12;\n  var p0123 = (p123 - p012) * t + p012;\n  out[0] = p0;\n  out[1] = p01;\n  out[2] = p012;\n  out[3] = p0123;\n  out[4] = p0123;\n  out[5] = p123;\n  out[6] = p23;\n  out[7] = p3;\n}\nexport function cubicProjectPoint(x0, y0, x1, y1, x2, y2, x3, y3, x, y, out) {\n  var t;\n  var interval = 0.005;\n  var d = Infinity;\n  var prev;\n  var next;\n  var d1;\n  var d2;\n  _v0[0] = x;\n  _v0[1] = y;\n  for (var _t = 0; _t < 1; _t += 0.05) {\n    _v1[0] = cubicAt(x0, x1, x2, x3, _t);\n    _v1[1] = cubicAt(y0, y1, y2, y3, _t);\n    d1 = v2DistSquare(_v0, _v1);\n    if (d1 < d) {\n      t = _t;\n      d = d1;\n    }\n  }\n  d = Infinity;\n  for (var i = 0; i < 32; i++) {\n    if (interval < EPSILON_NUMERIC) {\n      break;\n    }\n    prev = t - interval;\n    next = t + interval;\n    _v1[0] = cubicAt(x0, x1, x2, x3, prev);\n    _v1[1] = cubicAt(y0, y1, y2, y3, prev);\n    d1 = v2DistSquare(_v1, _v0);\n    if (prev >= 0 && d1 < d) {\n      t = prev;\n      d = d1;\n    } else {\n      _v2[0] = cubicAt(x0, x1, x2, x3, next);\n      _v2[1] = cubicAt(y0, y1, y2, y3, next);\n      d2 = v2DistSquare(_v2, _v0);\n      if (next <= 1 && d2 < d) {\n        t = next;\n        d = d2;\n      } else {\n        interval *= 0.5;\n      }\n    }\n  }\n  if (out) {\n    out[0] = cubicAt(x0, x1, x2, x3, t);\n    out[1] = cubicAt(y0, y1, y2, y3, t);\n  }\n  return mathSqrt(d);\n}\nexport function cubicLength(x0, y0, x1, y1, x2, y2, x3, y3, iteration) {\n  var px = x0;\n  var py = y0;\n  var d = 0;\n  var step = 1 / iteration;\n  for (var i = 1; i <= iteration; i++) {\n    var t = i * step;\n    var x = cubicAt(x0, x1, x2, x3, t);\n    var y = cubicAt(y0, y1, y2, y3, t);\n    var dx = x - px;\n    var dy = y - py;\n    d += Math.sqrt(dx * dx + dy * dy);\n    px = x;\n    py = y;\n  }\n  return d;\n}\nexport function quadraticAt(p0, p1, p2, t) {\n  var onet = 1 - t;\n  return onet * (onet * p0 + 2 * t * p1) + t * t * p2;\n}\nexport function quadraticDerivativeAt(p0, p1, p2, t) {\n  return 2 * ((1 - t) * (p1 - p0) + t * (p2 - p1));\n}\nexport function quadraticRootAt(p0, p1, p2, val, roots) {\n  var a = p0 - 2 * p1 + p2;\n  var b = 2 * (p1 - p0);\n  var c = p0 - val;\n  var n = 0;\n  if (isAroundZero(a)) {\n    if (isNotAroundZero(b)) {\n      var t1 = -c / b;\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n    }\n  } else {\n    var disc = b * b - 4 * a * c;\n    if (isAroundZero(disc)) {\n      var t1 = -b / (2 * a);\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n    } else if (disc > 0) {\n      var discSqrt = mathSqrt(disc);\n      var t1 = (-b + discSqrt) / (2 * a);\n      var t2 = (-b - discSqrt) / (2 * a);\n      if (t1 >= 0 && t1 <= 1) {\n        roots[n++] = t1;\n      }\n      if (t2 >= 0 && t2 <= 1) {\n        roots[n++] = t2;\n      }\n    }\n  }\n  return n;\n}\nexport function quadraticExtremum(p0, p1, p2) {\n  var divider = p0 + p2 - 2 * p1;\n  if (divider === 0) {\n    return 0.5;\n  } else {\n    return (p0 - p1) / divider;\n  }\n}\nexport function quadraticSubdivide(p0, p1, p2, t, out) {\n  var p01 = (p1 - p0) * t + p0;\n  var p12 = (p2 - p1) * t + p1;\n  var p012 = (p12 - p01) * t + p01;\n  out[0] = p0;\n  out[1] = p01;\n  out[2] = p012;\n  out[3] = p012;\n  out[4] = p12;\n  out[5] = p2;\n}\nexport function quadraticProjectPoint(x0, y0, x1, y1, x2, y2, x, y, out) {\n  var t;\n  var interval = 0.005;\n  var d = Infinity;\n  _v0[0] = x;\n  _v0[1] = y;\n  for (var _t = 0; _t < 1; _t += 0.05) {\n    _v1[0] = quadraticAt(x0, x1, x2, _t);\n    _v1[1] = quadraticAt(y0, y1, y2, _t);\n    var d1 = v2DistSquare(_v0, _v1);\n    if (d1 < d) {\n      t = _t;\n      d = d1;\n    }\n  }\n  d = Infinity;\n  for (var i = 0; i < 32; i++) {\n    if (interval < EPSILON_NUMERIC) {\n      break;\n    }\n    var prev = t - interval;\n    var next = t + interval;\n    _v1[0] = quadraticAt(x0, x1, x2, prev);\n    _v1[1] = quadraticAt(y0, y1, y2, prev);\n    var d1 = v2DistSquare(_v1, _v0);\n    if (prev >= 0 && d1 < d) {\n      t = prev;\n      d = d1;\n    } else {\n      _v2[0] = quadraticAt(x0, x1, x2, next);\n      _v2[1] = quadraticAt(y0, y1, y2, next);\n      var d2 = v2DistSquare(_v2, _v0);\n      if (next <= 1 && d2 < d) {\n        t = next;\n        d = d2;\n      } else {\n        interval *= 0.5;\n      }\n    }\n  }\n  if (out) {\n    out[0] = quadraticAt(x0, x1, x2, t);\n    out[1] = quadraticAt(y0, y1, y2, t);\n  }\n  return mathSqrt(d);\n}\nexport function quadraticLength(x0, y0, x1, y1, x2, y2, iteration) {\n  var px = x0;\n  var py = y0;\n  var d = 0;\n  var step = 1 / iteration;\n  for (var i = 1; i <= iteration; i++) {\n    var t = i * step;\n    var x = quadraticAt(x0, x1, x2, t);\n    var y = quadraticAt(y0, y1, y2, t);\n    var dx = x - px;\n    var dy = y - py;\n    d += Math.sqrt(dx * dx + dy * dy);\n    px = x;\n    py = y;\n  }\n  return d;\n}", "map": {"version": 3, "names": ["create", "v2Create", "distSquare", "v2DistSquare", "mathPow", "Math", "pow", "mathSqrt", "sqrt", "EPSILON", "EPSILON_NUMERIC", "THREE_SQRT", "ONE_THIRD", "_v0", "_v1", "_v2", "isAroundZero", "val", "isNotAroundZero", "cubicAt", "p0", "p1", "p2", "p3", "t", "onet", "cubicDerivativeAt", "cubicRootAt", "roots", "a", "b", "c", "d", "A", "B", "C", "n", "t1", "disc", "K", "t2", "discSqrt", "Y1", "Y2", "T", "theta", "acos", "ASqrt", "tmp", "cos", "sin", "t3", "cubicExtrema", "extrema", "cubicSubdivide", "out", "p01", "p12", "p23", "p012", "p123", "p0123", "cubicProjectPoint", "x0", "y0", "x1", "y1", "x2", "y2", "x3", "y3", "x", "y", "interval", "Infinity", "prev", "next", "d1", "d2", "_t", "i", "cubicLength", "iteration", "px", "py", "step", "dx", "dy", "quadraticAt", "quadraticDerivativeAt", "quadraticRootAt", "quadraticExtremum", "divider", "quadraticSubdivide", "quadraticProjectPoint", "quadraticLength"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/node_modules/zrender/lib/core/curve.js"], "sourcesContent": ["import { create as v2Create, distSquare as v2DistSquare } from './vector.js';\nvar mathPow = Math.pow;\nvar mathSqrt = Math.sqrt;\nvar EPSILON = 1e-8;\nvar EPSILON_NUMERIC = 1e-4;\nvar THREE_SQRT = mathSqrt(3);\nvar ONE_THIRD = 1 / 3;\nvar _v0 = v2Create();\nvar _v1 = v2Create();\nvar _v2 = v2Create();\nfunction isAroundZero(val) {\n    return val > -EPSILON && val < EPSILON;\n}\nfunction isNotAroundZero(val) {\n    return val > EPSILON || val < -EPSILON;\n}\nexport function cubicAt(p0, p1, p2, p3, t) {\n    var onet = 1 - t;\n    return onet * onet * (onet * p0 + 3 * t * p1)\n        + t * t * (t * p3 + 3 * onet * p2);\n}\nexport function cubicDerivativeAt(p0, p1, p2, p3, t) {\n    var onet = 1 - t;\n    return 3 * (((p1 - p0) * onet + 2 * (p2 - p1) * t) * onet\n        + (p3 - p2) * t * t);\n}\nexport function cubicRootAt(p0, p1, p2, p3, val, roots) {\n    var a = p3 + 3 * (p1 - p2) - p0;\n    var b = 3 * (p2 - p1 * 2 + p0);\n    var c = 3 * (p1 - p0);\n    var d = p0 - val;\n    var A = b * b - 3 * a * c;\n    var B = b * c - 9 * a * d;\n    var C = c * c - 3 * b * d;\n    var n = 0;\n    if (isAroundZero(A) && isAroundZero(B)) {\n        if (isAroundZero(b)) {\n            roots[0] = 0;\n        }\n        else {\n            var t1 = -c / b;\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n        }\n    }\n    else {\n        var disc = B * B - 4 * A * C;\n        if (isAroundZero(disc)) {\n            var K = B / A;\n            var t1 = -b / a + K;\n            var t2 = -K / 2;\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n            if (t2 >= 0 && t2 <= 1) {\n                roots[n++] = t2;\n            }\n        }\n        else if (disc > 0) {\n            var discSqrt = mathSqrt(disc);\n            var Y1 = A * b + 1.5 * a * (-B + discSqrt);\n            var Y2 = A * b + 1.5 * a * (-B - discSqrt);\n            if (Y1 < 0) {\n                Y1 = -mathPow(-Y1, ONE_THIRD);\n            }\n            else {\n                Y1 = mathPow(Y1, ONE_THIRD);\n            }\n            if (Y2 < 0) {\n                Y2 = -mathPow(-Y2, ONE_THIRD);\n            }\n            else {\n                Y2 = mathPow(Y2, ONE_THIRD);\n            }\n            var t1 = (-b - (Y1 + Y2)) / (3 * a);\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n        }\n        else {\n            var T = (2 * A * b - 3 * a * B) / (2 * mathSqrt(A * A * A));\n            var theta = Math.acos(T) / 3;\n            var ASqrt = mathSqrt(A);\n            var tmp = Math.cos(theta);\n            var t1 = (-b - 2 * ASqrt * tmp) / (3 * a);\n            var t2 = (-b + ASqrt * (tmp + THREE_SQRT * Math.sin(theta))) / (3 * a);\n            var t3 = (-b + ASqrt * (tmp - THREE_SQRT * Math.sin(theta))) / (3 * a);\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n            if (t2 >= 0 && t2 <= 1) {\n                roots[n++] = t2;\n            }\n            if (t3 >= 0 && t3 <= 1) {\n                roots[n++] = t3;\n            }\n        }\n    }\n    return n;\n}\nexport function cubicExtrema(p0, p1, p2, p3, extrema) {\n    var b = 6 * p2 - 12 * p1 + 6 * p0;\n    var a = 9 * p1 + 3 * p3 - 3 * p0 - 9 * p2;\n    var c = 3 * p1 - 3 * p0;\n    var n = 0;\n    if (isAroundZero(a)) {\n        if (isNotAroundZero(b)) {\n            var t1 = -c / b;\n            if (t1 >= 0 && t1 <= 1) {\n                extrema[n++] = t1;\n            }\n        }\n    }\n    else {\n        var disc = b * b - 4 * a * c;\n        if (isAroundZero(disc)) {\n            extrema[0] = -b / (2 * a);\n        }\n        else if (disc > 0) {\n            var discSqrt = mathSqrt(disc);\n            var t1 = (-b + discSqrt) / (2 * a);\n            var t2 = (-b - discSqrt) / (2 * a);\n            if (t1 >= 0 && t1 <= 1) {\n                extrema[n++] = t1;\n            }\n            if (t2 >= 0 && t2 <= 1) {\n                extrema[n++] = t2;\n            }\n        }\n    }\n    return n;\n}\nexport function cubicSubdivide(p0, p1, p2, p3, t, out) {\n    var p01 = (p1 - p0) * t + p0;\n    var p12 = (p2 - p1) * t + p1;\n    var p23 = (p3 - p2) * t + p2;\n    var p012 = (p12 - p01) * t + p01;\n    var p123 = (p23 - p12) * t + p12;\n    var p0123 = (p123 - p012) * t + p012;\n    out[0] = p0;\n    out[1] = p01;\n    out[2] = p012;\n    out[3] = p0123;\n    out[4] = p0123;\n    out[5] = p123;\n    out[6] = p23;\n    out[7] = p3;\n}\nexport function cubicProjectPoint(x0, y0, x1, y1, x2, y2, x3, y3, x, y, out) {\n    var t;\n    var interval = 0.005;\n    var d = Infinity;\n    var prev;\n    var next;\n    var d1;\n    var d2;\n    _v0[0] = x;\n    _v0[1] = y;\n    for (var _t = 0; _t < 1; _t += 0.05) {\n        _v1[0] = cubicAt(x0, x1, x2, x3, _t);\n        _v1[1] = cubicAt(y0, y1, y2, y3, _t);\n        d1 = v2DistSquare(_v0, _v1);\n        if (d1 < d) {\n            t = _t;\n            d = d1;\n        }\n    }\n    d = Infinity;\n    for (var i = 0; i < 32; i++) {\n        if (interval < EPSILON_NUMERIC) {\n            break;\n        }\n        prev = t - interval;\n        next = t + interval;\n        _v1[0] = cubicAt(x0, x1, x2, x3, prev);\n        _v1[1] = cubicAt(y0, y1, y2, y3, prev);\n        d1 = v2DistSquare(_v1, _v0);\n        if (prev >= 0 && d1 < d) {\n            t = prev;\n            d = d1;\n        }\n        else {\n            _v2[0] = cubicAt(x0, x1, x2, x3, next);\n            _v2[1] = cubicAt(y0, y1, y2, y3, next);\n            d2 = v2DistSquare(_v2, _v0);\n            if (next <= 1 && d2 < d) {\n                t = next;\n                d = d2;\n            }\n            else {\n                interval *= 0.5;\n            }\n        }\n    }\n    if (out) {\n        out[0] = cubicAt(x0, x1, x2, x3, t);\n        out[1] = cubicAt(y0, y1, y2, y3, t);\n    }\n    return mathSqrt(d);\n}\nexport function cubicLength(x0, y0, x1, y1, x2, y2, x3, y3, iteration) {\n    var px = x0;\n    var py = y0;\n    var d = 0;\n    var step = 1 / iteration;\n    for (var i = 1; i <= iteration; i++) {\n        var t = i * step;\n        var x = cubicAt(x0, x1, x2, x3, t);\n        var y = cubicAt(y0, y1, y2, y3, t);\n        var dx = x - px;\n        var dy = y - py;\n        d += Math.sqrt(dx * dx + dy * dy);\n        px = x;\n        py = y;\n    }\n    return d;\n}\nexport function quadraticAt(p0, p1, p2, t) {\n    var onet = 1 - t;\n    return onet * (onet * p0 + 2 * t * p1) + t * t * p2;\n}\nexport function quadraticDerivativeAt(p0, p1, p2, t) {\n    return 2 * ((1 - t) * (p1 - p0) + t * (p2 - p1));\n}\nexport function quadraticRootAt(p0, p1, p2, val, roots) {\n    var a = p0 - 2 * p1 + p2;\n    var b = 2 * (p1 - p0);\n    var c = p0 - val;\n    var n = 0;\n    if (isAroundZero(a)) {\n        if (isNotAroundZero(b)) {\n            var t1 = -c / b;\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n        }\n    }\n    else {\n        var disc = b * b - 4 * a * c;\n        if (isAroundZero(disc)) {\n            var t1 = -b / (2 * a);\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n        }\n        else if (disc > 0) {\n            var discSqrt = mathSqrt(disc);\n            var t1 = (-b + discSqrt) / (2 * a);\n            var t2 = (-b - discSqrt) / (2 * a);\n            if (t1 >= 0 && t1 <= 1) {\n                roots[n++] = t1;\n            }\n            if (t2 >= 0 && t2 <= 1) {\n                roots[n++] = t2;\n            }\n        }\n    }\n    return n;\n}\nexport function quadraticExtremum(p0, p1, p2) {\n    var divider = p0 + p2 - 2 * p1;\n    if (divider === 0) {\n        return 0.5;\n    }\n    else {\n        return (p0 - p1) / divider;\n    }\n}\nexport function quadraticSubdivide(p0, p1, p2, t, out) {\n    var p01 = (p1 - p0) * t + p0;\n    var p12 = (p2 - p1) * t + p1;\n    var p012 = (p12 - p01) * t + p01;\n    out[0] = p0;\n    out[1] = p01;\n    out[2] = p012;\n    out[3] = p012;\n    out[4] = p12;\n    out[5] = p2;\n}\nexport function quadraticProjectPoint(x0, y0, x1, y1, x2, y2, x, y, out) {\n    var t;\n    var interval = 0.005;\n    var d = Infinity;\n    _v0[0] = x;\n    _v0[1] = y;\n    for (var _t = 0; _t < 1; _t += 0.05) {\n        _v1[0] = quadraticAt(x0, x1, x2, _t);\n        _v1[1] = quadraticAt(y0, y1, y2, _t);\n        var d1 = v2DistSquare(_v0, _v1);\n        if (d1 < d) {\n            t = _t;\n            d = d1;\n        }\n    }\n    d = Infinity;\n    for (var i = 0; i < 32; i++) {\n        if (interval < EPSILON_NUMERIC) {\n            break;\n        }\n        var prev = t - interval;\n        var next = t + interval;\n        _v1[0] = quadraticAt(x0, x1, x2, prev);\n        _v1[1] = quadraticAt(y0, y1, y2, prev);\n        var d1 = v2DistSquare(_v1, _v0);\n        if (prev >= 0 && d1 < d) {\n            t = prev;\n            d = d1;\n        }\n        else {\n            _v2[0] = quadraticAt(x0, x1, x2, next);\n            _v2[1] = quadraticAt(y0, y1, y2, next);\n            var d2 = v2DistSquare(_v2, _v0);\n            if (next <= 1 && d2 < d) {\n                t = next;\n                d = d2;\n            }\n            else {\n                interval *= 0.5;\n            }\n        }\n    }\n    if (out) {\n        out[0] = quadraticAt(x0, x1, x2, t);\n        out[1] = quadraticAt(y0, y1, y2, t);\n    }\n    return mathSqrt(d);\n}\nexport function quadraticLength(x0, y0, x1, y1, x2, y2, iteration) {\n    var px = x0;\n    var py = y0;\n    var d = 0;\n    var step = 1 / iteration;\n    for (var i = 1; i <= iteration; i++) {\n        var t = i * step;\n        var x = quadraticAt(x0, x1, x2, t);\n        var y = quadraticAt(y0, y1, y2, t);\n        var dx = x - px;\n        var dy = y - py;\n        d += Math.sqrt(dx * dx + dy * dy);\n        px = x;\n        py = y;\n    }\n    return d;\n}\n"], "mappings": "AAAA,SAASA,MAAM,IAAIC,QAAQ,EAAEC,UAAU,IAAIC,YAAY,QAAQ,aAAa;AAC5E,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,QAAQ,GAAGF,IAAI,CAACG,IAAI;AACxB,IAAIC,OAAO,GAAG,IAAI;AAClB,IAAIC,eAAe,GAAG,IAAI;AAC1B,IAAIC,UAAU,GAAGJ,QAAQ,CAAC,CAAC,CAAC;AAC5B,IAAIK,SAAS,GAAG,CAAC,GAAG,CAAC;AACrB,IAAIC,GAAG,GAAGZ,QAAQ,CAAC,CAAC;AACpB,IAAIa,GAAG,GAAGb,QAAQ,CAAC,CAAC;AACpB,IAAIc,GAAG,GAAGd,QAAQ,CAAC,CAAC;AACpB,SAASe,YAAYA,CAACC,GAAG,EAAE;EACvB,OAAOA,GAAG,GAAG,CAACR,OAAO,IAAIQ,GAAG,GAAGR,OAAO;AAC1C;AACA,SAASS,eAAeA,CAACD,GAAG,EAAE;EAC1B,OAAOA,GAAG,GAAGR,OAAO,IAAIQ,GAAG,GAAG,CAACR,OAAO;AAC1C;AACA,OAAO,SAASU,OAAOA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAE;EACvC,IAAIC,IAAI,GAAG,CAAC,GAAGD,CAAC;EAChB,OAAOC,IAAI,GAAGA,IAAI,IAAIA,IAAI,GAAGL,EAAE,GAAG,CAAC,GAAGI,CAAC,GAAGH,EAAE,CAAC,GACvCG,CAAC,GAAGA,CAAC,IAAIA,CAAC,GAAGD,EAAE,GAAG,CAAC,GAAGE,IAAI,GAAGH,EAAE,CAAC;AAC1C;AACA,OAAO,SAASI,iBAAiBA,CAACN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAE;EACjD,IAAIC,IAAI,GAAG,CAAC,GAAGD,CAAC;EAChB,OAAO,CAAC,IAAI,CAAC,CAACH,EAAE,GAAGD,EAAE,IAAIK,IAAI,GAAG,CAAC,IAAIH,EAAE,GAAGD,EAAE,CAAC,GAAGG,CAAC,IAAIC,IAAI,GACnD,CAACF,EAAE,GAAGD,EAAE,IAAIE,CAAC,GAAGA,CAAC,CAAC;AAC5B;AACA,OAAO,SAASG,WAAWA,CAACP,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEN,GAAG,EAAEW,KAAK,EAAE;EACpD,IAAIC,CAAC,GAAGN,EAAE,GAAG,CAAC,IAAIF,EAAE,GAAGC,EAAE,CAAC,GAAGF,EAAE;EAC/B,IAAIU,CAAC,GAAG,CAAC,IAAIR,EAAE,GAAGD,EAAE,GAAG,CAAC,GAAGD,EAAE,CAAC;EAC9B,IAAIW,CAAC,GAAG,CAAC,IAAIV,EAAE,GAAGD,EAAE,CAAC;EACrB,IAAIY,CAAC,GAAGZ,EAAE,GAAGH,GAAG;EAChB,IAAIgB,CAAC,GAAGH,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGE,CAAC;EACzB,IAAIG,CAAC,GAAGJ,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGF,CAAC,GAAGG,CAAC;EACzB,IAAIG,CAAC,GAAGJ,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGE,CAAC;EACzB,IAAII,CAAC,GAAG,CAAC;EACT,IAAIpB,YAAY,CAACiB,CAAC,CAAC,IAAIjB,YAAY,CAACkB,CAAC,CAAC,EAAE;IACpC,IAAIlB,YAAY,CAACc,CAAC,CAAC,EAAE;MACjBF,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;IAChB,CAAC,MACI;MACD,IAAIS,EAAE,GAAG,CAACN,CAAC,GAAGD,CAAC;MACf,IAAIO,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBT,KAAK,CAACQ,CAAC,EAAE,CAAC,GAAGC,EAAE;MACnB;IACJ;EACJ,CAAC,MACI;IACD,IAAIC,IAAI,GAAGJ,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGE,CAAC;IAC5B,IAAInB,YAAY,CAACsB,IAAI,CAAC,EAAE;MACpB,IAAIC,CAAC,GAAGL,CAAC,GAAGD,CAAC;MACb,IAAII,EAAE,GAAG,CAACP,CAAC,GAAGD,CAAC,GAAGU,CAAC;MACnB,IAAIC,EAAE,GAAG,CAACD,CAAC,GAAG,CAAC;MACf,IAAIF,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBT,KAAK,CAACQ,CAAC,EAAE,CAAC,GAAGC,EAAE;MACnB;MACA,IAAIG,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBZ,KAAK,CAACQ,CAAC,EAAE,CAAC,GAAGI,EAAE;MACnB;IACJ,CAAC,MACI,IAAIF,IAAI,GAAG,CAAC,EAAE;MACf,IAAIG,QAAQ,GAAGlC,QAAQ,CAAC+B,IAAI,CAAC;MAC7B,IAAII,EAAE,GAAGT,CAAC,GAAGH,CAAC,GAAG,GAAG,GAAGD,CAAC,IAAI,CAACK,CAAC,GAAGO,QAAQ,CAAC;MAC1C,IAAIE,EAAE,GAAGV,CAAC,GAAGH,CAAC,GAAG,GAAG,GAAGD,CAAC,IAAI,CAACK,CAAC,GAAGO,QAAQ,CAAC;MAC1C,IAAIC,EAAE,GAAG,CAAC,EAAE;QACRA,EAAE,GAAG,CAACtC,OAAO,CAAC,CAACsC,EAAE,EAAE9B,SAAS,CAAC;MACjC,CAAC,MACI;QACD8B,EAAE,GAAGtC,OAAO,CAACsC,EAAE,EAAE9B,SAAS,CAAC;MAC/B;MACA,IAAI+B,EAAE,GAAG,CAAC,EAAE;QACRA,EAAE,GAAG,CAACvC,OAAO,CAAC,CAACuC,EAAE,EAAE/B,SAAS,CAAC;MACjC,CAAC,MACI;QACD+B,EAAE,GAAGvC,OAAO,CAACuC,EAAE,EAAE/B,SAAS,CAAC;MAC/B;MACA,IAAIyB,EAAE,GAAG,CAAC,CAACP,CAAC,IAAIY,EAAE,GAAGC,EAAE,CAAC,KAAK,CAAC,GAAGd,CAAC,CAAC;MACnC,IAAIQ,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBT,KAAK,CAACQ,CAAC,EAAE,CAAC,GAAGC,EAAE;MACnB;IACJ,CAAC,MACI;MACD,IAAIO,CAAC,GAAG,CAAC,CAAC,GAAGX,CAAC,GAAGH,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGK,CAAC,KAAK,CAAC,GAAG3B,QAAQ,CAAC0B,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAAC,CAAC;MAC3D,IAAIY,KAAK,GAAGxC,IAAI,CAACyC,IAAI,CAACF,CAAC,CAAC,GAAG,CAAC;MAC5B,IAAIG,KAAK,GAAGxC,QAAQ,CAAC0B,CAAC,CAAC;MACvB,IAAIe,GAAG,GAAG3C,IAAI,CAAC4C,GAAG,CAACJ,KAAK,CAAC;MACzB,IAAIR,EAAE,GAAG,CAAC,CAACP,CAAC,GAAG,CAAC,GAAGiB,KAAK,GAAGC,GAAG,KAAK,CAAC,GAAGnB,CAAC,CAAC;MACzC,IAAIW,EAAE,GAAG,CAAC,CAACV,CAAC,GAAGiB,KAAK,IAAIC,GAAG,GAAGrC,UAAU,GAAGN,IAAI,CAAC6C,GAAG,CAACL,KAAK,CAAC,CAAC,KAAK,CAAC,GAAGhB,CAAC,CAAC;MACtE,IAAIsB,EAAE,GAAG,CAAC,CAACrB,CAAC,GAAGiB,KAAK,IAAIC,GAAG,GAAGrC,UAAU,GAAGN,IAAI,CAAC6C,GAAG,CAACL,KAAK,CAAC,CAAC,KAAK,CAAC,GAAGhB,CAAC,CAAC;MACtE,IAAIQ,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBT,KAAK,CAACQ,CAAC,EAAE,CAAC,GAAGC,EAAE;MACnB;MACA,IAAIG,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBZ,KAAK,CAACQ,CAAC,EAAE,CAAC,GAAGI,EAAE;MACnB;MACA,IAAIW,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBvB,KAAK,CAACQ,CAAC,EAAE,CAAC,GAAGe,EAAE;MACnB;IACJ;EACJ;EACA,OAAOf,CAAC;AACZ;AACA,OAAO,SAASgB,YAAYA,CAAChC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE8B,OAAO,EAAE;EAClD,IAAIvB,CAAC,GAAG,CAAC,GAAGR,EAAE,GAAG,EAAE,GAAGD,EAAE,GAAG,CAAC,GAAGD,EAAE;EACjC,IAAIS,CAAC,GAAG,CAAC,GAAGR,EAAE,GAAG,CAAC,GAAGE,EAAE,GAAG,CAAC,GAAGH,EAAE,GAAG,CAAC,GAAGE,EAAE;EACzC,IAAIS,CAAC,GAAG,CAAC,GAAGV,EAAE,GAAG,CAAC,GAAGD,EAAE;EACvB,IAAIgB,CAAC,GAAG,CAAC;EACT,IAAIpB,YAAY,CAACa,CAAC,CAAC,EAAE;IACjB,IAAIX,eAAe,CAACY,CAAC,CAAC,EAAE;MACpB,IAAIO,EAAE,GAAG,CAACN,CAAC,GAAGD,CAAC;MACf,IAAIO,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBgB,OAAO,CAACjB,CAAC,EAAE,CAAC,GAAGC,EAAE;MACrB;IACJ;EACJ,CAAC,MACI;IACD,IAAIC,IAAI,GAAGR,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGE,CAAC;IAC5B,IAAIf,YAAY,CAACsB,IAAI,CAAC,EAAE;MACpBe,OAAO,CAAC,CAAC,CAAC,GAAG,CAACvB,CAAC,IAAI,CAAC,GAAGD,CAAC,CAAC;IAC7B,CAAC,MACI,IAAIS,IAAI,GAAG,CAAC,EAAE;MACf,IAAIG,QAAQ,GAAGlC,QAAQ,CAAC+B,IAAI,CAAC;MAC7B,IAAID,EAAE,GAAG,CAAC,CAACP,CAAC,GAAGW,QAAQ,KAAK,CAAC,GAAGZ,CAAC,CAAC;MAClC,IAAIW,EAAE,GAAG,CAAC,CAACV,CAAC,GAAGW,QAAQ,KAAK,CAAC,GAAGZ,CAAC,CAAC;MAClC,IAAIQ,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBgB,OAAO,CAACjB,CAAC,EAAE,CAAC,GAAGC,EAAE;MACrB;MACA,IAAIG,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBa,OAAO,CAACjB,CAAC,EAAE,CAAC,GAAGI,EAAE;MACrB;IACJ;EACJ;EACA,OAAOJ,CAAC;AACZ;AACA,OAAO,SAASkB,cAAcA,CAAClC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAE+B,GAAG,EAAE;EACnD,IAAIC,GAAG,GAAG,CAACnC,EAAE,GAAGD,EAAE,IAAII,CAAC,GAAGJ,EAAE;EAC5B,IAAIqC,GAAG,GAAG,CAACnC,EAAE,GAAGD,EAAE,IAAIG,CAAC,GAAGH,EAAE;EAC5B,IAAIqC,GAAG,GAAG,CAACnC,EAAE,GAAGD,EAAE,IAAIE,CAAC,GAAGF,EAAE;EAC5B,IAAIqC,IAAI,GAAG,CAACF,GAAG,GAAGD,GAAG,IAAIhC,CAAC,GAAGgC,GAAG;EAChC,IAAII,IAAI,GAAG,CAACF,GAAG,GAAGD,GAAG,IAAIjC,CAAC,GAAGiC,GAAG;EAChC,IAAII,KAAK,GAAG,CAACD,IAAI,GAAGD,IAAI,IAAInC,CAAC,GAAGmC,IAAI;EACpCJ,GAAG,CAAC,CAAC,CAAC,GAAGnC,EAAE;EACXmC,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG;EACZD,GAAG,CAAC,CAAC,CAAC,GAAGI,IAAI;EACbJ,GAAG,CAAC,CAAC,CAAC,GAAGM,KAAK;EACdN,GAAG,CAAC,CAAC,CAAC,GAAGM,KAAK;EACdN,GAAG,CAAC,CAAC,CAAC,GAAGK,IAAI;EACbL,GAAG,CAAC,CAAC,CAAC,GAAGG,GAAG;EACZH,GAAG,CAAC,CAAC,CAAC,GAAGhC,EAAE;AACf;AACA,OAAO,SAASuC,iBAAiBA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,CAAC,EAAEjB,GAAG,EAAE;EACzE,IAAI/B,CAAC;EACL,IAAIiD,QAAQ,GAAG,KAAK;EACpB,IAAIzC,CAAC,GAAG0C,QAAQ;EAChB,IAAIC,IAAI;EACR,IAAIC,IAAI;EACR,IAAIC,EAAE;EACN,IAAIC,EAAE;EACNjE,GAAG,CAAC,CAAC,CAAC,GAAG0D,CAAC;EACV1D,GAAG,CAAC,CAAC,CAAC,GAAG2D,CAAC;EACV,KAAK,IAAIO,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAI,IAAI,EAAE;IACjCjE,GAAG,CAAC,CAAC,CAAC,GAAGK,OAAO,CAAC4C,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEU,EAAE,CAAC;IACpCjE,GAAG,CAAC,CAAC,CAAC,GAAGK,OAAO,CAAC6C,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAES,EAAE,CAAC;IACpCF,EAAE,GAAG1E,YAAY,CAACU,GAAG,EAAEC,GAAG,CAAC;IAC3B,IAAI+D,EAAE,GAAG7C,CAAC,EAAE;MACRR,CAAC,GAAGuD,EAAE;MACN/C,CAAC,GAAG6C,EAAE;IACV;EACJ;EACA7C,CAAC,GAAG0C,QAAQ;EACZ,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IACzB,IAAIP,QAAQ,GAAG/D,eAAe,EAAE;MAC5B;IACJ;IACAiE,IAAI,GAAGnD,CAAC,GAAGiD,QAAQ;IACnBG,IAAI,GAAGpD,CAAC,GAAGiD,QAAQ;IACnB3D,GAAG,CAAC,CAAC,CAAC,GAAGK,OAAO,CAAC4C,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEM,IAAI,CAAC;IACtC7D,GAAG,CAAC,CAAC,CAAC,GAAGK,OAAO,CAAC6C,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEK,IAAI,CAAC;IACtCE,EAAE,GAAG1E,YAAY,CAACW,GAAG,EAAED,GAAG,CAAC;IAC3B,IAAI8D,IAAI,IAAI,CAAC,IAAIE,EAAE,GAAG7C,CAAC,EAAE;MACrBR,CAAC,GAAGmD,IAAI;MACR3C,CAAC,GAAG6C,EAAE;IACV,CAAC,MACI;MACD9D,GAAG,CAAC,CAAC,CAAC,GAAGI,OAAO,CAAC4C,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEO,IAAI,CAAC;MACtC7D,GAAG,CAAC,CAAC,CAAC,GAAGI,OAAO,CAAC6C,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEM,IAAI,CAAC;MACtCE,EAAE,GAAG3E,YAAY,CAACY,GAAG,EAAEF,GAAG,CAAC;MAC3B,IAAI+D,IAAI,IAAI,CAAC,IAAIE,EAAE,GAAG9C,CAAC,EAAE;QACrBR,CAAC,GAAGoD,IAAI;QACR5C,CAAC,GAAG8C,EAAE;MACV,CAAC,MACI;QACDL,QAAQ,IAAI,GAAG;MACnB;IACJ;EACJ;EACA,IAAIlB,GAAG,EAAE;IACLA,GAAG,CAAC,CAAC,CAAC,GAAGpC,OAAO,CAAC4C,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAE7C,CAAC,CAAC;IACnC+B,GAAG,CAAC,CAAC,CAAC,GAAGpC,OAAO,CAAC6C,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAE9C,CAAC,CAAC;EACvC;EACA,OAAOjB,QAAQ,CAACyB,CAAC,CAAC;AACtB;AACA,OAAO,SAASiD,WAAWA,CAAClB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEY,SAAS,EAAE;EACnE,IAAIC,EAAE,GAAGpB,EAAE;EACX,IAAIqB,EAAE,GAAGpB,EAAE;EACX,IAAIhC,CAAC,GAAG,CAAC;EACT,IAAIqD,IAAI,GAAG,CAAC,GAAGH,SAAS;EACxB,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIE,SAAS,EAAEF,CAAC,EAAE,EAAE;IACjC,IAAIxD,CAAC,GAAGwD,CAAC,GAAGK,IAAI;IAChB,IAAId,CAAC,GAAGpD,OAAO,CAAC4C,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAE7C,CAAC,CAAC;IAClC,IAAIgD,CAAC,GAAGrD,OAAO,CAAC6C,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAE9C,CAAC,CAAC;IAClC,IAAI8D,EAAE,GAAGf,CAAC,GAAGY,EAAE;IACf,IAAII,EAAE,GAAGf,CAAC,GAAGY,EAAE;IACfpD,CAAC,IAAI3B,IAAI,CAACG,IAAI,CAAC8E,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;IACjCJ,EAAE,GAAGZ,CAAC;IACNa,EAAE,GAAGZ,CAAC;EACV;EACA,OAAOxC,CAAC;AACZ;AACA,OAAO,SAASwD,WAAWA,CAACpE,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEE,CAAC,EAAE;EACvC,IAAIC,IAAI,GAAG,CAAC,GAAGD,CAAC;EAChB,OAAOC,IAAI,IAAIA,IAAI,GAAGL,EAAE,GAAG,CAAC,GAAGI,CAAC,GAAGH,EAAE,CAAC,GAAGG,CAAC,GAAGA,CAAC,GAAGF,EAAE;AACvD;AACA,OAAO,SAASmE,qBAAqBA,CAACrE,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEE,CAAC,EAAE;EACjD,OAAO,CAAC,IAAI,CAAC,CAAC,GAAGA,CAAC,KAAKH,EAAE,GAAGD,EAAE,CAAC,GAAGI,CAAC,IAAIF,EAAE,GAAGD,EAAE,CAAC,CAAC;AACpD;AACA,OAAO,SAASqE,eAAeA,CAACtE,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEL,GAAG,EAAEW,KAAK,EAAE;EACpD,IAAIC,CAAC,GAAGT,EAAE,GAAG,CAAC,GAAGC,EAAE,GAAGC,EAAE;EACxB,IAAIQ,CAAC,GAAG,CAAC,IAAIT,EAAE,GAAGD,EAAE,CAAC;EACrB,IAAIW,CAAC,GAAGX,EAAE,GAAGH,GAAG;EAChB,IAAImB,CAAC,GAAG,CAAC;EACT,IAAIpB,YAAY,CAACa,CAAC,CAAC,EAAE;IACjB,IAAIX,eAAe,CAACY,CAAC,CAAC,EAAE;MACpB,IAAIO,EAAE,GAAG,CAACN,CAAC,GAAGD,CAAC;MACf,IAAIO,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBT,KAAK,CAACQ,CAAC,EAAE,CAAC,GAAGC,EAAE;MACnB;IACJ;EACJ,CAAC,MACI;IACD,IAAIC,IAAI,GAAGR,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGE,CAAC;IAC5B,IAAIf,YAAY,CAACsB,IAAI,CAAC,EAAE;MACpB,IAAID,EAAE,GAAG,CAACP,CAAC,IAAI,CAAC,GAAGD,CAAC,CAAC;MACrB,IAAIQ,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBT,KAAK,CAACQ,CAAC,EAAE,CAAC,GAAGC,EAAE;MACnB;IACJ,CAAC,MACI,IAAIC,IAAI,GAAG,CAAC,EAAE;MACf,IAAIG,QAAQ,GAAGlC,QAAQ,CAAC+B,IAAI,CAAC;MAC7B,IAAID,EAAE,GAAG,CAAC,CAACP,CAAC,GAAGW,QAAQ,KAAK,CAAC,GAAGZ,CAAC,CAAC;MAClC,IAAIW,EAAE,GAAG,CAAC,CAACV,CAAC,GAAGW,QAAQ,KAAK,CAAC,GAAGZ,CAAC,CAAC;MAClC,IAAIQ,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBT,KAAK,CAACQ,CAAC,EAAE,CAAC,GAAGC,EAAE;MACnB;MACA,IAAIG,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACpBZ,KAAK,CAACQ,CAAC,EAAE,CAAC,GAAGI,EAAE;MACnB;IACJ;EACJ;EACA,OAAOJ,CAAC;AACZ;AACA,OAAO,SAASuD,iBAAiBA,CAACvE,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC1C,IAAIsE,OAAO,GAAGxE,EAAE,GAAGE,EAAE,GAAG,CAAC,GAAGD,EAAE;EAC9B,IAAIuE,OAAO,KAAK,CAAC,EAAE;IACf,OAAO,GAAG;EACd,CAAC,MACI;IACD,OAAO,CAACxE,EAAE,GAAGC,EAAE,IAAIuE,OAAO;EAC9B;AACJ;AACA,OAAO,SAASC,kBAAkBA,CAACzE,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEE,CAAC,EAAE+B,GAAG,EAAE;EACnD,IAAIC,GAAG,GAAG,CAACnC,EAAE,GAAGD,EAAE,IAAII,CAAC,GAAGJ,EAAE;EAC5B,IAAIqC,GAAG,GAAG,CAACnC,EAAE,GAAGD,EAAE,IAAIG,CAAC,GAAGH,EAAE;EAC5B,IAAIsC,IAAI,GAAG,CAACF,GAAG,GAAGD,GAAG,IAAIhC,CAAC,GAAGgC,GAAG;EAChCD,GAAG,CAAC,CAAC,CAAC,GAAGnC,EAAE;EACXmC,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG;EACZD,GAAG,CAAC,CAAC,CAAC,GAAGI,IAAI;EACbJ,GAAG,CAAC,CAAC,CAAC,GAAGI,IAAI;EACbJ,GAAG,CAAC,CAAC,CAAC,GAAGE,GAAG;EACZF,GAAG,CAAC,CAAC,CAAC,GAAGjC,EAAE;AACf;AACA,OAAO,SAASwE,qBAAqBA,CAAC/B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,CAAC,EAAEC,CAAC,EAAEjB,GAAG,EAAE;EACrE,IAAI/B,CAAC;EACL,IAAIiD,QAAQ,GAAG,KAAK;EACpB,IAAIzC,CAAC,GAAG0C,QAAQ;EAChB7D,GAAG,CAAC,CAAC,CAAC,GAAG0D,CAAC;EACV1D,GAAG,CAAC,CAAC,CAAC,GAAG2D,CAAC;EACV,KAAK,IAAIO,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAI,IAAI,EAAE;IACjCjE,GAAG,CAAC,CAAC,CAAC,GAAG0E,WAAW,CAACzB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEY,EAAE,CAAC;IACpCjE,GAAG,CAAC,CAAC,CAAC,GAAG0E,WAAW,CAACxB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEW,EAAE,CAAC;IACpC,IAAIF,EAAE,GAAG1E,YAAY,CAACU,GAAG,EAAEC,GAAG,CAAC;IAC/B,IAAI+D,EAAE,GAAG7C,CAAC,EAAE;MACRR,CAAC,GAAGuD,EAAE;MACN/C,CAAC,GAAG6C,EAAE;IACV;EACJ;EACA7C,CAAC,GAAG0C,QAAQ;EACZ,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IACzB,IAAIP,QAAQ,GAAG/D,eAAe,EAAE;MAC5B;IACJ;IACA,IAAIiE,IAAI,GAAGnD,CAAC,GAAGiD,QAAQ;IACvB,IAAIG,IAAI,GAAGpD,CAAC,GAAGiD,QAAQ;IACvB3D,GAAG,CAAC,CAAC,CAAC,GAAG0E,WAAW,CAACzB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEQ,IAAI,CAAC;IACtC7D,GAAG,CAAC,CAAC,CAAC,GAAG0E,WAAW,CAACxB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEO,IAAI,CAAC;IACtC,IAAIE,EAAE,GAAG1E,YAAY,CAACW,GAAG,EAAED,GAAG,CAAC;IAC/B,IAAI8D,IAAI,IAAI,CAAC,IAAIE,EAAE,GAAG7C,CAAC,EAAE;MACrBR,CAAC,GAAGmD,IAAI;MACR3C,CAAC,GAAG6C,EAAE;IACV,CAAC,MACI;MACD9D,GAAG,CAAC,CAAC,CAAC,GAAGyE,WAAW,CAACzB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAES,IAAI,CAAC;MACtC7D,GAAG,CAAC,CAAC,CAAC,GAAGyE,WAAW,CAACxB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEQ,IAAI,CAAC;MACtC,IAAIE,EAAE,GAAG3E,YAAY,CAACY,GAAG,EAAEF,GAAG,CAAC;MAC/B,IAAI+D,IAAI,IAAI,CAAC,IAAIE,EAAE,GAAG9C,CAAC,EAAE;QACrBR,CAAC,GAAGoD,IAAI;QACR5C,CAAC,GAAG8C,EAAE;MACV,CAAC,MACI;QACDL,QAAQ,IAAI,GAAG;MACnB;IACJ;EACJ;EACA,IAAIlB,GAAG,EAAE;IACLA,GAAG,CAAC,CAAC,CAAC,GAAGiC,WAAW,CAACzB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAE3C,CAAC,CAAC;IACnC+B,GAAG,CAAC,CAAC,CAAC,GAAGiC,WAAW,CAACxB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAE5C,CAAC,CAAC;EACvC;EACA,OAAOjB,QAAQ,CAACyB,CAAC,CAAC;AACtB;AACA,OAAO,SAAS+D,eAAeA,CAAChC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEc,SAAS,EAAE;EAC/D,IAAIC,EAAE,GAAGpB,EAAE;EACX,IAAIqB,EAAE,GAAGpB,EAAE;EACX,IAAIhC,CAAC,GAAG,CAAC;EACT,IAAIqD,IAAI,GAAG,CAAC,GAAGH,SAAS;EACxB,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIE,SAAS,EAAEF,CAAC,EAAE,EAAE;IACjC,IAAIxD,CAAC,GAAGwD,CAAC,GAAGK,IAAI;IAChB,IAAId,CAAC,GAAGiB,WAAW,CAACzB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAE3C,CAAC,CAAC;IAClC,IAAIgD,CAAC,GAAGgB,WAAW,CAACxB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAE5C,CAAC,CAAC;IAClC,IAAI8D,EAAE,GAAGf,CAAC,GAAGY,EAAE;IACf,IAAII,EAAE,GAAGf,CAAC,GAAGY,EAAE;IACfpD,CAAC,IAAI3B,IAAI,CAACG,IAAI,CAAC8E,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;IACjCJ,EAAE,GAAGZ,CAAC;IACNa,EAAE,GAAGZ,CAAC;EACV;EACA,OAAOxC,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}