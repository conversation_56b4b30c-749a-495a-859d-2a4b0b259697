{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Scale from './Scale.js';\nimport * as numberUtil from '../util/number.js';\nimport * as scaleHelper from './helper.js';\n// Use some method of IntervalScale\nimport IntervalScale from './Interval.js';\nvar scaleProto = Scale.prototype;\n// FIXME:TS refactor: not good to call it directly with `this`?\nvar intervalScaleProto = IntervalScale.prototype;\nvar roundingErrorFix = numberUtil.round;\nvar mathFloor = Math.floor;\nvar mathCeil = Math.ceil;\nvar mathPow = Math.pow;\nvar mathLog = Math.log;\nvar LogScale = /** @class */function (_super) {\n  __extends(LogScale, _super);\n  function LogScale() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'log';\n    _this.base = 10;\n    _this._originalScale = new IntervalScale();\n    // FIXME:TS actually used by `IntervalScale`\n    _this._interval = 0;\n    return _this;\n  }\n  /**\r\n   * @param Whether expand the ticks to niced extent.\r\n   */\n  LogScale.prototype.getTicks = function (expandToNicedExtent) {\n    var originalScale = this._originalScale;\n    var extent = this._extent;\n    var originalExtent = originalScale.getExtent();\n    var ticks = intervalScaleProto.getTicks.call(this, expandToNicedExtent);\n    return zrUtil.map(ticks, function (tick) {\n      var val = tick.value;\n      var powVal = numberUtil.round(mathPow(this.base, val));\n      // Fix #4158\n      powVal = val === extent[0] && this._fixMin ? fixRoundingError(powVal, originalExtent[0]) : powVal;\n      powVal = val === extent[1] && this._fixMax ? fixRoundingError(powVal, originalExtent[1]) : powVal;\n      return {\n        value: powVal\n      };\n    }, this);\n  };\n  LogScale.prototype.setExtent = function (start, end) {\n    var base = mathLog(this.base);\n    // log(-Infinity) is NaN, so safe guard here\n    start = mathLog(Math.max(0, start)) / base;\n    end = mathLog(Math.max(0, end)) / base;\n    intervalScaleProto.setExtent.call(this, start, end);\n  };\n  /**\r\n   * @return {number} end\r\n   */\n  LogScale.prototype.getExtent = function () {\n    var base = this.base;\n    var extent = scaleProto.getExtent.call(this);\n    extent[0] = mathPow(base, extent[0]);\n    extent[1] = mathPow(base, extent[1]);\n    // Fix #4158\n    var originalScale = this._originalScale;\n    var originalExtent = originalScale.getExtent();\n    this._fixMin && (extent[0] = fixRoundingError(extent[0], originalExtent[0]));\n    this._fixMax && (extent[1] = fixRoundingError(extent[1], originalExtent[1]));\n    return extent;\n  };\n  LogScale.prototype.unionExtent = function (extent) {\n    this._originalScale.unionExtent(extent);\n    var base = this.base;\n    extent[0] = mathLog(extent[0]) / mathLog(base);\n    extent[1] = mathLog(extent[1]) / mathLog(base);\n    scaleProto.unionExtent.call(this, extent);\n  };\n  LogScale.prototype.unionExtentFromData = function (data, dim) {\n    // TODO\n    // filter value that <= 0\n    this.unionExtent(data.getApproximateExtent(dim));\n  };\n  /**\r\n   * Update interval and extent of intervals for nice ticks\r\n   * @param approxTickNum default 10 Given approx tick number\r\n   */\n  LogScale.prototype.calcNiceTicks = function (approxTickNum) {\n    approxTickNum = approxTickNum || 10;\n    var extent = this._extent;\n    var span = extent[1] - extent[0];\n    if (span === Infinity || span <= 0) {\n      return;\n    }\n    var interval = numberUtil.quantity(span);\n    var err = approxTickNum / span * interval;\n    // Filter ticks to get closer to the desired count.\n    if (err <= 0.5) {\n      interval *= 10;\n    }\n    // Interval should be integer\n    while (!isNaN(interval) && Math.abs(interval) < 1 && Math.abs(interval) > 0) {\n      interval *= 10;\n    }\n    var niceExtent = [numberUtil.round(mathCeil(extent[0] / interval) * interval), numberUtil.round(mathFloor(extent[1] / interval) * interval)];\n    this._interval = interval;\n    this._niceExtent = niceExtent;\n  };\n  LogScale.prototype.calcNiceExtent = function (opt) {\n    intervalScaleProto.calcNiceExtent.call(this, opt);\n    this._fixMin = opt.fixMin;\n    this._fixMax = opt.fixMax;\n  };\n  LogScale.prototype.parse = function (val) {\n    return val;\n  };\n  LogScale.prototype.contain = function (val) {\n    val = mathLog(val) / mathLog(this.base);\n    return scaleHelper.contain(val, this._extent);\n  };\n  LogScale.prototype.normalize = function (val) {\n    val = mathLog(val) / mathLog(this.base);\n    return scaleHelper.normalize(val, this._extent);\n  };\n  LogScale.prototype.scale = function (val) {\n    val = scaleHelper.scale(val, this._extent);\n    return mathPow(this.base, val);\n  };\n  LogScale.type = 'log';\n  return LogScale;\n}(Scale);\nvar proto = LogScale.prototype;\nproto.getMinorTicks = intervalScaleProto.getMinorTicks;\nproto.getLabel = intervalScaleProto.getLabel;\nfunction fixRoundingError(val, originalVal) {\n  return roundingErrorFix(val, numberUtil.getPrecision(originalVal));\n}\nScale.registerClass(LogScale);\nexport default LogScale;", "map": {"version": 3, "names": ["__extends", "zrUtil", "Scale", "numberUtil", "scaleHelper", "IntervalScale", "scaleProto", "prototype", "intervalScaleProto", "roundingErrorFix", "round", "mathFloor", "Math", "floor", "math<PERSON>eil", "ceil", "mathPow", "pow", "mathLog", "log", "LogScale", "_super", "_this", "apply", "arguments", "type", "base", "_originalScale", "_interval", "getTicks", "expandToNicedExtent", "originalScale", "extent", "_extent", "originalExtent", "getExtent", "ticks", "call", "map", "tick", "val", "value", "powVal", "_fixMin", "fixRoundingError", "_fixMax", "setExtent", "start", "end", "max", "unionExtent", "unionExtentFromData", "data", "dim", "getApproximateExtent", "calcNiceTicks", "approxTickNum", "span", "Infinity", "interval", "quantity", "err", "isNaN", "abs", "niceExtent", "_niceExtent", "calcNiceExtent", "opt", "fixMin", "fixMax", "parse", "contain", "normalize", "scale", "proto", "getMinorTicks", "get<PERSON><PERSON><PERSON>", "originalVal", "getPrecision", "registerClass"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/node_modules/echarts/lib/scale/Log.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Scale from './Scale.js';\nimport * as numberUtil from '../util/number.js';\nimport * as scaleHelper from './helper.js';\n// Use some method of IntervalScale\nimport IntervalScale from './Interval.js';\nvar scaleProto = Scale.prototype;\n// FIXME:TS refactor: not good to call it directly with `this`?\nvar intervalScaleProto = IntervalScale.prototype;\nvar roundingErrorFix = numberUtil.round;\nvar mathFloor = Math.floor;\nvar mathCeil = Math.ceil;\nvar mathPow = Math.pow;\nvar mathLog = Math.log;\nvar LogScale = /** @class */function (_super) {\n  __extends(LogScale, _super);\n  function LogScale() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'log';\n    _this.base = 10;\n    _this._originalScale = new IntervalScale();\n    // FIXME:TS actually used by `IntervalScale`\n    _this._interval = 0;\n    return _this;\n  }\n  /**\r\n   * @param Whether expand the ticks to niced extent.\r\n   */\n  LogScale.prototype.getTicks = function (expandToNicedExtent) {\n    var originalScale = this._originalScale;\n    var extent = this._extent;\n    var originalExtent = originalScale.getExtent();\n    var ticks = intervalScaleProto.getTicks.call(this, expandToNicedExtent);\n    return zrUtil.map(ticks, function (tick) {\n      var val = tick.value;\n      var powVal = numberUtil.round(mathPow(this.base, val));\n      // Fix #4158\n      powVal = val === extent[0] && this._fixMin ? fixRoundingError(powVal, originalExtent[0]) : powVal;\n      powVal = val === extent[1] && this._fixMax ? fixRoundingError(powVal, originalExtent[1]) : powVal;\n      return {\n        value: powVal\n      };\n    }, this);\n  };\n  LogScale.prototype.setExtent = function (start, end) {\n    var base = mathLog(this.base);\n    // log(-Infinity) is NaN, so safe guard here\n    start = mathLog(Math.max(0, start)) / base;\n    end = mathLog(Math.max(0, end)) / base;\n    intervalScaleProto.setExtent.call(this, start, end);\n  };\n  /**\r\n   * @return {number} end\r\n   */\n  LogScale.prototype.getExtent = function () {\n    var base = this.base;\n    var extent = scaleProto.getExtent.call(this);\n    extent[0] = mathPow(base, extent[0]);\n    extent[1] = mathPow(base, extent[1]);\n    // Fix #4158\n    var originalScale = this._originalScale;\n    var originalExtent = originalScale.getExtent();\n    this._fixMin && (extent[0] = fixRoundingError(extent[0], originalExtent[0]));\n    this._fixMax && (extent[1] = fixRoundingError(extent[1], originalExtent[1]));\n    return extent;\n  };\n  LogScale.prototype.unionExtent = function (extent) {\n    this._originalScale.unionExtent(extent);\n    var base = this.base;\n    extent[0] = mathLog(extent[0]) / mathLog(base);\n    extent[1] = mathLog(extent[1]) / mathLog(base);\n    scaleProto.unionExtent.call(this, extent);\n  };\n  LogScale.prototype.unionExtentFromData = function (data, dim) {\n    // TODO\n    // filter value that <= 0\n    this.unionExtent(data.getApproximateExtent(dim));\n  };\n  /**\r\n   * Update interval and extent of intervals for nice ticks\r\n   * @param approxTickNum default 10 Given approx tick number\r\n   */\n  LogScale.prototype.calcNiceTicks = function (approxTickNum) {\n    approxTickNum = approxTickNum || 10;\n    var extent = this._extent;\n    var span = extent[1] - extent[0];\n    if (span === Infinity || span <= 0) {\n      return;\n    }\n    var interval = numberUtil.quantity(span);\n    var err = approxTickNum / span * interval;\n    // Filter ticks to get closer to the desired count.\n    if (err <= 0.5) {\n      interval *= 10;\n    }\n    // Interval should be integer\n    while (!isNaN(interval) && Math.abs(interval) < 1 && Math.abs(interval) > 0) {\n      interval *= 10;\n    }\n    var niceExtent = [numberUtil.round(mathCeil(extent[0] / interval) * interval), numberUtil.round(mathFloor(extent[1] / interval) * interval)];\n    this._interval = interval;\n    this._niceExtent = niceExtent;\n  };\n  LogScale.prototype.calcNiceExtent = function (opt) {\n    intervalScaleProto.calcNiceExtent.call(this, opt);\n    this._fixMin = opt.fixMin;\n    this._fixMax = opt.fixMax;\n  };\n  LogScale.prototype.parse = function (val) {\n    return val;\n  };\n  LogScale.prototype.contain = function (val) {\n    val = mathLog(val) / mathLog(this.base);\n    return scaleHelper.contain(val, this._extent);\n  };\n  LogScale.prototype.normalize = function (val) {\n    val = mathLog(val) / mathLog(this.base);\n    return scaleHelper.normalize(val, this._extent);\n  };\n  LogScale.prototype.scale = function (val) {\n    val = scaleHelper.scale(val, this._extent);\n    return mathPow(this.base, val);\n  };\n  LogScale.type = 'log';\n  return LogScale;\n}(Scale);\nvar proto = LogScale.prototype;\nproto.getMinorTicks = intervalScaleProto.getMinorTicks;\nproto.getLabel = intervalScaleProto.getLabel;\nfunction fixRoundingError(val, originalVal) {\n  return roundingErrorFix(val, numberUtil.getPrecision(originalVal));\n}\nScale.registerClass(LogScale);\nexport default LogScale;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAO,KAAKC,UAAU,MAAM,mBAAmB;AAC/C,OAAO,KAAKC,WAAW,MAAM,aAAa;AAC1C;AACA,OAAOC,aAAa,MAAM,eAAe;AACzC,IAAIC,UAAU,GAAGJ,KAAK,CAACK,SAAS;AAChC;AACA,IAAIC,kBAAkB,GAAGH,aAAa,CAACE,SAAS;AAChD,IAAIE,gBAAgB,GAAGN,UAAU,CAACO,KAAK;AACvC,IAAIC,SAAS,GAAGC,IAAI,CAACC,KAAK;AAC1B,IAAIC,QAAQ,GAAGF,IAAI,CAACG,IAAI;AACxB,IAAIC,OAAO,GAAGJ,IAAI,CAACK,GAAG;AACtB,IAAIC,OAAO,GAAGN,IAAI,CAACO,GAAG;AACtB,IAAIC,QAAQ,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC5CrB,SAAS,CAACoB,QAAQ,EAAEC,MAAM,CAAC;EAC3B,SAASD,QAAQA,CAAA,EAAG;IAClB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAG,KAAK;IAClBH,KAAK,CAACI,IAAI,GAAG,EAAE;IACfJ,KAAK,CAACK,cAAc,GAAG,IAAItB,aAAa,CAAC,CAAC;IAC1C;IACAiB,KAAK,CAACM,SAAS,GAAG,CAAC;IACnB,OAAON,KAAK;EACd;EACA;AACF;AACA;EACEF,QAAQ,CAACb,SAAS,CAACsB,QAAQ,GAAG,UAAUC,mBAAmB,EAAE;IAC3D,IAAIC,aAAa,GAAG,IAAI,CAACJ,cAAc;IACvC,IAAIK,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAIC,cAAc,GAAGH,aAAa,CAACI,SAAS,CAAC,CAAC;IAC9C,IAAIC,KAAK,GAAG5B,kBAAkB,CAACqB,QAAQ,CAACQ,IAAI,CAAC,IAAI,EAAEP,mBAAmB,CAAC;IACvE,OAAO7B,MAAM,CAACqC,GAAG,CAACF,KAAK,EAAE,UAAUG,IAAI,EAAE;MACvC,IAAIC,GAAG,GAAGD,IAAI,CAACE,KAAK;MACpB,IAAIC,MAAM,GAAGvC,UAAU,CAACO,KAAK,CAACM,OAAO,CAAC,IAAI,CAACU,IAAI,EAAEc,GAAG,CAAC,CAAC;MACtD;MACAE,MAAM,GAAGF,GAAG,KAAKR,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAACW,OAAO,GAAGC,gBAAgB,CAACF,MAAM,EAAER,cAAc,CAAC,CAAC,CAAC,CAAC,GAAGQ,MAAM;MACjGA,MAAM,GAAGF,GAAG,KAAKR,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,CAACa,OAAO,GAAGD,gBAAgB,CAACF,MAAM,EAAER,cAAc,CAAC,CAAC,CAAC,CAAC,GAAGQ,MAAM;MACjG,OAAO;QACLD,KAAK,EAAEC;MACT,CAAC;IACH,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACDtB,QAAQ,CAACb,SAAS,CAACuC,SAAS,GAAG,UAAUC,KAAK,EAAEC,GAAG,EAAE;IACnD,IAAItB,IAAI,GAAGR,OAAO,CAAC,IAAI,CAACQ,IAAI,CAAC;IAC7B;IACAqB,KAAK,GAAG7B,OAAO,CAACN,IAAI,CAACqC,GAAG,CAAC,CAAC,EAAEF,KAAK,CAAC,CAAC,GAAGrB,IAAI;IAC1CsB,GAAG,GAAG9B,OAAO,CAACN,IAAI,CAACqC,GAAG,CAAC,CAAC,EAAED,GAAG,CAAC,CAAC,GAAGtB,IAAI;IACtClB,kBAAkB,CAACsC,SAAS,CAACT,IAAI,CAAC,IAAI,EAAEU,KAAK,EAAEC,GAAG,CAAC;EACrD,CAAC;EACD;AACF;AACA;EACE5B,QAAQ,CAACb,SAAS,CAAC4B,SAAS,GAAG,YAAY;IACzC,IAAIT,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAIM,MAAM,GAAG1B,UAAU,CAAC6B,SAAS,CAACE,IAAI,CAAC,IAAI,CAAC;IAC5CL,MAAM,CAAC,CAAC,CAAC,GAAGhB,OAAO,CAACU,IAAI,EAAEM,MAAM,CAAC,CAAC,CAAC,CAAC;IACpCA,MAAM,CAAC,CAAC,CAAC,GAAGhB,OAAO,CAACU,IAAI,EAAEM,MAAM,CAAC,CAAC,CAAC,CAAC;IACpC;IACA,IAAID,aAAa,GAAG,IAAI,CAACJ,cAAc;IACvC,IAAIO,cAAc,GAAGH,aAAa,CAACI,SAAS,CAAC,CAAC;IAC9C,IAAI,CAACQ,OAAO,KAAKX,MAAM,CAAC,CAAC,CAAC,GAAGY,gBAAgB,CAACZ,MAAM,CAAC,CAAC,CAAC,EAAEE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,IAAI,CAACW,OAAO,KAAKb,MAAM,CAAC,CAAC,CAAC,GAAGY,gBAAgB,CAACZ,MAAM,CAAC,CAAC,CAAC,EAAEE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,OAAOF,MAAM;EACf,CAAC;EACDZ,QAAQ,CAACb,SAAS,CAAC2C,WAAW,GAAG,UAAUlB,MAAM,EAAE;IACjD,IAAI,CAACL,cAAc,CAACuB,WAAW,CAAClB,MAAM,CAAC;IACvC,IAAIN,IAAI,GAAG,IAAI,CAACA,IAAI;IACpBM,MAAM,CAAC,CAAC,CAAC,GAAGd,OAAO,CAACc,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGd,OAAO,CAACQ,IAAI,CAAC;IAC9CM,MAAM,CAAC,CAAC,CAAC,GAAGd,OAAO,CAACc,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGd,OAAO,CAACQ,IAAI,CAAC;IAC9CpB,UAAU,CAAC4C,WAAW,CAACb,IAAI,CAAC,IAAI,EAAEL,MAAM,CAAC;EAC3C,CAAC;EACDZ,QAAQ,CAACb,SAAS,CAAC4C,mBAAmB,GAAG,UAAUC,IAAI,EAAEC,GAAG,EAAE;IAC5D;IACA;IACA,IAAI,CAACH,WAAW,CAACE,IAAI,CAACE,oBAAoB,CAACD,GAAG,CAAC,CAAC;EAClD,CAAC;EACD;AACF;AACA;AACA;EACEjC,QAAQ,CAACb,SAAS,CAACgD,aAAa,GAAG,UAAUC,aAAa,EAAE;IAC1DA,aAAa,GAAGA,aAAa,IAAI,EAAE;IACnC,IAAIxB,MAAM,GAAG,IAAI,CAACC,OAAO;IACzB,IAAIwB,IAAI,GAAGzB,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;IAChC,IAAIyB,IAAI,KAAKC,QAAQ,IAAID,IAAI,IAAI,CAAC,EAAE;MAClC;IACF;IACA,IAAIE,QAAQ,GAAGxD,UAAU,CAACyD,QAAQ,CAACH,IAAI,CAAC;IACxC,IAAII,GAAG,GAAGL,aAAa,GAAGC,IAAI,GAAGE,QAAQ;IACzC;IACA,IAAIE,GAAG,IAAI,GAAG,EAAE;MACdF,QAAQ,IAAI,EAAE;IAChB;IACA;IACA,OAAO,CAACG,KAAK,CAACH,QAAQ,CAAC,IAAI/C,IAAI,CAACmD,GAAG,CAACJ,QAAQ,CAAC,GAAG,CAAC,IAAI/C,IAAI,CAACmD,GAAG,CAACJ,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC3EA,QAAQ,IAAI,EAAE;IAChB;IACA,IAAIK,UAAU,GAAG,CAAC7D,UAAU,CAACO,KAAK,CAACI,QAAQ,CAACkB,MAAM,CAAC,CAAC,CAAC,GAAG2B,QAAQ,CAAC,GAAGA,QAAQ,CAAC,EAAExD,UAAU,CAACO,KAAK,CAACC,SAAS,CAACqB,MAAM,CAAC,CAAC,CAAC,GAAG2B,QAAQ,CAAC,GAAGA,QAAQ,CAAC,CAAC;IAC5I,IAAI,CAAC/B,SAAS,GAAG+B,QAAQ;IACzB,IAAI,CAACM,WAAW,GAAGD,UAAU;EAC/B,CAAC;EACD5C,QAAQ,CAACb,SAAS,CAAC2D,cAAc,GAAG,UAAUC,GAAG,EAAE;IACjD3D,kBAAkB,CAAC0D,cAAc,CAAC7B,IAAI,CAAC,IAAI,EAAE8B,GAAG,CAAC;IACjD,IAAI,CAACxB,OAAO,GAAGwB,GAAG,CAACC,MAAM;IACzB,IAAI,CAACvB,OAAO,GAAGsB,GAAG,CAACE,MAAM;EAC3B,CAAC;EACDjD,QAAQ,CAACb,SAAS,CAAC+D,KAAK,GAAG,UAAU9B,GAAG,EAAE;IACxC,OAAOA,GAAG;EACZ,CAAC;EACDpB,QAAQ,CAACb,SAAS,CAACgE,OAAO,GAAG,UAAU/B,GAAG,EAAE;IAC1CA,GAAG,GAAGtB,OAAO,CAACsB,GAAG,CAAC,GAAGtB,OAAO,CAAC,IAAI,CAACQ,IAAI,CAAC;IACvC,OAAOtB,WAAW,CAACmE,OAAO,CAAC/B,GAAG,EAAE,IAAI,CAACP,OAAO,CAAC;EAC/C,CAAC;EACDb,QAAQ,CAACb,SAAS,CAACiE,SAAS,GAAG,UAAUhC,GAAG,EAAE;IAC5CA,GAAG,GAAGtB,OAAO,CAACsB,GAAG,CAAC,GAAGtB,OAAO,CAAC,IAAI,CAACQ,IAAI,CAAC;IACvC,OAAOtB,WAAW,CAACoE,SAAS,CAAChC,GAAG,EAAE,IAAI,CAACP,OAAO,CAAC;EACjD,CAAC;EACDb,QAAQ,CAACb,SAAS,CAACkE,KAAK,GAAG,UAAUjC,GAAG,EAAE;IACxCA,GAAG,GAAGpC,WAAW,CAACqE,KAAK,CAACjC,GAAG,EAAE,IAAI,CAACP,OAAO,CAAC;IAC1C,OAAOjB,OAAO,CAAC,IAAI,CAACU,IAAI,EAAEc,GAAG,CAAC;EAChC,CAAC;EACDpB,QAAQ,CAACK,IAAI,GAAG,KAAK;EACrB,OAAOL,QAAQ;AACjB,CAAC,CAAClB,KAAK,CAAC;AACR,IAAIwE,KAAK,GAAGtD,QAAQ,CAACb,SAAS;AAC9BmE,KAAK,CAACC,aAAa,GAAGnE,kBAAkB,CAACmE,aAAa;AACtDD,KAAK,CAACE,QAAQ,GAAGpE,kBAAkB,CAACoE,QAAQ;AAC5C,SAAShC,gBAAgBA,CAACJ,GAAG,EAAEqC,WAAW,EAAE;EAC1C,OAAOpE,gBAAgB,CAAC+B,GAAG,EAAErC,UAAU,CAAC2E,YAAY,CAACD,WAAW,CAAC,CAAC;AACpE;AACA3E,KAAK,CAAC6E,aAAa,CAAC3D,QAAQ,CAAC;AAC7B,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}