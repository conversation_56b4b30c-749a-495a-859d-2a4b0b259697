{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/quant/AIQuant7/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { Layout, Menu } from 'antd';\nimport { DashboardOutlined, LineChartOutlined, FireOutlined, CrownOutlined, BellOutlined, WalletOutlined, SettingOutlined } from '@ant-design/icons';\nimport Dashboard from './pages/Dashboard';\nimport MarketSentiment from './pages/MarketSentiment';\nimport ThemeAnalysis from './pages/ThemeAnalysis';\nimport LeaderStocks from './pages/LeaderStocks';\nimport TradingSignals from './pages/TradingSignals';\nimport Portfolio from './pages/Portfolio';\nimport Settings from './pages/Settings';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Content\n} = Layout;\nconst menuItems = [{\n  key: '/',\n  icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 11\n  }, this),\n  label: '总览'\n}, {\n  key: '/sentiment',\n  icon: /*#__PURE__*/_jsxDEV(LineChartOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 11\n  }, this),\n  label: '市场情绪'\n}, {\n  key: '/themes',\n  icon: /*#__PURE__*/_jsxDEV(FireOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 11\n  }, this),\n  label: '题材分析'\n}, {\n  key: '/leaders',\n  icon: /*#__PURE__*/_jsxDEV(CrownOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 11\n  }, this),\n  label: '龙头股'\n}, {\n  key: '/signals',\n  icon: /*#__PURE__*/_jsxDEV(SignalOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 11\n  }, this),\n  label: '交易信号'\n}, {\n  key: '/portfolio',\n  icon: /*#__PURE__*/_jsxDEV(WalletOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 11\n  }, this),\n  label: '持仓管理'\n}, {\n  key: '/settings',\n  icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 11\n  }, this),\n  label: '系统设置'\n}];\nfunction App() {\n  _s();\n  const [selectedKey, setSelectedKey] = React.useState('/');\n  React.useEffect(() => {\n    setSelectedKey(window.location.pathname);\n  }, []);\n  const handleMenuClick = e => {\n    setSelectedKey(e.key);\n    window.history.pushState(null, '', e.key);\n  };\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    className: \"app-layout\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      className: \"app-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"app-logo\",\n        children: \"AIQuant7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        theme: \"dark\",\n        mode: \"horizontal\",\n        selectedKeys: [selectedKey],\n        items: menuItems,\n        onClick: handleMenuClick,\n        className: \"app-menu\",\n        style: {\n          flex: 1,\n          minWidth: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Content, {\n      className: \"app-content\",\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/sentiment\",\n          element: /*#__PURE__*/_jsxDEV(MarketSentiment, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/themes\",\n          element: /*#__PURE__*/_jsxDEV(ThemeAnalysis, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 42\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/leaders\",\n          element: /*#__PURE__*/_jsxDEV(LeaderStocks, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/signals\",\n          element: /*#__PURE__*/_jsxDEV(TradingSignals, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/portfolio\",\n          element: /*#__PURE__*/_jsxDEV(Portfolio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/settings\",\n          element: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"I2Uub+1kB+miKMglnVJJMn6bH9w=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Layout", "<PERSON><PERSON>", "DashboardOutlined", "LineChartOutlined", "FireOutlined", "CrownOutlined", "BellOutlined", "WalletOutlined", "SettingOutlined", "Dashboard", "MarketSentiment", "ThemeAnalysis", "LeaderStocks", "TradingSignals", "Portfolio", "Settings", "jsxDEV", "_jsxDEV", "Header", "Content", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "SignalOutlined", "App", "_s", "<PERSON><PERSON><PERSON>", "setSelectedKey", "useState", "useEffect", "window", "location", "pathname", "handleMenuClick", "e", "history", "pushState", "className", "children", "theme", "mode", "<PERSON><PERSON><PERSON><PERSON>", "items", "onClick", "style", "flex", "min<PERSON><PERSON><PERSON>", "path", "element", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { Layout, Menu } from 'antd';\nimport {\n  DashboardOutlined,\n  LineChartOutlined,\n  FireOutlined,\n  CrownOutlined,\n  BellOutlined,\n  WalletOutlined,\n  SettingOutlined\n} from '@ant-design/icons';\nimport Dashboard from './pages/Dashboard';\nimport MarketSentiment from './pages/MarketSentiment';\nimport ThemeAnalysis from './pages/ThemeAnalysis';\nimport LeaderStocks from './pages/LeaderStocks';\nimport TradingSignals from './pages/TradingSignals';\nimport Portfolio from './pages/Portfolio';\nimport Settings from './pages/Settings';\n\nconst { Header, Content } = Layout;\n\nconst menuItems = [\n  {\n    key: '/',\n    icon: <DashboardOutlined />,\n    label: '总览'\n  },\n  {\n    key: '/sentiment',\n    icon: <LineChartOutlined />,\n    label: '市场情绪'\n  },\n  {\n    key: '/themes',\n    icon: <FireOutlined />,\n    label: '题材分析'\n  },\n  {\n    key: '/leaders',\n    icon: <CrownOutlined />,\n    label: '龙头股'\n  },\n  {\n    key: '/signals',\n    icon: <SignalOutlined />,\n    label: '交易信号'\n  },\n  {\n    key: '/portfolio',\n    icon: <WalletOutlined />,\n    label: '持仓管理'\n  },\n  {\n    key: '/settings',\n    icon: <SettingOutlined />,\n    label: '系统设置'\n  }\n];\n\nfunction App() {\n  const [selectedKey, setSelectedKey] = React.useState('/');\n\n  React.useEffect(() => {\n    setSelectedKey(window.location.pathname);\n  }, []);\n\n  const handleMenuClick = (e) => {\n    setSelectedKey(e.key);\n    window.history.pushState(null, '', e.key);\n  };\n\n  return (\n    <Layout className=\"app-layout\">\n      <Header className=\"app-header\">\n        <div className=\"app-logo\">\n          AIQuant7\n        </div>\n        <Menu\n          theme=\"dark\"\n          mode=\"horizontal\"\n          selectedKeys={[selectedKey]}\n          items={menuItems}\n          onClick={handleMenuClick}\n          className=\"app-menu\"\n          style={{ flex: 1, minWidth: 0 }}\n        />\n      </Header>\n      \n      <Content className=\"app-content\">\n        <Routes>\n          <Route path=\"/\" element={<Dashboard />} />\n          <Route path=\"/sentiment\" element={<MarketSentiment />} />\n          <Route path=\"/themes\" element={<ThemeAnalysis />} />\n          <Route path=\"/leaders\" element={<LeaderStocks />} />\n          <Route path=\"/signals\" element={<TradingSignals />} />\n          <Route path=\"/portfolio\" element={<Portfolio />} />\n          <Route path=\"/settings\" element={<Settings />} />\n        </Routes>\n      </Content>\n    </Layout>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,MAAM,EAAEC,IAAI,QAAQ,MAAM;AACnC,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,cAAc,EACdC,eAAe,QACV,mBAAmB;AAC1B,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAM;EAAEC,MAAM;EAAEC;AAAQ,CAAC,GAAGnB,MAAM;AAElC,MAAMoB,SAAS,GAAG,CAChB;EACEC,GAAG,EAAE,GAAG;EACRC,IAAI,eAAEL,OAAA,CAACf,iBAAiB;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC3BC,KAAK,EAAE;AACT,CAAC,EACD;EACEN,GAAG,EAAE,YAAY;EACjBC,IAAI,eAAEL,OAAA,CAACd,iBAAiB;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC3BC,KAAK,EAAE;AACT,CAAC,EACD;EACEN,GAAG,EAAE,SAAS;EACdC,IAAI,eAAEL,OAAA,CAACb,YAAY;IAAAmB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACtBC,KAAK,EAAE;AACT,CAAC,EACD;EACEN,GAAG,EAAE,UAAU;EACfC,IAAI,eAAEL,OAAA,CAACZ,aAAa;IAAAkB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACvBC,KAAK,EAAE;AACT,CAAC,EACD;EACEN,GAAG,EAAE,UAAU;EACfC,IAAI,eAAEL,OAAA,CAACW,cAAc;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxBC,KAAK,EAAE;AACT,CAAC,EACD;EACEN,GAAG,EAAE,YAAY;EACjBC,IAAI,eAAEL,OAAA,CAACV,cAAc;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxBC,KAAK,EAAE;AACT,CAAC,EACD;EACEN,GAAG,EAAE,WAAW;EAChBC,IAAI,eAAEL,OAAA,CAACT,eAAe;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACzBC,KAAK,EAAE;AACT,CAAC,CACF;AAED,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,KAAK,CAACoC,QAAQ,CAAC,GAAG,CAAC;EAEzDpC,KAAK,CAACqC,SAAS,CAAC,MAAM;IACpBF,cAAc,CAACG,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,eAAe,GAAIC,CAAC,IAAK;IAC7BP,cAAc,CAACO,CAAC,CAAClB,GAAG,CAAC;IACrBc,MAAM,CAACK,OAAO,CAACC,SAAS,CAAC,IAAI,EAAE,EAAE,EAAEF,CAAC,CAAClB,GAAG,CAAC;EAC3C,CAAC;EAED,oBACEJ,OAAA,CAACjB,MAAM;IAAC0C,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAC5B1B,OAAA,CAACC,MAAM;MAACwB,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC5B1B,OAAA;QAAKyB,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAC;MAE1B;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNT,OAAA,CAAChB,IAAI;QACH2C,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC,YAAY;QACjBC,YAAY,EAAE,CAACf,WAAW,CAAE;QAC5BgB,KAAK,EAAE3B,SAAU;QACjB4B,OAAO,EAAEV,eAAgB;QACzBI,SAAS,EAAC,UAAU;QACpBO,KAAK,EAAE;UAAEC,IAAI,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAE;MAAE;QAAA5B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAETT,OAAA,CAACE,OAAO;MAACuB,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC9B1B,OAAA,CAACnB,MAAM;QAAA6C,QAAA,gBACL1B,OAAA,CAAClB,KAAK;UAACqD,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEpC,OAAA,CAACR,SAAS;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CT,OAAA,CAAClB,KAAK;UAACqD,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEpC,OAAA,CAACP,eAAe;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDT,OAAA,CAAClB,KAAK;UAACqD,IAAI,EAAC,SAAS;UAACC,OAAO,eAAEpC,OAAA,CAACN,aAAa;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDT,OAAA,CAAClB,KAAK;UAACqD,IAAI,EAAC,UAAU;UAACC,OAAO,eAAEpC,OAAA,CAACL,YAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDT,OAAA,CAAClB,KAAK;UAACqD,IAAI,EAAC,UAAU;UAACC,OAAO,eAAEpC,OAAA,CAACJ,cAAc;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDT,OAAA,CAAClB,KAAK;UAACqD,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEpC,OAAA,CAACH,SAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnDT,OAAA,CAAClB,KAAK;UAACqD,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEpC,OAAA,CAACF,QAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb;AAACI,EAAA,CA1CQD,GAAG;AAAAyB,EAAA,GAAHzB,GAAG;AA4CZ,eAAeA,GAAG;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}