{"ast": null, "code": "import { format } from \"../util\";\nvar range = function range(rule, value, source, errors, options) {\n  var len = typeof rule.len === 'number';\n  var min = typeof rule.min === 'number';\n  var max = typeof rule.max === 'number';\n  // 正则匹配码点范围从U+010000一直到U+10FFFF的文字（补充平面Supplementary Plane）\n  var spRegexp = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n  var val = value;\n  var key = null;\n  var num = typeof value === 'number';\n  var str = typeof value === 'string';\n  var arr = Array.isArray(value);\n  if (num) {\n    key = 'number';\n  } else if (str) {\n    key = 'string';\n  } else if (arr) {\n    key = 'array';\n  }\n  // if the value is not of a supported type for range validation\n  // the validation rule rule should use the\n  // type property to also test for a particular type\n  if (!key) {\n    return false;\n  }\n  if (arr) {\n    val = value.length;\n  }\n  if (str) {\n    // 处理码点大于U+010000的文字length属性不准确的bug，如\"𠮷𠮷𠮷\".length !== 3\n    val = value.replace(spRegexp, '_').length;\n  }\n  if (len) {\n    if (val !== rule.len) {\n      errors.push(format(options.messages[key].len, rule.fullField, rule.len));\n    }\n  } else if (min && !max && val < rule.min) {\n    errors.push(format(options.messages[key].min, rule.fullField, rule.min));\n  } else if (max && !min && val > rule.max) {\n    errors.push(format(options.messages[key].max, rule.fullField, rule.max));\n  } else if (min && max && (val < rule.min || val > rule.max)) {\n    errors.push(format(options.messages[key].range, rule.fullField, rule.min, rule.max));\n  }\n};\nexport default range;", "map": {"version": 3, "names": ["format", "range", "rule", "value", "source", "errors", "options", "len", "min", "max", "spRegexp", "val", "key", "num", "str", "arr", "Array", "isArray", "length", "replace", "push", "messages", "fullField"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/node_modules/@rc-component/async-validator/es/rule/range.js"], "sourcesContent": ["import { format } from \"../util\";\nvar range = function range(rule, value, source, errors, options) {\n  var len = typeof rule.len === 'number';\n  var min = typeof rule.min === 'number';\n  var max = typeof rule.max === 'number';\n  // 正则匹配码点范围从U+010000一直到U+10FFFF的文字（补充平面Supplementary Plane）\n  var spRegexp = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n  var val = value;\n  var key = null;\n  var num = typeof value === 'number';\n  var str = typeof value === 'string';\n  var arr = Array.isArray(value);\n  if (num) {\n    key = 'number';\n  } else if (str) {\n    key = 'string';\n  } else if (arr) {\n    key = 'array';\n  }\n  // if the value is not of a supported type for range validation\n  // the validation rule rule should use the\n  // type property to also test for a particular type\n  if (!key) {\n    return false;\n  }\n  if (arr) {\n    val = value.length;\n  }\n  if (str) {\n    // 处理码点大于U+010000的文字length属性不准确的bug，如\"𠮷𠮷𠮷\".length !== 3\n    val = value.replace(spRegexp, '_').length;\n  }\n  if (len) {\n    if (val !== rule.len) {\n      errors.push(format(options.messages[key].len, rule.fullField, rule.len));\n    }\n  } else if (min && !max && val < rule.min) {\n    errors.push(format(options.messages[key].min, rule.fullField, rule.min));\n  } else if (max && !min && val > rule.max) {\n    errors.push(format(options.messages[key].max, rule.fullField, rule.max));\n  } else if (min && max && (val < rule.min || val > rule.max)) {\n    errors.push(format(options.messages[key].range, rule.fullField, rule.min, rule.max));\n  }\n};\nexport default range;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC/D,IAAIC,GAAG,GAAG,OAAOL,IAAI,CAACK,GAAG,KAAK,QAAQ;EACtC,IAAIC,GAAG,GAAG,OAAON,IAAI,CAACM,GAAG,KAAK,QAAQ;EACtC,IAAIC,GAAG,GAAG,OAAOP,IAAI,CAACO,GAAG,KAAK,QAAQ;EACtC;EACA,IAAIC,QAAQ,GAAG,iCAAiC;EAChD,IAAIC,GAAG,GAAGR,KAAK;EACf,IAAIS,GAAG,GAAG,IAAI;EACd,IAAIC,GAAG,GAAG,OAAOV,KAAK,KAAK,QAAQ;EACnC,IAAIW,GAAG,GAAG,OAAOX,KAAK,KAAK,QAAQ;EACnC,IAAIY,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACd,KAAK,CAAC;EAC9B,IAAIU,GAAG,EAAE;IACPD,GAAG,GAAG,QAAQ;EAChB,CAAC,MAAM,IAAIE,GAAG,EAAE;IACdF,GAAG,GAAG,QAAQ;EAChB,CAAC,MAAM,IAAIG,GAAG,EAAE;IACdH,GAAG,GAAG,OAAO;EACf;EACA;EACA;EACA;EACA,IAAI,CAACA,GAAG,EAAE;IACR,OAAO,KAAK;EACd;EACA,IAAIG,GAAG,EAAE;IACPJ,GAAG,GAAGR,KAAK,CAACe,MAAM;EACpB;EACA,IAAIJ,GAAG,EAAE;IACP;IACAH,GAAG,GAAGR,KAAK,CAACgB,OAAO,CAACT,QAAQ,EAAE,GAAG,CAAC,CAACQ,MAAM;EAC3C;EACA,IAAIX,GAAG,EAAE;IACP,IAAII,GAAG,KAAKT,IAAI,CAACK,GAAG,EAAE;MACpBF,MAAM,CAACe,IAAI,CAACpB,MAAM,CAACM,OAAO,CAACe,QAAQ,CAACT,GAAG,CAAC,CAACL,GAAG,EAAEL,IAAI,CAACoB,SAAS,EAAEpB,IAAI,CAACK,GAAG,CAAC,CAAC;IAC1E;EACF,CAAC,MAAM,IAAIC,GAAG,IAAI,CAACC,GAAG,IAAIE,GAAG,GAAGT,IAAI,CAACM,GAAG,EAAE;IACxCH,MAAM,CAACe,IAAI,CAACpB,MAAM,CAACM,OAAO,CAACe,QAAQ,CAACT,GAAG,CAAC,CAACJ,GAAG,EAAEN,IAAI,CAACoB,SAAS,EAAEpB,IAAI,CAACM,GAAG,CAAC,CAAC;EAC1E,CAAC,MAAM,IAAIC,GAAG,IAAI,CAACD,GAAG,IAAIG,GAAG,GAAGT,IAAI,CAACO,GAAG,EAAE;IACxCJ,MAAM,CAACe,IAAI,CAACpB,MAAM,CAACM,OAAO,CAACe,QAAQ,CAACT,GAAG,CAAC,CAACH,GAAG,EAAEP,IAAI,CAACoB,SAAS,EAAEpB,IAAI,CAACO,GAAG,CAAC,CAAC;EAC1E,CAAC,MAAM,IAAID,GAAG,IAAIC,GAAG,KAAKE,GAAG,GAAGT,IAAI,CAACM,GAAG,IAAIG,GAAG,GAAGT,IAAI,CAACO,GAAG,CAAC,EAAE;IAC3DJ,MAAM,CAACe,IAAI,CAACpB,MAAM,CAACM,OAAO,CAACe,QAAQ,CAACT,GAAG,CAAC,CAACX,KAAK,EAAEC,IAAI,CAACoB,SAAS,EAAEpB,IAAI,CAACM,GAAG,EAAEN,IAAI,CAACO,GAAG,CAAC,CAAC;EACtF;AACF,CAAC;AACD,eAAeR,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}