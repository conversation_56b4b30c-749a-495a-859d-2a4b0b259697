{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\nvar _default = function _default(fn) {\n  var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 60;\n  var timer = null;\n  return function () {\n    var _this = this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    clearTimeout(timer);\n    timer = setTimeout(function () {\n      fn.apply(_this, args);\n    }, delay);\n  };\n};\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_default", "fn", "delay", "arguments", "length", "undefined", "timer", "_this", "_len", "args", "Array", "_key", "clearTimeout", "setTimeout", "apply"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/node_modules/size-sensor/lib/debounce.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n * Created by hustcc on 18/6/9.\n * Contract: <EMAIL>\n */\nvar _default = function _default(fn) {\n  var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 60;\n  var timer = null;\n  return function () {\n    var _this = this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    clearTimeout(timer);\n    timer = setTimeout(function () {\n      fn.apply(_this, args);\n    }, delay);\n  };\n};\nexports[\"default\"] = _default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAC3B;AACA;AACA;AACA;AACA,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACC,EAAE,EAAE;EACnC,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAClF,IAAIG,KAAK,GAAG,IAAI;EAChB,OAAO,YAAY;IACjB,IAAIC,KAAK,GAAG,IAAI;IAChB,KAAK,IAAIC,IAAI,GAAGL,SAAS,CAACC,MAAM,EAAEK,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGR,SAAS,CAACQ,IAAI,CAAC;IAC9B;IACAC,YAAY,CAACN,KAAK,CAAC;IACnBA,KAAK,GAAGO,UAAU,CAAC,YAAY;MAC7BZ,EAAE,CAACa,KAAK,CAACP,KAAK,EAAEE,IAAI,CAAC;IACvB,CAAC,EAAEP,KAAK,CAAC;EACX,CAAC;AACH,CAAC;AACDJ,OAAO,CAAC,SAAS,CAAC,GAAGE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}