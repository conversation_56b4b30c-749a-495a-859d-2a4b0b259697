{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getCurvenessForEdge } from '../helper/multipleGraphEdgeHelper.js';\nexport function simpleLayout(seriesModel) {\n  var coordSys = seriesModel.coordinateSystem;\n  if (coordSys && coordSys.type !== 'view') {\n    return;\n  }\n  var graph = seriesModel.getGraph();\n  graph.eachNode(function (node) {\n    var model = node.getModel();\n    node.setLayout([+model.get('x'), +model.get('y')]);\n  });\n  simpleLayoutEdge(graph, seriesModel);\n}\nexport function simpleLayoutEdge(graph, seriesModel) {\n  graph.eachEdge(function (edge, index) {\n    var curveness = zrUtil.retrieve3(edge.getModel().get(['lineStyle', 'curveness']), -getCurvenessForEdge(edge, seriesModel, index, true), 0);\n    var p1 = vec2.clone(edge.node1.getLayout());\n    var p2 = vec2.clone(edge.node2.getLayout());\n    var points = [p1, p2];\n    if (+curveness) {\n      points.push([(p1[0] + p2[0]) / 2 - (p1[1] - p2[1]) * curveness, (p1[1] + p2[1]) / 2 - (p2[0] - p1[0]) * curveness]);\n    }\n    edge.setLayout(points);\n  });\n}", "map": {"version": 3, "names": ["vec2", "zrUtil", "getCurvenessForEdge", "simpleLayout", "seriesModel", "coordSys", "coordinateSystem", "type", "graph", "getGraph", "eachNode", "node", "model", "getModel", "setLayout", "get", "simpleLayoutEdge", "eachEdge", "edge", "index", "curveness", "retrieve3", "p1", "clone", "node1", "getLayout", "p2", "node2", "points", "push"], "sources": ["/Users/<USER>/Documents/quant/AIQuant7/frontend/node_modules/echarts/lib/chart/graph/simpleLayoutHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as vec2 from 'zrender/lib/core/vector.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getCurvenessForEdge } from '../helper/multipleGraphEdgeHelper.js';\nexport function simpleLayout(seriesModel) {\n  var coordSys = seriesModel.coordinateSystem;\n  if (coordSys && coordSys.type !== 'view') {\n    return;\n  }\n  var graph = seriesModel.getGraph();\n  graph.eachNode(function (node) {\n    var model = node.getModel();\n    node.setLayout([+model.get('x'), +model.get('y')]);\n  });\n  simpleLayoutEdge(graph, seriesModel);\n}\nexport function simpleLayoutEdge(graph, seriesModel) {\n  graph.eachEdge(function (edge, index) {\n    var curveness = zrUtil.retrieve3(edge.getModel().get(['lineStyle', 'curveness']), -getCurvenessForEdge(edge, seriesModel, index, true), 0);\n    var p1 = vec2.clone(edge.node1.getLayout());\n    var p2 = vec2.clone(edge.node2.getLayout());\n    var points = [p1, p2];\n    if (+curveness) {\n      points.push([(p1[0] + p2[0]) / 2 - (p1[1] - p2[1]) * curveness, (p1[1] + p2[1]) / 2 - (p2[0] - p1[0]) * curveness]);\n    }\n    edge.setLayout(points);\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,IAAI,MAAM,4BAA4B;AAClD,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,OAAO,SAASC,YAAYA,CAACC,WAAW,EAAE;EACxC,IAAIC,QAAQ,GAAGD,WAAW,CAACE,gBAAgB;EAC3C,IAAID,QAAQ,IAAIA,QAAQ,CAACE,IAAI,KAAK,MAAM,EAAE;IACxC;EACF;EACA,IAAIC,KAAK,GAAGJ,WAAW,CAACK,QAAQ,CAAC,CAAC;EAClCD,KAAK,CAACE,QAAQ,CAAC,UAAUC,IAAI,EAAE;IAC7B,IAAIC,KAAK,GAAGD,IAAI,CAACE,QAAQ,CAAC,CAAC;IAC3BF,IAAI,CAACG,SAAS,CAAC,CAAC,CAACF,KAAK,CAACG,GAAG,CAAC,GAAG,CAAC,EAAE,CAACH,KAAK,CAACG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACpD,CAAC,CAAC;EACFC,gBAAgB,CAACR,KAAK,EAAEJ,WAAW,CAAC;AACtC;AACA,OAAO,SAASY,gBAAgBA,CAACR,KAAK,EAAEJ,WAAW,EAAE;EACnDI,KAAK,CAACS,QAAQ,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACpC,IAAIC,SAAS,GAAGnB,MAAM,CAACoB,SAAS,CAACH,IAAI,CAACL,QAAQ,CAAC,CAAC,CAACE,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,EAAE,CAACb,mBAAmB,CAACgB,IAAI,EAAEd,WAAW,EAAEe,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAC1I,IAAIG,EAAE,GAAGtB,IAAI,CAACuB,KAAK,CAACL,IAAI,CAACM,KAAK,CAACC,SAAS,CAAC,CAAC,CAAC;IAC3C,IAAIC,EAAE,GAAG1B,IAAI,CAACuB,KAAK,CAACL,IAAI,CAACS,KAAK,CAACF,SAAS,CAAC,CAAC,CAAC;IAC3C,IAAIG,MAAM,GAAG,CAACN,EAAE,EAAEI,EAAE,CAAC;IACrB,IAAI,CAACN,SAAS,EAAE;MACdQ,MAAM,CAACC,IAAI,CAAC,CAAC,CAACP,EAAE,CAAC,CAAC,CAAC,GAAGI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAACJ,EAAE,CAAC,CAAC,CAAC,GAAGI,EAAE,CAAC,CAAC,CAAC,IAAIN,SAAS,EAAE,CAACE,EAAE,CAAC,CAAC,CAAC,GAAGI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAACA,EAAE,CAAC,CAAC,CAAC,GAAGJ,EAAE,CAAC,CAAC,CAAC,IAAIF,SAAS,CAAC,CAAC;IACrH;IACAF,IAAI,CAACJ,SAAS,CAACc,MAAM,CAAC;EACxB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}