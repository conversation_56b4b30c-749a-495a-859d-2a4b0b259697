[{"/Users/<USER>/Documents/quant/AIQuant7/frontend/src/index.js": "1", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/App.js": "2", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/ThemeAnalysis.js": "3", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Dashboard.js": "4", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/TradingSignals.js": "5", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/MarketSentiment.js": "6", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Settings.js": "7", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Portfolio.js": "8", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/LeaderStocks.js": "9", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/services/api.js": "10"}, {"size": 517, "mtime": 1750170935396, "results": "11", "hashOfConfig": "12"}, {"size": 2619, "mtime": 1750174034585, "results": "13", "hashOfConfig": "12"}, {"size": 9590, "mtime": 1750171158659, "results": "14", "hashOfConfig": "12"}, {"size": 10171, "mtime": 1750173758506, "results": "15", "hashOfConfig": "12"}, {"size": 10705, "mtime": 1750171280928, "results": "16", "hashOfConfig": "12"}, {"size": 12387, "mtime": 1750174191521, "results": "17", "hashOfConfig": "12"}, {"size": 10334, "mtime": 1750171373912, "results": "18", "hashOfConfig": "12"}, {"size": 11819, "mtime": 1750171334397, "results": "19", "hashOfConfig": "12"}, {"size": 12515, "mtime": 1750171227825, "results": "20", "hashOfConfig": "12"}, {"size": 3101, "mtime": 1750173914101, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1yof5n8", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/index.js", [], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/App.js", [], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/ThemeAnalysis.js", ["52", "53", "54"], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Dashboard.js", [], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/TradingSignals.js", ["55", "56"], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/MarketSentiment.js", ["57", "58", "59", "60", "61"], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Settings.js", ["62", "63"], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Portfolio.js", ["64", "65", "66"], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/LeaderStocks.js", ["67"], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/services/api.js", [], [], {"ruleId": "68", "severity": 1, "message": "69", "line": 3, "column": 10, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 22}, {"ruleId": "68", "severity": 1, "message": "72", "line": 3, "column": 40, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 58}, {"ruleId": "73", "severity": 1, "message": "74", "line": 22, "column": 6, "nodeType": "75", "endLine": 22, "endColumn": 38, "suggestions": "76"}, {"ruleId": "68", "severity": 1, "message": "77", "line": 3, "column": 10, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 24}, {"ruleId": "73", "severity": 1, "message": "78", "line": 25, "column": 6, "nodeType": "75", "endLine": 25, "endColumn": 41, "suggestions": "79"}, {"ruleId": "68", "severity": 1, "message": "80", "line": 2, "column": 26, "nodeType": "70", "messageId": "71", "endLine": 2, "endColumn": 31}, {"ruleId": "68", "severity": 1, "message": "81", "line": 2, "column": 44, "nodeType": "70", "messageId": "71", "endLine": 2, "endColumn": 49}, {"ruleId": "68", "severity": 1, "message": "82", "line": 3, "column": 10, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 27}, {"ruleId": "68", "severity": 1, "message": "83", "line": 8, "column": 9, "nodeType": "70", "messageId": "71", "endLine": 8, "endColumn": 20}, {"ruleId": "73", "severity": 1, "message": "84", "line": 19, "column": 6, "nodeType": "75", "endLine": 19, "endColumn": 17, "suggestions": "85"}, {"ruleId": "68", "severity": 1, "message": "86", "line": 3, "column": 10, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 25}, {"ruleId": "68", "severity": 1, "message": "87", "line": 6, "column": 9, "nodeType": "70", "messageId": "71", "endLine": 6, "endColumn": 17}, {"ruleId": "68", "severity": 1, "message": "88", "line": 2, "column": 33, "nodeType": "70", "messageId": "71", "endLine": 2, "endColumn": 36}, {"ruleId": "68", "severity": 1, "message": "89", "line": 3, "column": 10, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 24}, {"ruleId": "68", "severity": 1, "message": "90", "line": 3, "column": 55, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 69}, {"ruleId": "73", "severity": 1, "message": "91", "line": 24, "column": 6, "nodeType": "75", "endLine": 24, "endColumn": 40, "suggestions": "92"}, "no-unused-vars", "'FireOutlined' is defined but never used.", "Identifier", "unusedVar", "'TrendingUpOutlined' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'filterThemes'. Either include it or remove the dependency array.", "ArrayExpression", ["93"], "'SignalOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'filterSignals'. Either include it or remove the dependency array.", ["94"], "'Table' is defined but never used.", "'Alert' is defined but never used.", "'LineChartOutlined' is defined but never used.", "'RangePicker' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSentimentData'. Either include it or remove the dependency array.", ["95"], "'SettingOutlined' is defined but never used.", "'TextArea' is assigned a value but never used.", "'Tag' is defined but never used.", "'WalletOutlined' is defined but never used.", "'DollarOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'filterLeaders'. Either include it or remove the dependency array.", ["96"], {"desc": "97", "fix": "98"}, {"desc": "99", "fix": "100"}, {"desc": "101", "fix": "102"}, {"desc": "103", "fix": "104"}, "Update the dependencies array to be: [themes, searchText, filterType, filterThemes]", {"range": "105", "text": "106"}, "Update the dependencies array to be: [signals, filterType, filterStatus, filterSignals]", {"range": "107", "text": "108"}, "Update the dependencies array to be: [loadSentimentData, timeRange]", {"range": "109", "text": "110"}, "Update the dependencies array to be: [leaders, searchText, filterTheme, filterLeaders]", {"range": "111", "text": "112"}, [704, 736], "[themes, searchText, filterType, filterThemes]", [844, 879], "[signals, filterType, filterStatus, filterSignals]", [720, 731], "[loadSentimentData, timeRange]", [845, 879], "[leaders, searchText, filterTheme, filterLeaders]"]