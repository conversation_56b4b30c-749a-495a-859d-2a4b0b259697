[{"/Users/<USER>/Documents/quant/AIQuant7/frontend/src/index.js": "1", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/App.js": "2", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/ThemeAnalysis.js": "3", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Dashboard.js": "4", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/TradingSignals.js": "5", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/MarketSentiment.js": "6", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Settings.js": "7", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Portfolio.js": "8", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/LeaderStocks.js": "9", "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/services/api.js": "10"}, {"size": 517, "mtime": 1750170935396, "results": "11", "hashOfConfig": "12"}, {"size": 2619, "mtime": 1750174034585, "results": "13", "hashOfConfig": "12"}, {"size": 10003, "mtime": 1750174453220, "results": "14", "hashOfConfig": "12"}, {"size": 10171, "mtime": 1750173758506, "results": "15", "hashOfConfig": "12"}, {"size": 10971, "mtime": 1750174696788, "results": "16", "hashOfConfig": "12"}, {"size": 12451, "mtime": 1750236613371, "results": "17", "hashOfConfig": "12"}, {"size": 10334, "mtime": 1750171373912, "results": "18", "hashOfConfig": "12"}, {"size": 12367, "mtime": 1750174870839, "results": "19", "hashOfConfig": "12"}, {"size": 12921, "mtime": 1750174589082, "results": "20", "hashOfConfig": "12"}, {"size": 3208, "mtime": 1750236597919, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1yof5n8", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/index.js", [], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/App.js", [], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/ThemeAnalysis.js", ["52", "53", "54", "55", "56"], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Dashboard.js", [], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/TradingSignals.js", ["57", "58"], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/MarketSentiment.js", ["59", "60", "61", "62", "63", "64", "65"], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Settings.js", ["66", "67"], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/Portfolio.js", ["68", "69", "70"], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/pages/LeaderStocks.js", ["71"], [], "/Users/<USER>/Documents/quant/AIQuant7/frontend/src/services/api.js", [], [], {"ruleId": "72", "severity": 1, "message": "73", "line": 3, "column": 10, "nodeType": "74", "messageId": "75", "endLine": 3, "endColumn": 22}, {"ruleId": "72", "severity": 1, "message": "76", "line": 3, "column": 40, "nodeType": "74", "messageId": "75", "endLine": 3, "endColumn": 58}, {"ruleId": "77", "severity": 1, "message": "78", "line": 22, "column": 6, "nodeType": "79", "endLine": 22, "endColumn": 38, "suggestions": "80"}, {"ruleId": "81", "severity": 1, "message": "82", "line": 94, "column": 26, "nodeType": "83", "messageId": "84", "endLine": 94, "endColumn": 28}, {"ruleId": "81", "severity": 1, "message": "82", "line": 94, "column": 95, "nodeType": "83", "messageId": "84", "endLine": 94, "endColumn": 97}, {"ruleId": "72", "severity": 1, "message": "85", "line": 3, "column": 10, "nodeType": "74", "messageId": "75", "endLine": 3, "endColumn": 24}, {"ruleId": "77", "severity": 1, "message": "86", "line": 25, "column": 6, "nodeType": "79", "endLine": 25, "endColumn": 41, "suggestions": "87"}, {"ruleId": "72", "severity": 1, "message": "88", "line": 2, "column": 26, "nodeType": "74", "messageId": "75", "endLine": 2, "endColumn": 31}, {"ruleId": "72", "severity": 1, "message": "89", "line": 2, "column": 44, "nodeType": "74", "messageId": "75", "endLine": 2, "endColumn": 49}, {"ruleId": "72", "severity": 1, "message": "90", "line": 3, "column": 10, "nodeType": "74", "messageId": "75", "endLine": 3, "endColumn": 27}, {"ruleId": "72", "severity": 1, "message": "91", "line": 8, "column": 9, "nodeType": "74", "messageId": "75", "endLine": 8, "endColumn": 20}, {"ruleId": "72", "severity": 1, "message": "92", "line": 15, "column": 10, "nodeType": "74", "messageId": "75", "endLine": 15, "endColumn": 25}, {"ruleId": "72", "severity": 1, "message": "93", "line": 15, "column": 27, "nodeType": "74", "messageId": "75", "endLine": 15, "endColumn": 45}, {"ruleId": "77", "severity": 1, "message": "94", "line": 20, "column": 6, "nodeType": "79", "endLine": 20, "endColumn": 17, "suggestions": "95"}, {"ruleId": "72", "severity": 1, "message": "96", "line": 3, "column": 10, "nodeType": "74", "messageId": "75", "endLine": 3, "endColumn": 25}, {"ruleId": "72", "severity": 1, "message": "97", "line": 6, "column": 9, "nodeType": "74", "messageId": "75", "endLine": 6, "endColumn": 17}, {"ruleId": "72", "severity": 1, "message": "98", "line": 2, "column": 33, "nodeType": "74", "messageId": "75", "endLine": 2, "endColumn": 36}, {"ruleId": "72", "severity": 1, "message": "99", "line": 3, "column": 10, "nodeType": "74", "messageId": "75", "endLine": 3, "endColumn": 24}, {"ruleId": "72", "severity": 1, "message": "100", "line": 3, "column": 55, "nodeType": "74", "messageId": "75", "endLine": 3, "endColumn": 69}, {"ruleId": "77", "severity": 1, "message": "101", "line": 24, "column": 6, "nodeType": "79", "endLine": 24, "endColumn": 40, "suggestions": "102"}, "no-unused-vars", "'FireOutlined' is defined but never used.", "Identifier", "unusedVar", "'TrendingUpOutlined' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'filterThemes'. Either include it or remove the dependency array.", "ArrayExpression", ["103"], "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'SignalOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'filterSignals'. Either include it or remove the dependency array.", ["104"], "'Table' is defined but never used.", "'Alert' is defined but never used.", "'LineChartOutlined' is defined but never used.", "'RangePicker' is assigned a value but never used.", "'moneyEffectData' is assigned a value but never used.", "'setMoneyEffectData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadSentimentData'. Either include it or remove the dependency array.", ["105"], "'SettingOutlined' is defined but never used.", "'TextArea' is assigned a value but never used.", "'Tag' is defined but never used.", "'WalletOutlined' is defined but never used.", "'DollarOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'filterLeaders'. Either include it or remove the dependency array.", ["106"], {"desc": "107", "fix": "108"}, {"desc": "109", "fix": "110"}, {"desc": "111", "fix": "112"}, {"desc": "113", "fix": "114"}, "Update the dependencies array to be: [themes, searchText, filterType, filterThemes]", {"range": "115", "text": "116"}, "Update the dependencies array to be: [signals, filterType, filterStatus, filterSignals]", {"range": "117", "text": "118"}, "Update the dependencies array to be: [loadSentimentData, timeRange]", {"range": "119", "text": "120"}, "Update the dependencies array to be: [leaders, searchText, filterTheme, filterLeaders]", {"range": "121", "text": "122"}, [704, 736], "[themes, searchText, filterType, filterThemes]", [844, 879], "[signals, filterType, filterStatus, filterSignals]", [784, 795], "[loadSentimentData, timeRange]", [845, 879], "[leaders, searchText, filterTheme, filterLeaders]"]