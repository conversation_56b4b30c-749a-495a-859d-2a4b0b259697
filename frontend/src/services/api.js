import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'development'
    ? 'http://localhost:8080/api/v1'
    : '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API请求错误:', error);
    return Promise.reject(error);
  }
);

// API服务类
class ApiService {
  // 系统健康检查
  async getHealth() {
    const healthApi = axios.create({
      baseURL: process.env.NODE_ENV === 'development'
        ? 'http://localhost:8080'
        : '',
      timeout: 5000,
    });
    return healthApi.get('/health');
  }

  // 市场情绪相关API
  async getCurrentSentiment() {
    return api.get('/sentiment/current');
  }

  async getSentimentHistory(days = 30) {
    return api.get(`/sentiment/history?days=${days}`);
  }

  async getSentimentAnalysis() {
    return api.get('/sentiment/analysis');
  }

  // 题材分析相关API
  async getThemeRanking(limit = 20) {
    return api.get(`/themes/ranking?limit=${limit}`);
  }

  async getThemeDetail(themeId) {
    return api.get(`/themes/${themeId}`);
  }

  async getThemeLifecycle(themeId) {
    return api.get(`/themes/${themeId}/lifecycle`);
  }

  // 龙头股相关API
  async getLeaderRanking(limit = 50) {
    return api.get(`/leaders/ranking?limit=${limit}`);
  }

  async getLeaderAnalysis(symbol) {
    return api.get(`/leaders/${symbol}/analysis`);
  }

  async getLeaderComparison(symbols) {
    return api.post('/leaders/comparison', { symbols });
  }

  async getThemeLeadersSummary() {
    return api.get('/leaders/theme-summary');
  }

  // 交易信号相关API
  async getLatestSignals(limit = 100) {
    return api.get(`/signals/latest?limit=${limit}`);
  }

  async getStockSignal(symbol) {
    return api.get(`/signals/${symbol}`);
  }

  async getSignalSummary() {
    return api.get('/signals/summary');
  }

  // 持仓管理相关API
  async getCurrentPositions() {
    return api.get('/positions/current');
  }

  async getPortfolioMetrics() {
    return api.get('/positions/metrics');
  }

  async openPosition(data) {
    return api.post('/positions/open', data);
  }

  async closePosition(positionId) {
    return api.post(`/positions/${positionId}/close`);
  }

  async getRiskControl() {
    return api.get('/positions/risk-control');
  }

  // 实时数据相关API
  async getRealtimeMarketData() {
    return api.get('/market/realtime');
  }

  async getStockRealtimeData(symbol) {
    return api.get(`/market/stock/${symbol}/realtime`);
  }

  async getNewsData(limit = 20) {
    return api.get(`/market/news?limit=${limit}`);
  }
}

// 创建API服务实例
const apiService = new ApiService();

// 导出API服务和axios实例
export { apiService, api };
export default apiService;
