import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Table, Tag, Spin, Alert, Select, DatePicker } from 'antd';
import { LineChartOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import moment from 'moment';
import apiService from '../services/api';

const { RangePicker } = DatePicker;
const { Option } = Select;

const MarketSentiment = () => {
  const [loading, setLoading] = useState(true);
  const [currentSentiment, setCurrentSentiment] = useState(null);
  const [sentimentHistory, setSentimentHistory] = useState([]);
  const [timeRange, setTimeRange] = useState(30);

  useEffect(() => {
    loadSentimentData();
  }, [timeRange]);

  const loadSentimentData = async () => {
    try {
      setLoading(true);
      
      const [current, history] = await Promise.allSettled([
        apiService.getCurrentSentiment(),
        apiService.getSentimentHistory(timeRange)
      ]);

      if (current.status === 'fulfilled') {
        console.log('Current sentiment data:', current.value);
        setCurrentSentiment(current.value?.data || current.value);
      } else {
        // 模拟数据
        setCurrentSentiment({
          sentiment_score: 75.5,
          market_temperature: 68.2,
          trend_direction: 'bullish',
          volatility_index: 0.234,
          sentiment_phase: 'high_tide',
          phase_description: '市场情绪高涨，投资者积极性较高',
          risk_level: 'medium',
          entry_timing: 'good'
        });
      }

      if (history.status === 'fulfilled') {
        console.log('Sentiment history data:', history.value);
        const historyData = history.value?.data || history.value;
        setSentimentHistory(Array.isArray(historyData) ? historyData : []);
      } else {
        // 生成模拟历史数据
        const mockHistory = [];
        for (let i = timeRange; i >= 0; i--) {
          const date = moment().subtract(i, 'days');
          mockHistory.push({
            date: date.format('YYYY-MM-DD'),
            sentiment_score: 50 + Math.random() * 40,
            market_temperature: 40 + Math.random() * 40,
            limit_up_count: Math.floor(Math.random() * 100),
            limit_down_count: Math.floor(Math.random() * 50),
            volatility_index: Math.random() * 0.5
          });
        }
        setSentimentHistory(mockHistory);
      }
    } catch (error) {
      console.error('加载情绪数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 情绪历史趋势图配置
  const getSentimentTrendOption = () => {
    if (!Array.isArray(sentimentHistory) || sentimentHistory.length === 0) {
      return {
        title: { text: '市场情绪趋势', left: 'center' },
        xAxis: { type: 'category', data: [] },
        yAxis: { type: 'value' },
        series: []
      };
    }

    const dates = sentimentHistory.map(item => item.date);
    const sentimentScores = sentimentHistory.map(item => item.sentiment_score);
    const temperatures = sentimentHistory.map(item => item.market_temperature);

    return {
      title: {
        text: '市场情绪趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['情绪指数', '市场温度'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLabel: {
          formatter: (value) => moment(value).format('MM-DD')
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '情绪指数',
          min: 0,
          max: 100,
          position: 'left'
        },
        {
          type: 'value',
          name: '市场温度',
          min: 0,
          max: 100,
          position: 'right'
        }
      ],
      series: [
        {
          name: '情绪指数',
          type: 'line',
          yAxisIndex: 0,
          data: sentimentScores,
          smooth: true,
          lineStyle: {
            color: '#1890ff'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ]
            }
          }
        },
        {
          name: '市场温度',
          type: 'line',
          yAxisIndex: 1,
          data: temperatures,
          smooth: true,
          lineStyle: {
            color: '#ff4d4f'
          }
        }
      ]
    };
  };

  // 涨跌停分布图配置
  const getLimitDistributionOption = () => {
    if (!Array.isArray(sentimentHistory) || sentimentHistory.length === 0) {
      return {
        title: { text: '涨跌停分布', left: 'center' },
        xAxis: { type: 'category', data: [] },
        yAxis: { type: 'value' },
        series: []
      };
    }

    const dates = sentimentHistory.map(item => item.date);
    const limitUp = sentimentHistory.map(item => item.limit_up_count);
    const limitDown = sentimentHistory.map(item => -item.limit_down_count); // 负数显示

    return {
      title: {
        text: '涨跌停分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params) => {
          let result = params[0].axisValue + '<br/>';
          params.forEach(param => {
            const value = Math.abs(param.value);
            const name = param.seriesName;
            result += `${param.marker}${name}: ${value}只<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: ['涨停', '跌停'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLabel: {
          formatter: (value) => moment(value).format('MM-DD')
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: (value) => Math.abs(value)
        }
      },
      series: [
        {
          name: '涨停',
          type: 'bar',
          data: limitUp,
          itemStyle: {
            color: '#ff4d4f'
          }
        },
        {
          name: '跌停',
          type: 'bar',
          data: limitDown,
          itemStyle: {
            color: '#52c41a'
          }
        }
      ]
    };
  };

  const getSentimentPhaseColor = (phase) => {
    switch (phase) {
      case 'freezing': return 'blue';
      case 'warming': return 'orange';
      case 'high_tide': return 'red';
      case 'retreat': return 'purple';
      case 'chaos': return 'gray';
      default: return 'default';
    }
  };

  const getSentimentPhaseName = (phase) => {
    switch (phase) {
      case 'freezing': return '冰点期';
      case 'warming': return '回暖期';
      case 'high_tide': return '高潮期';
      case 'retreat': return '退潮期';
      case 'chaos': return '混沌期';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>正在加载市场情绪数据...</div>
      </div>
    );
  }

  return (
    <div>
      {/* 当前情绪状态 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '48px', fontWeight: 'bold', color: '#1890ff' }}>
                {currentSentiment?.sentiment_score?.toFixed(1) || 0}
              </div>
              <div style={{ color: '#666', marginTop: 8 }}>情绪指数</div>
              <Tag 
                color={getSentimentPhaseColor(currentSentiment?.sentiment_phase)}
                style={{ marginTop: 8 }}
              >
                {getSentimentPhaseName(currentSentiment?.sentiment_phase)}
              </Tag>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '48px', fontWeight: 'bold', color: '#ff4d4f' }}>
                {currentSentiment?.market_temperature?.toFixed(1) || 0}°C
              </div>
              <div style={{ color: '#666', marginTop: 8 }}>市场温度</div>
              <div style={{ marginTop: 8, color: '#666' }}>
                {currentSentiment?.trend_direction === 'bullish' ?
                  <><ArrowUpOutlined style={{ color: '#ff4d4f' }} /> 看涨</> :
                  <><ArrowDownOutlined style={{ color: '#52c41a' }} /> 看跌</>
                }
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '48px', fontWeight: 'bold', color: '#faad14' }}>
                {(currentSentiment?.volatility_index * 100)?.toFixed(1) || 0}%
              </div>
              <div style={{ color: '#666', marginTop: 8 }}>波动率指数</div>
              <div style={{ marginTop: 8 }}>
                <Tag color={currentSentiment?.entry_timing === 'good' ? 'green' : 'orange'}>
                  {currentSentiment?.entry_timing === 'good' ? '适宜入场' : '谨慎入场'}
                </Tag>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 时间范围选择 */}
      <Row style={{ marginBottom: 16 }}>
        <Col>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 120 }}
          >
            <Option value={7}>近7天</Option>
            <Option value={15}>近15天</Option>
            <Option value={30}>近30天</Option>
            <Option value={60}>近60天</Option>
          </Select>
        </Col>
      </Row>

      {/* 趋势图表 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="情绪趋势分析" className="dashboard-card">
            <ReactECharts 
              option={getSentimentTrendOption()} 
              style={{ height: '400px' }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="涨跌停分布" className="dashboard-card">
            <ReactECharts 
              option={getLimitDistributionOption()} 
              style={{ height: '400px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 情绪分析说明 */}
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="情绪分析说明" className="dashboard-card">
            <div style={{ lineHeight: '1.8' }}>
              <p><strong>当前阶段：</strong>{currentSentiment?.phase_description}</p>
              <p><strong>风险等级：</strong>
                <Tag color={
                  currentSentiment?.risk_level === 'high' ? 'red' :
                  currentSentiment?.risk_level === 'medium' ? 'orange' : 'green'
                }>
                  {currentSentiment?.risk_level === 'high' ? '高风险' :
                   currentSentiment?.risk_level === 'medium' ? '中等风险' : '低风险'}
                </Tag>
              </p>
              <p><strong>操作建议：</strong>
                {currentSentiment?.sentiment_phase === 'high_tide' && '市场情绪高涨，注意风险控制，适当减仓'}
                {currentSentiment?.sentiment_phase === 'warming' && '市场回暖，可适当增加仓位'}
                {currentSentiment?.sentiment_phase === 'freezing' && '市场冰点，寻找优质标的建仓机会'}
                {currentSentiment?.sentiment_phase === 'retreat' && '市场退潮，保持谨慎，控制仓位'}
                {currentSentiment?.sentiment_phase === 'chaos' && '市场混沌，观望为主，等待明确信号'}
              </p>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default MarketSentiment;
