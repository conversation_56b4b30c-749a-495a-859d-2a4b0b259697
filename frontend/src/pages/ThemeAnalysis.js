import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Table, Tag, Progress, Spin, Input, Select } from 'antd';
import { FireOutlined, SearchOutlined, TrendingUpOutlined } from '@ant-design/icons';
import apiService from '../services/api';

const { Search } = Input;
const { Option } = Select;

const ThemeAnalysis = () => {
  const [loading, setLoading] = useState(true);
  const [themes, setThemes] = useState([]);
  const [filteredThemes, setFilteredThemes] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState('all');

  useEffect(() => {
    loadThemeData();
  }, []);

  useEffect(() => {
    filterThemes();
  }, [themes, searchText, filterType]);

  const loadThemeData = async () => {
    try {
      setLoading(true);
      const data = await apiService.getThemeRanking(50);
      setThemes(data);
    } catch (error) {
      console.error('加载题材数据失败:', error);
      // 使用模拟数据
      const mockThemes = [
        {
          theme_id: '1',
          theme_name: '新能源汽车',
          theme_type: 'industry',
          theme_score: 95.5,
          stock_count: 156,
          leader_stocks: ['300750', '002594'],
          avg_change_pct: 8.5,
          hot_level: 'very_hot',
          lifecycle_stage: 'growth',
          keywords: ['新能源', '汽车', '锂电池', '充电桩']
        },
        {
          theme_id: '2',
          theme_name: '人工智能',
          theme_type: 'concept',
          theme_score: 88.2,
          stock_count: 89,
          leader_stocks: ['000725', '002415'],
          avg_change_pct: 6.2,
          hot_level: 'hot',
          lifecycle_stage: 'mature',
          keywords: ['AI', '人工智能', '机器学习', '算法']
        },
        {
          theme_id: '3',
          theme_name: '碳中和',
          theme_type: 'policy',
          theme_score: 82.1,
          stock_count: 234,
          leader_stocks: ['600036', '000002'],
          avg_change_pct: 4.8,
          hot_level: 'warm',
          lifecycle_stage: 'growth',
          keywords: ['碳中和', '碳达峰', '环保', '绿色能源']
        }
      ];
      setThemes(mockThemes);
    } finally {
      setLoading(false);
    }
  };

  const filterThemes = () => {
    let filtered = themes;

    // 按类型筛选
    if (filterType !== 'all') {
      filtered = filtered.filter(theme => theme.theme_type === filterType);
    }

    // 按搜索文本筛选
    if (searchText) {
      filtered = filtered.filter(theme => 
        theme.theme_name.toLowerCase().includes(searchText.toLowerCase()) ||
        theme.keywords.some(keyword => 
          keyword.toLowerCase().includes(searchText.toLowerCase())
        )
      );
    }

    setFilteredThemes(filtered);
  };

  const getThemeTypeColor = (type) => {
    switch (type) {
      case 'policy': return 'blue';
      case 'industry': return 'green';
      case 'event': return 'orange';
      case 'concept': return 'purple';
      default: return 'default';
    }
  };

  const getThemeTypeName = (type) => {
    switch (type) {
      case 'policy': return '政策驱动';
      case 'industry': return '行业拐点';
      case 'event': return '事件催化';
      case 'concept': return '主题炒作';
      default: return '未知';
    }
  };

  const getHotLevelColor = (level) => {
    switch (level) {
      case 'very_hot': return '#ff4d4f';
      case 'hot': return '#fa8c16';
      case 'warm': return '#faad14';
      case 'cold': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  const getHotLevelName = (level) => {
    switch (level) {
      case 'very_hot': return '极热';
      case 'hot': return '热门';
      case 'warm': return '温热';
      case 'cold': return '冷门';
      default: return '未知';
    }
  };

  const columns = [
    {
      title: '排名',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_, __, index) => index + 1
    },
    {
      title: '题材名称',
      dataIndex: 'theme_name',
      key: 'theme_name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.keywords.slice(0, 3).join(', ')}
          </div>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'theme_type',
      key: 'theme_type',
      width: 100,
      render: (type) => (
        <Tag color={getThemeTypeColor(type)}>
          {getThemeTypeName(type)}
        </Tag>
      )
    },
    {
      title: '题材评分',
      dataIndex: 'theme_score',
      key: 'theme_score',
      width: 120,
      render: (score) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{score.toFixed(1)}</div>
          <Progress 
            percent={score} 
            size="small" 
            showInfo={false}
            strokeColor={score > 80 ? '#ff4d4f' : score > 60 ? '#faad14' : '#52c41a'}
          />
        </div>
      ),
      sorter: (a, b) => a.theme_score - b.theme_score
    },
    {
      title: '热度',
      dataIndex: 'hot_level',
      key: 'hot_level',
      width: 80,
      render: (level) => (
        <Tag color={getHotLevelColor(level)}>
          {getHotLevelName(level)}
        </Tag>
      )
    },
    {
      title: '个股数量',
      dataIndex: 'stock_count',
      key: 'stock_count',
      width: 80,
      render: (count) => `${count}只`,
      sorter: (a, b) => a.stock_count - b.stock_count
    },
    {
      title: '平均涨幅',
      dataIndex: 'avg_change_pct',
      key: 'avg_change_pct',
      width: 100,
      render: (pct) => (
        <span className={pct >= 0 ? 'price-up' : 'price-down'}>
          {pct >= 0 ? '+' : ''}{pct.toFixed(2)}%
        </span>
      ),
      sorter: (a, b) => a.avg_change_pct - b.avg_change_pct
    },
    {
      title: '生命周期',
      dataIndex: 'lifecycle_stage',
      key: 'lifecycle_stage',
      width: 100,
      render: (stage) => {
        const stageMap = {
          'emerging': { text: '萌芽期', color: 'blue' },
          'growth': { text: '成长期', color: 'green' },
          'mature': { text: '成熟期', color: 'orange' },
          'decline': { text: '衰退期', color: 'red' }
        };
        const stageInfo = stageMap[stage] || { text: '未知', color: 'default' };
        return <Tag color={stageInfo.color}>{stageInfo.text}</Tag>;
      }
    }
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>正在加载题材数据...</div>
      </div>
    );
  }

  return (
    <div>
      {/* 筛选控件 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={12} md={8}>
          <Search
            placeholder="搜索题材名称或关键词"
            allowClear
            onSearch={setSearchText}
            onChange={(e) => setSearchText(e.target.value)}
            prefix={<SearchOutlined />}
          />
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Select
            value={filterType}
            onChange={setFilterType}
            style={{ width: '100%' }}
            placeholder="选择题材类型"
          >
            <Option value="all">全部类型</Option>
            <Option value="policy">政策驱动</Option>
            <Option value="industry">行业拐点</Option>
            <Option value="event">事件催化</Option>
            <Option value="concept">主题炒作</Option>
          </Select>
        </Col>
      </Row>

      {/* 题材统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#1890ff' }}>
              {themes.length}
            </div>
            <div className="metric-label">总题材数</div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#ff4d4f' }}>
              {themes.filter(t => t.hot_level === 'very_hot' || t.hot_level === 'hot').length}
            </div>
            <div className="metric-label">热门题材</div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#52c41a' }}>
              {themes.filter(t => t.avg_change_pct > 5).length}
            </div>
            <div className="metric-label">强势题材</div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#faad14' }}>
              {themes.filter(t => t.lifecycle_stage === 'growth').length}
            </div>
            <div className="metric-label">成长期题材</div>
          </Card>
        </Col>
      </Row>

      {/* 题材排行榜 */}
      <Card title="题材排行榜" className="dashboard-card">
        <Table
          columns={columns}
          dataSource={filteredThemes}
          rowKey="theme_id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个题材`
          }}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  );
};

export default ThemeAnalysis;
