import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Table, Tag, Button, Spin, Select, Alert } from 'antd';
import { SignalOutlined, BellOutlined, CheckCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import apiService from '../services/api';

const { Option } = Select;

const TradingSignals = () => {
  const [loading, setLoading] = useState(true);
  const [signals, setSignals] = useState([]);
  const [filteredSignals, setFilteredSignals] = useState([]);
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  useEffect(() => {
    loadSignalData();
    // 设置定时刷新
    const interval = setInterval(loadSignalData, 60000); // 1分钟刷新一次
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    filterSignals();
  }, [signals, filterType, filterStatus]);

  const loadSignalData = async () => {
    try {
      setLoading(true);
      const response = await apiService.getLatestSignals(200);
      console.log('Signal data received:', response);
      const signalData = response?.data || response;
      setSignals(Array.isArray(signalData) ? signalData : []);
    } catch (error) {
      console.error('加载交易信号失败:', error);
      // 使用模拟数据
      const mockSignals = [
        {
          signal_id: '1',
          symbol: '300750',
          name: '宁德时代',
          signal_type: 'buy',
          signal_strength: 'strong',
          price: 245.80,
          target_price: 280.00,
          stop_loss: 220.00,
          confidence: 85.5,
          reason: '突破关键阻力位，成交量放大',
          created_time: moment().subtract(5, 'minutes').toISOString(),
          status: 'active',
          theme: '新能源汽车'
        },
        {
          signal_id: '2',
          symbol: '000725',
          name: '京东方A',
          signal_type: 'sell',
          signal_strength: 'medium',
          price: 4.25,
          target_price: 3.80,
          stop_loss: 4.50,
          confidence: 72.3,
          reason: '技术指标背离，获利了结',
          created_time: moment().subtract(15, 'minutes').toISOString(),
          status: 'active',
          theme: '人工智能'
        },
        {
          signal_id: '3',
          symbol: '002594',
          name: '比亚迪',
          signal_type: 'buy',
          signal_strength: 'weak',
          price: 280.50,
          target_price: 300.00,
          stop_loss: 265.00,
          confidence: 68.8,
          reason: '题材热度回升，资金流入',
          created_time: moment().subtract(30, 'minutes').toISOString(),
          status: 'executed',
          theme: '新能源汽车'
        }
      ];
      setSignals(mockSignals);
    } finally {
      setLoading(false);
    }
  };

  const filterSignals = () => {
    if (!Array.isArray(signals)) {
      setFilteredSignals([]);
      return;
    }

    let filtered = signals;

    // 按信号类型筛选
    if (filterType !== 'all') {
      filtered = filtered.filter(signal => signal.signal_type === filterType);
    }

    // 按状态筛选
    if (filterStatus !== 'all') {
      filtered = filtered.filter(signal => signal.status === filterStatus);
    }

    setFilteredSignals(filtered);
  };

  const getSignalTypeColor = (type) => {
    switch (type) {
      case 'buy': return 'red';
      case 'sell': return 'green';
      case 'hold': return 'blue';
      default: return 'default';
    }
  };

  const getSignalTypeName = (type) => {
    switch (type) {
      case 'buy': return '买入';
      case 'sell': return '卖出';
      case 'hold': return '持有';
      default: return '未知';
    }
  };

  const getStrengthColor = (strength) => {
    switch (strength) {
      case 'strong': return '#ff4d4f';
      case 'medium': return '#faad14';
      case 'weak': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  const getStrengthName = (strength) => {
    switch (strength) {
      case 'strong': return '强';
      case 'medium': return '中';
      case 'weak': return '弱';
      default: return '未知';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'processing';
      case 'executed': return 'success';
      case 'expired': return 'default';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getStatusName = (status) => {
    switch (status) {
      case 'active': return '活跃';
      case 'executed': return '已执行';
      case 'expired': return '已过期';
      case 'cancelled': return '已取消';
      default: return '未知';
    }
  };

  const columns = [
    {
      title: '时间',
      dataIndex: 'created_time',
      key: 'created_time',
      width: 120,
      render: (time) => moment(time).format('HH:mm:ss'),
      sorter: (a, b) => moment(a.created_time).unix() - moment(b.created_time).unix()
    },
    {
      title: '股票信息',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.symbol}</div>
        </div>
      )
    },
    {
      title: '信号类型',
      dataIndex: 'signal_type',
      key: 'signal_type',
      width: 80,
      render: (type) => (
        <Tag color={getSignalTypeColor(type)}>
          {getSignalTypeName(type)}
        </Tag>
      )
    },
    {
      title: '强度',
      dataIndex: 'signal_strength',
      key: 'signal_strength',
      width: 60,
      render: (strength) => (
        <Tag color={getStrengthColor(strength)}>
          {getStrengthName(strength)}
        </Tag>
      )
    },
    {
      title: '当前价',
      dataIndex: 'price',
      key: 'price',
      width: 80,
      render: (price) => `¥${price.toFixed(2)}`
    },
    {
      title: '目标价',
      dataIndex: 'target_price',
      key: 'target_price',
      width: 80,
      render: (price) => `¥${price.toFixed(2)}`
    },
    {
      title: '止损价',
      dataIndex: 'stop_loss',
      key: 'stop_loss',
      width: 80,
      render: (price) => `¥${price.toFixed(2)}`
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      width: 80,
      render: (confidence) => `${confidence.toFixed(1)}%`,
      sorter: (a, b) => a.confidence - b.confidence
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusName(status)}
        </Tag>
      )
    },
    {
      title: '信号原因',
      dataIndex: 'reason',
      key: 'reason',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <div>
          {record.status === 'active' && (
            <Button type="link" size="small" icon={<CheckCircleOutlined />}>
              执行
            </Button>
          )}
        </div>
      )
    }
  ];

  // 统计数据
  const activeSignals = Array.isArray(signals) ? signals.filter(s => s.status === 'active') : [];
  const buySignals = activeSignals.filter(s => s.signal_type === 'buy');
  const sellSignals = activeSignals.filter(s => s.signal_type === 'sell');
  const strongSignals = activeSignals.filter(s => s.signal_strength === 'strong');

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>正在加载交易信号...</div>
      </div>
    );
  }

  return (
    <div>
      {/* 信号统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#1890ff' }}>
              {activeSignals.length}
            </div>
            <div className="metric-label">活跃信号</div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#ff4d4f' }}>
              {buySignals.length}
            </div>
            <div className="metric-label">买入信号</div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#52c41a' }}>
              {sellSignals.length}
            </div>
            <div className="metric-label">卖出信号</div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#faad14' }}>
              {strongSignals.length}
            </div>
            <div className="metric-label">强信号</div>
          </Card>
        </Col>
      </Row>

      {/* 重要提醒 */}
      {strongSignals.length > 0 && (
        <Alert
          message={`发现 ${strongSignals.length} 个强信号`}
          description="建议重点关注强信号，及时执行交易决策"
          type="warning"
          showIcon
          icon={<BellOutlined />}
          style={{ marginBottom: 24 }}
        />
      )}

      {/* 筛选控件 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={8}>
          <Select
            value={filterType}
            onChange={setFilterType}
            style={{ width: '100%' }}
            placeholder="选择信号类型"
          >
            <Option value="all">全部类型</Option>
            <Option value="buy">买入信号</Option>
            <Option value="sell">卖出信号</Option>
            <Option value="hold">持有信号</Option>
          </Select>
        </Col>
        <Col xs={24} sm={8}>
          <Select
            value={filterStatus}
            onChange={setFilterStatus}
            style={{ width: '100%' }}
            placeholder="选择状态"
          >
            <Option value="all">全部状态</Option>
            <Option value="active">活跃</Option>
            <Option value="executed">已执行</Option>
            <Option value="expired">已过期</Option>
            <Option value="cancelled">已取消</Option>
          </Select>
        </Col>
      </Row>

      {/* 交易信号表格 */}
      <Card title="交易信号" className="dashboard-card">
        <Table
          columns={columns}
          dataSource={filteredSignals}
          rowKey="signal_id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个信号`
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  );
};

export default TradingSignals;
