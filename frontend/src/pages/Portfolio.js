import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Table, Tag, Button, Spin, Progress, Modal, Form, Input, InputNumber } from 'antd';
import { WalletOutlined, PlusOutlined, MinusOutlined, DollarOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import apiService from '../services/api';

const Portfolio = () => {
  const [loading, setLoading] = useState(true);
  const [positions, setPositions] = useState([]);
  const [portfolioMetrics, setPortfolioMetrics] = useState(null);
  const [addPositionVisible, setAddPositionVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    loadPortfolioData();
  }, []);

  const loadPortfolioData = async () => {
    try {
      setLoading(true);
      
      const [positionsData, metricsData] = await Promise.allSettled([
        apiService.getCurrentPositions(),
        apiService.getPortfolioMetrics()
      ]);

      if (positionsData.status === 'fulfilled') {
        console.log('Positions data received:', positionsData.value);
        const positions = positionsData.value?.data || positionsData.value;
        setPositions(Array.isArray(positions) ? positions : []);
      } else {
        // 模拟持仓数据
        const mockPositions = [
          {
            position_id: '1',
            symbol: '300750',
            name: '宁德时代',
            quantity: 1000,
            avg_cost: 220.50,
            current_price: 245.80,
            market_value: 245800,
            pnl: 25300,
            pnl_pct: 11.47,
            weight: 24.58,
            theme: '新能源汽车',
            open_date: '2024-12-01',
            days_held: 16
          },
          {
            position_id: '2',
            symbol: '002594',
            name: '比亚迪',
            quantity: 500,
            avg_cost: 260.00,
            current_price: 280.50,
            market_value: 140250,
            pnl: 10250,
            pnl_pct: 7.88,
            weight: 14.03,
            theme: '新能源汽车',
            open_date: '2024-12-05',
            days_held: 12
          }
        ];
        setPositions(mockPositions);
      }

      if (metricsData.status === 'fulfilled') {
        console.log('Portfolio metrics received:', metricsData.value);
        const metrics = metricsData.value?.data || metricsData.value;
        setPortfolioMetrics(metrics);
      } else {
        // 模拟组合指标
        setPortfolioMetrics({
          total_value: 1000000,
          total_cost: 950000,
          total_pnl: 50000,
          total_pnl_pct: 5.26,
          cash_balance: 614000,
          position_count: 2,
          max_drawdown: -8.5,
          sharpe_ratio: 1.25,
          win_rate: 68.5
        });
      }
    } catch (error) {
      console.error('加载投资组合数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddPosition = async (values) => {
    try {
      await apiService.openPosition(values);
      setAddPositionVisible(false);
      form.resetFields();
      loadPortfolioData();
    } catch (error) {
      console.error('开仓失败:', error);
    }
  };

  const handleClosePosition = async (positionId) => {
    try {
      await apiService.closePosition(positionId);
      loadPortfolioData();
    } catch (error) {
      console.error('平仓失败:', error);
    }
  };

  // 持仓分布饼图
  const getPositionDistributionOption = () => {
    if (!Array.isArray(positions) || positions.length === 0) {
      return {
        title: { text: '持仓分布', left: 'center' },
        series: []
      };
    }

    const data = positions.map(pos => ({
      name: pos.name,
      value: pos.weight
    }));

    return {
      title: {
        text: '持仓分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}% ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '持仓权重',
          type: 'pie',
          radius: '50%',
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
  };

  const columns = [
    {
      title: '股票信息',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.symbol}</div>
        </div>
      )
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity) => quantity.toLocaleString()
    },
    {
      title: '成本价',
      dataIndex: 'avg_cost',
      key: 'avg_cost',
      render: (cost) => `¥${(cost || 0).toFixed(2)}`
    },
    {
      title: '现价',
      dataIndex: 'current_price',
      key: 'current_price',
      render: (price) => `¥${(price || 0).toFixed(2)}`
    },
    {
      title: '市值',
      dataIndex: 'market_value',
      key: 'market_value',
      render: (value) => `¥${value.toLocaleString()}`
    },
    {
      title: '盈亏',
      dataIndex: 'pnl',
      key: 'pnl',
      render: (pnl, record) => (
        <div>
          <div className={(pnl || 0) >= 0 ? 'price-up' : 'price-down'}>
            ¥{(pnl || 0).toLocaleString()}
          </div>
          <div className={(record.pnl_pct || 0) >= 0 ? 'price-up' : 'price-down'}>
            {(record.pnl_pct || 0) >= 0 ? '+' : ''}{(record.pnl_pct || 0).toFixed(2)}%
          </div>
        </div>
      ),
      sorter: (a, b) => (a.pnl || 0) - (b.pnl || 0)
    },
    {
      title: '权重',
      dataIndex: 'weight',
      key: 'weight',
      render: (weight) => (
        <div>
          <div>{(weight || 0).toFixed(2)}%</div>
          <Progress
            percent={weight || 0}
            size="small"
            showInfo={false}
            strokeColor={(weight || 0) > 20 ? '#ff4d4f' : '#1890ff'}
          />
        </div>
      )
    },
    {
      title: '持有天数',
      dataIndex: 'days_held',
      key: 'days_held',
      render: (days) => `${days}天`
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button 
          type="link" 
          danger 
          size="small"
          icon={<MinusOutlined />}
          onClick={() => handleClosePosition(record.position_id)}
        >
          平仓
        </Button>
      )
    }
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>正在加载投资组合数据...</div>
      </div>
    );
  }

  return (
    <div>
      {/* 组合概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#1890ff' }}>
              ¥{portfolioMetrics?.total_value?.toLocaleString() || 0}
            </div>
            <div className="metric-label">总资产</div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ 
              color: (portfolioMetrics?.total_pnl || 0) >= 0 ? '#ff4d4f' : '#52c41a' 
            }}>
              ¥{portfolioMetrics?.total_pnl?.toLocaleString() || 0}
            </div>
            <div className="metric-label">总盈亏</div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ 
              color: (portfolioMetrics?.total_pnl_pct || 0) >= 0 ? '#ff4d4f' : '#52c41a' 
            }}>
              {((portfolioMetrics?.total_pnl_pct || 0) * 100).toFixed(2)}%
            </div>
            <div className="metric-label">总收益率</div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#52c41a' }}>
              ¥{portfolioMetrics?.cash_balance?.toLocaleString() || 0}
            </div>
            <div className="metric-label">现金余额</div>
          </Card>
        </Col>
      </Row>

      {/* 组合分析 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="持仓分布" className="dashboard-card">
            {positions.length > 0 ? (
              <ReactECharts 
                option={getPositionDistributionOption()} 
                style={{ height: '300px' }}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px', color: '#666' }}>
                暂无持仓
              </div>
            )}
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="风险指标" className="dashboard-card">
            <Row gutter={16}>
              <Col span={12}>
                <div style={{ textAlign: 'center', marginBottom: 16 }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold' }}>
                    {portfolioMetrics?.sharpe_ratio?.toFixed(2) || 0}
                  </div>
                  <div style={{ color: '#666' }}>夏普比率</div>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ textAlign: 'center', marginBottom: 16 }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold' }}>
                    {portfolioMetrics?.win_rate?.toFixed(1) || 0}%
                  </div>
                  <div style={{ color: '#666' }}>胜率</div>
                </div>
              </Col>
            </Row>
            <div style={{ marginTop: 16 }}>
              <div style={{ marginBottom: 8 }}>
                最大回撤: {portfolioMetrics?.max_drawdown?.toFixed(2) || 0}%
              </div>
              <Progress
                percent={Math.abs(portfolioMetrics?.max_drawdown || 0)}
                status="exception"
                strokeColor="#ff4d4f"
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 操作按钮 */}
      <Row style={{ marginBottom: 16 }}>
        <Col>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setAddPositionVisible(true)}
          >
            新增持仓
          </Button>
        </Col>
      </Row>

      {/* 持仓明细 */}
      <Card title="持仓明细" className="dashboard-card">
        <Table
          columns={columns}
          dataSource={positions}
          rowKey="position_id"
          pagination={false}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 新增持仓弹窗 */}
      <Modal
        title="新增持仓"
        open={addPositionVisible}
        onCancel={() => setAddPositionVisible(false)}
        onOk={() => form.submit()}
        okText="确认开仓"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleAddPosition}
        >
          <Form.Item
            name="symbol"
            label="股票代码"
            rules={[{ required: true, message: '请输入股票代码' }]}
          >
            <Input placeholder="如: 300750" />
          </Form.Item>
          <Form.Item
            name="quantity"
            label="买入数量"
            rules={[{ required: true, message: '请输入买入数量' }]}
          >
            <InputNumber 
              style={{ width: '100%' }}
              placeholder="股数"
              min={100}
              step={100}
            />
          </Form.Item>
          <Form.Item
            name="price"
            label="买入价格"
            rules={[{ required: true, message: '请输入买入价格' }]}
          >
            <InputNumber 
              style={{ width: '100%' }}
              placeholder="元"
              min={0}
              step={0.01}
              precision={2}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Portfolio;
