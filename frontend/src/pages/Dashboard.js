import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Alert, Spin, Tag, Progress } from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  FireOutlined,
  DollarOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import apiService from '../services/api';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [marketData, setMarketData] = useState(null);
  const [sentimentData, setSentimentData] = useState(null);
  const [portfolioData, setPortfolioData] = useState(null);
  const [systemStatus, setSystemStatus] = useState('online');

  useEffect(() => {
    loadDashboardData();
    // 设置定时刷新
    const interval = setInterval(loadDashboardData, 30000); // 30秒刷新一次
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // 并行加载数据
      const [market, sentiment, portfolio] = await Promise.allSettled([
        apiService.getRealtimeMarketData(),
        apiService.getCurrentSentiment(),
        apiService.getPortfolioMetrics()
      ]);

      if (market.status === 'fulfilled') {
        console.log('Market data received:', market.value);
        setMarketData(market.value?.data || market.value);
      }

      if (sentiment.status === 'fulfilled') {
        console.log('Sentiment data received:', sentiment.value);
        setSentimentData(sentiment.value?.data || sentiment.value);
      }

      if (portfolio.status === 'fulfilled') {
        console.log('Portfolio data received:', portfolio.value);
        setPortfolioData(portfolio.value?.data || portfolio.value);
      }

      setSystemStatus('online');
    } catch (error) {
      console.error('加载仪表板数据失败:', error);
      setSystemStatus('error');
      // 使用模拟数据
      setMarketData({
        limit_up_count: 46,
        limit_down_count: 13,
        max_continuous_boards: 4,
        timestamp: new Date().toISOString()
      });
      setSentimentData({
        sentiment_score: 75.5,
        market_temperature: 68.2,
        trend_direction: 'bullish',
        volatility_index: 0.234,
        sentiment_phase: 'high_tide'
      });
      setPortfolioData({
        total_value: 1000000,
        total_pnl: 25000,
        total_pnl_pct: 2.5,
        position_count: 8,
        cash_balance: 200000
      });
    } finally {
      setLoading(false);
    }
  };

  // 情绪温度计配置
  const getSentimentGaugeOption = () => {
    const score = sentimentData?.sentiment_score || 0;
    return {
      series: [
        {
          name: '市场情绪',
          type: 'gauge',
          startAngle: 180,
          endAngle: 0,
          min: 0,
          max: 100,
          splitNumber: 5,
          itemStyle: {
            color: score > 70 ? '#ff4d4f' : score > 40 ? '#faad14' : '#52c41a'
          },
          progress: {
            show: true,
            width: 30
          },
          pointer: {
            show: false
          },
          axisLine: {
            lineStyle: {
              width: 30
            }
          },
          axisTick: {
            distance: -45,
            splitNumber: 5,
            lineStyle: {
              width: 2,
              color: '#999'
            }
          },
          splitLine: {
            distance: -52,
            length: 14,
            lineStyle: {
              width: 3,
              color: '#999'
            }
          },
          axisLabel: {
            distance: -20,
            color: '#999',
            fontSize: 20
          },
          detail: {
            valueAnimation: true,
            width: '60%',
            lineHeight: 40,
            borderRadius: 8,
            offsetCenter: [0, '-15%'],
            fontSize: 30,
            fontWeight: 'bolder',
            formatter: '{value}',
            color: 'inherit'
          },
          data: [
            {
              value: score,
              name: '情绪指数'
            }
          ]
        }
      ]
    };
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>正在加载数据...</div>
      </div>
    );
  }

  return (
    <div>
      {/* 系统状态提示 */}
      {systemStatus === 'error' && (
        <Alert
          message="数据连接异常"
          description="当前显示模拟数据，请检查后端服务状态"
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      {/* 核心指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card className="metric-card">
            <Statistic
              title="涨停股数"
              value={marketData?.limit_up_count || 0}
              prefix={<ArrowUpOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
              suffix="只"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className="metric-card">
            <Statistic
              title="跌停股数"
              value={marketData?.limit_down_count || 0}
              prefix={<ArrowDownOutlined />}
              valueStyle={{ color: '#52c41a' }}
              suffix="只"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className="metric-card">
            <Statistic
              title="最高连板"
              value={marketData?.max_continuous_boards || 0}
              prefix={<FireOutlined />}
              valueStyle={{ color: '#faad14' }}
              suffix="天"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className="metric-card">
            <Statistic
              title="总盈亏"
              value={portfolioData?.total_pnl || 0}
              prefix={<DollarOutlined />}
              valueStyle={{ 
                color: (portfolioData?.total_pnl || 0) >= 0 ? '#ff4d4f' : '#52c41a' 
              }}
              suffix="元"
              precision={0}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Row gutter={[16, 16]}>
        {/* 市场情绪 */}
        <Col xs={24} lg={12}>
          <Card title="市场情绪温度计" className="dashboard-card">
            <ReactECharts 
              option={getSentimentGaugeOption()} 
              style={{ height: '300px' }}
            />
            <div style={{ textAlign: 'center', marginTop: 16 }}>
              <Tag color={
                sentimentData?.sentiment_phase === 'high_tide' ? 'red' :
                sentimentData?.sentiment_phase === 'warming' ? 'orange' :
                sentimentData?.sentiment_phase === 'freezing' ? 'blue' : 'default'
              }>
                {sentimentData?.sentiment_phase === 'high_tide' ? '高潮期' :
                 sentimentData?.sentiment_phase === 'warming' ? '回暖期' :
                 sentimentData?.sentiment_phase === 'freezing' ? '冰点期' : '未知'}
              </Tag>
              <div style={{ marginTop: 8, color: '#666' }}>
                市场温度: {sentimentData?.market_temperature?.toFixed(1) || 0}°C
              </div>
            </div>
          </Card>
        </Col>

        {/* 投资组合概览 */}
        <Col xs={24} lg={12}>
          <Card title="投资组合概览" className="dashboard-card">
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="总资产"
                  value={portfolioData?.total_value || 0}
                  suffix="元"
                  precision={0}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="持仓数量"
                  value={portfolioData?.position_count || 0}
                  suffix="只"
                />
              </Col>
            </Row>
            <div style={{ marginTop: 24 }}>
              <div style={{ marginBottom: 8 }}>
                总收益率: {((portfolioData?.total_pnl_pct || 0) * 100).toFixed(2)}%
              </div>
              <Progress
                percent={Math.abs((portfolioData?.total_pnl_pct || 0) * 100)}
                status={(portfolioData?.total_pnl_pct || 0) >= 0 ? 'success' : 'exception'}
                strokeColor={(portfolioData?.total_pnl_pct || 0) >= 0 ? '#ff4d4f' : '#52c41a'}
              />
            </div>
            <div style={{ marginTop: 16, color: '#666' }}>
              现金余额: {(portfolioData?.cash_balance || 0).toLocaleString()}元
            </div>
          </Card>
        </Col>
      </Row>

      {/* 快速操作和状态 */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="系统状态" className="dashboard-card">
            <Row gutter={16}>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <div className={`status-indicator status-${systemStatus === 'online' ? 'online' : 'offline'}`}></div>
                  数据连接: {systemStatus === 'online' ? '正常' : '异常'}
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <div className="status-indicator status-online"></div>
                  策略引擎: 运行中
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <div className="status-indicator status-online"></div>
                  风控系统: 正常
                </div>
              </Col>
            </Row>
            <div style={{ marginTop: 16, textAlign: 'center', color: '#666' }}>
              最后更新: {new Date().toLocaleTimeString()}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
