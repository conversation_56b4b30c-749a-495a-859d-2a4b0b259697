import React, { useState } from 'react';
import { Row, Col, Card, Form, Input, InputNumber, Switch, Button, Select, Divider, message } from 'antd';
import { SettingOutlined, SaveOutlined, ReloadOutlined } from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;

const Settings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSave = async (values) => {
    try {
      setLoading(true);
      // 这里应该调用API保存设置
      console.log('保存设置:', values);
      message.success('设置保存成功');
    } catch (error) {
      message.error('保存设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.resetFields();
    message.info('设置已重置');
  };

  return (
    <div>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        initialValues={{
          // 数据源设置
          akshare_enabled: true,
          tushare_enabled: false,
          tushare_token: '',
          data_refresh_interval: 30,
          
          // 策略参数
          sentiment_threshold: 70,
          leader_score_threshold: 80,
          signal_confidence_threshold: 75,
          max_positions: 10,
          
          // 风控设置
          max_position_size: 20,
          stop_loss_pct: 8,
          take_profit_pct: 15,
          max_drawdown_pct: 10,
          
          // 通知设置
          email_notifications: true,
          signal_notifications: true,
          risk_notifications: true,
          email_address: '',
          
          // 系统设置
          auto_trading: false,
          log_level: 'INFO',
          backup_enabled: true
        }}
      >
        <Row gutter={[24, 24]}>
          {/* 数据源设置 */}
          <Col xs={24} lg={12}>
            <Card title="数据源设置" className="dashboard-card">
              <Form.Item
                name="akshare_enabled"
                label="启用AKShare数据源"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                name="tushare_enabled"
                label="启用Tushare数据源"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                name="tushare_token"
                label="Tushare Token"
                help="从Tushare官网获取的API Token"
              >
                <Input.Password placeholder="请输入Tushare Token" />
              </Form.Item>
              
              <Form.Item
                name="data_refresh_interval"
                label="数据刷新间隔(秒)"
              >
                <InputNumber min={10} max={300} style={{ width: '100%' }} />
              </Form.Item>
            </Card>
          </Col>

          {/* 策略参数 */}
          <Col xs={24} lg={12}>
            <Card title="策略参数" className="dashboard-card">
              <Form.Item
                name="sentiment_threshold"
                label="情绪阈值"
                help="市场情绪指数阈值，超过此值认为市场过热"
              >
                <InputNumber min={0} max={100} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="leader_score_threshold"
                label="龙头股评分阈值"
                help="龙头股评分阈值，超过此值认为是强龙头"
              >
                <InputNumber min={0} max={100} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="signal_confidence_threshold"
                label="信号置信度阈值"
                help="交易信号置信度阈值，超过此值才执行交易"
              >
                <InputNumber min={0} max={100} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="max_positions"
                label="最大持仓数量"
                help="同时持有的最大股票数量"
              >
                <InputNumber min={1} max={50} style={{ width: '100%' }} />
              </Form.Item>
            </Card>
          </Col>

          {/* 风控设置 */}
          <Col xs={24} lg={12}>
            <Card title="风控设置" className="dashboard-card">
              <Form.Item
                name="max_position_size"
                label="单仓位最大占比(%)"
                help="单个股票仓位占总资产的最大比例"
              >
                <InputNumber min={1} max={50} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="stop_loss_pct"
                label="止损比例(%)"
                help="单个股票的最大亏损比例"
              >
                <InputNumber min={1} max={20} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="take_profit_pct"
                label="止盈比例(%)"
                help="单个股票的目标盈利比例"
              >
                <InputNumber min={5} max={50} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="max_drawdown_pct"
                label="最大回撤比例(%)"
                help="投资组合的最大回撤限制"
              >
                <InputNumber min={5} max={30} style={{ width: '100%' }} />
              </Form.Item>
            </Card>
          </Col>

          {/* 通知设置 */}
          <Col xs={24} lg={12}>
            <Card title="通知设置" className="dashboard-card">
              <Form.Item
                name="email_notifications"
                label="启用邮件通知"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                name="email_address"
                label="邮箱地址"
              >
                <Input placeholder="请输入邮箱地址" />
              </Form.Item>
              
              <Form.Item
                name="signal_notifications"
                label="交易信号通知"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                name="risk_notifications"
                label="风险预警通知"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Card>
          </Col>

          {/* 系统设置 */}
          <Col xs={24}>
            <Card title="系统设置" className="dashboard-card">
              <Row gutter={16}>
                <Col xs={24} sm={8}>
                  <Form.Item
                    name="auto_trading"
                    label="启用自动交易"
                    valuePropName="checked"
                    help="⚠️ 谨慎开启，建议先进行充分测试"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={8}>
                  <Form.Item
                    name="log_level"
                    label="日志级别"
                  >
                    <Select>
                      <Option value="DEBUG">DEBUG</Option>
                      <Option value="INFO">INFO</Option>
                      <Option value="WARNING">WARNING</Option>
                      <Option value="ERROR">ERROR</Option>
                    </Select>
                  </Form.Item>
                </Col>
                
                <Col xs={24} sm={8}>
                  <Form.Item
                    name="backup_enabled"
                    label="启用数据备份"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* 操作按钮 */}
        <Row justify="center" gutter={16}>
          <Col>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              icon={<SaveOutlined />}
              size="large"
            >
              保存设置
            </Button>
          </Col>
          <Col>
            <Button 
              onClick={handleReset}
              icon={<ReloadOutlined />}
              size="large"
            >
              重置设置
            </Button>
          </Col>
        </Row>

        {/* 系统信息 */}
        <Divider />
        <Card title="系统信息" className="dashboard-card">
          <Row gutter={16}>
            <Col xs={24} sm={8}>
              <div style={{ marginBottom: 16 }}>
                <strong>系统版本:</strong> AIQuant7 v1.0.0
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div style={{ marginBottom: 16 }}>
                <strong>Python版本:</strong> 3.11+
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div style={{ marginBottom: 16 }}>
                <strong>数据库:</strong> CSV/Feather文件
              </div>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col xs={24} sm={8}>
              <div style={{ marginBottom: 16 }}>
                <strong>最后更新:</strong> {new Date().toLocaleString()}
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div style={{ marginBottom: 16 }}>
                <strong>运行状态:</strong> <span style={{ color: '#52c41a' }}>正常</span>
              </div>
            </Col>
            <Col xs={24} sm={8}>
              <div style={{ marginBottom: 16 }}>
                <strong>数据源:</strong> AKShare
              </div>
            </Col>
          </Row>
        </Card>
      </Form>
    </div>
  );
};

export default Settings;
