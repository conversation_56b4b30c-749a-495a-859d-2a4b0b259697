import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Table, Tag, Button, Spin, Input, Select, Modal } from 'antd';
import { CrownOutlined, SearchOutlined, EyeOutlined, StarOutlined } from '@ant-design/icons';
import apiService from '../services/api';

const { Search } = Input;
const { Option } = Select;

const LeaderStocks = () => {
  const [loading, setLoading] = useState(true);
  const [leaders, setLeaders] = useState([]);
  const [filteredLeaders, setFilteredLeaders] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [filterTheme, setFilterTheme] = useState('all');
  const [selectedStock, setSelectedStock] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);

  useEffect(() => {
    loadLeaderData();
  }, []);

  useEffect(() => {
    filterLeaders();
  }, [leaders, searchText, filterTheme]);

  const loadLeaderData = async () => {
    try {
      setLoading(true);
      const response = await apiService.getLeaderRanking(100);
      console.log('Leader data received:', response);
      const leaderData = response?.data || response;
      setLeaders(Array.isArray(leaderData) ? leaderData : []);
    } catch (error) {
      console.error('加载龙头股数据失败:', error);
      // 使用模拟数据
      const mockLeaders = [
        {
          symbol: '300750',
          name: '宁德时代',
          theme: '新能源汽车',
          leader_score: 98.5,
          market_cap: 1200000000000,
          price: 245.80,
          change_pct: 8.5,
          volume_ratio: 2.3,
          turnover_rate: 1.2,
          leader_type: 'absolute',
          continuous_days: 5,
          theme_position: 1,
          strength_level: 'very_strong'
        },
        {
          symbol: '002594',
          name: '比亚迪',
          theme: '新能源汽车',
          leader_score: 95.2,
          market_cap: 800000000000,
          price: 280.50,
          change_pct: 6.8,
          volume_ratio: 1.8,
          turnover_rate: 0.9,
          leader_type: 'relative',
          continuous_days: 3,
          theme_position: 2,
          strength_level: 'strong'
        },
        {
          symbol: '000725',
          name: '京东方A',
          theme: '人工智能',
          leader_score: 88.7,
          market_cap: 150000000000,
          price: 4.25,
          change_pct: 10.0,
          volume_ratio: 3.5,
          turnover_rate: 2.8,
          leader_type: 'absolute',
          continuous_days: 2,
          theme_position: 1,
          strength_level: 'very_strong'
        }
      ];
      setLeaders(mockLeaders);
    } finally {
      setLoading(false);
    }
  };

  const filterLeaders = () => {
    if (!Array.isArray(leaders)) {
      setFilteredLeaders([]);
      return;
    }

    let filtered = leaders;

    // 按题材筛选
    if (filterTheme !== 'all') {
      filtered = filtered.filter(leader => leader.theme === filterTheme);
    }

    // 按搜索文本筛选
    if (searchText) {
      filtered = filtered.filter(leader =>
        (leader.name && leader.name.toLowerCase().includes(searchText.toLowerCase())) ||
        (leader.symbol && leader.symbol.includes(searchText)) ||
        (leader.theme && leader.theme.toLowerCase().includes(searchText.toLowerCase()))
      );
    }

    setFilteredLeaders(filtered);
  };

  const getLeaderTypeColor = (type) => {
    switch (type) {
      case 'absolute': return 'red';
      case 'relative': return 'orange';
      case 'potential': return 'blue';
      default: return 'default';
    }
  };

  const getLeaderTypeName = (type) => {
    switch (type) {
      case 'absolute': return '绝对龙头';
      case 'relative': return '相对龙头';
      case 'potential': return '潜在龙头';
      default: return '未知';
    }
  };

  const getStrengthColor = (level) => {
    switch (level) {
      case 'very_strong': return '#ff4d4f';
      case 'strong': return '#fa8c16';
      case 'medium': return '#faad14';
      case 'weak': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  const getStrengthName = (level) => {
    switch (level) {
      case 'very_strong': return '极强';
      case 'strong': return '强势';
      case 'medium': return '中等';
      case 'weak': return '偏弱';
      default: return '未知';
    }
  };

  const showStockDetail = async (stock) => {
    setSelectedStock(stock);
    setDetailVisible(true);
  };

  const columns = [
    {
      title: '排名',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_, __, index) => {
        const rank = index + 1;
        return (
          <div style={{ textAlign: 'center' }}>
            {rank <= 3 ? (
              <CrownOutlined style={{ 
                color: rank === 1 ? '#ffd700' : rank === 2 ? '#c0c0c0' : '#cd7f32',
                fontSize: '16px'
              }} />
            ) : (
              rank
            )}
          </div>
        );
      }
    },
    {
      title: '股票信息',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.symbol}</div>
        </div>
      )
    },
    {
      title: '所属题材',
      dataIndex: 'theme',
      key: 'theme',
      render: (theme, record) => (
        <div>
          <Tag color="blue">{theme}</Tag>
          <div style={{ fontSize: '12px', color: '#666' }}>
            题材第{record.theme_position}位
          </div>
        </div>
      )
    },
    {
      title: '龙头评分',
      dataIndex: 'leader_score',
      key: 'leader_score',
      width: 100,
      render: (score) => (
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontWeight: 'bold', fontSize: '16px' }}>
            {score.toFixed(1)}
          </div>
        </div>
      ),
      sorter: (a, b) => a.leader_score - b.leader_score
    },
    {
      title: '龙头类型',
      dataIndex: 'leader_type',
      key: 'leader_type',
      width: 100,
      render: (type) => (
        <Tag color={getLeaderTypeColor(type)}>
          {getLeaderTypeName(type)}
        </Tag>
      )
    },
    {
      title: '股价',
      dataIndex: 'price',
      key: 'price',
      width: 80,
      render: (price, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>¥{price.toFixed(2)}</div>
          <div className={record.change_pct >= 0 ? 'price-up' : 'price-down'}>
            {record.change_pct >= 0 ? '+' : ''}{record.change_pct.toFixed(2)}%
          </div>
        </div>
      )
    },
    {
      title: '强度',
      dataIndex: 'strength_level',
      key: 'strength_level',
      width: 80,
      render: (level) => (
        <Tag color={getStrengthColor(level)}>
          {getStrengthName(level)}
        </Tag>
      )
    },
    {
      title: '连续天数',
      dataIndex: 'continuous_days',
      key: 'continuous_days',
      width: 80,
      render: (days) => `${days}天`,
      sorter: (a, b) => a.continuous_days - b.continuous_days
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <div>
          <Button 
            type="link" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => showStockDetail(record)}
          >
            详情
          </Button>
          <Button 
            type="link" 
            size="small" 
            icon={<StarOutlined />}
          >
            关注
          </Button>
        </div>
      )
    }
  ];

  // 获取唯一题材列表
  const themes = Array.isArray(leaders) ? [...new Set(leaders.map(leader => leader.theme))] : [];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>正在加载龙头股数据...</div>
      </div>
    );
  }

  return (
    <div>
      {/* 筛选控件 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={12} md={8}>
          <Search
            placeholder="搜索股票名称、代码或题材"
            allowClear
            onSearch={setSearchText}
            onChange={(e) => setSearchText(e.target.value)}
            prefix={<SearchOutlined />}
          />
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Select
            value={filterTheme}
            onChange={setFilterTheme}
            style={{ width: '100%' }}
            placeholder="选择题材"
          >
            <Option value="all">全部题材</Option>
            {themes.map(theme => (
              <Option key={theme} value={theme}>{theme}</Option>
            ))}
          </Select>
        </Col>
      </Row>

      {/* 龙头股统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#1890ff' }}>
              {Array.isArray(leaders) ? leaders.length : 0}
            </div>
            <div className="metric-label">龙头股总数</div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#ff4d4f' }}>
              {Array.isArray(leaders) ? leaders.filter(l => l.leader_type === 'absolute').length : 0}
            </div>
            <div className="metric-label">绝对龙头</div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#52c41a' }}>
              {Array.isArray(leaders) ? leaders.filter(l => l.change_pct > 5).length : 0}
            </div>
            <div className="metric-label">强势龙头</div>
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="metric-card">
            <div className="metric-value" style={{ color: '#faad14' }}>
              {themes.length}
            </div>
            <div className="metric-label">涉及题材</div>
          </Card>
        </Col>
      </Row>

      {/* 龙头股排行榜 */}
      <Card title="龙头股排行榜" className="dashboard-card">
        <Table
          columns={columns}
          dataSource={filteredLeaders}
          rowKey="symbol"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 只龙头股`
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 股票详情弹窗 */}
      <Modal
        title={`${selectedStock?.name} (${selectedStock?.symbol}) 详情`}
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={800}
      >
        {selectedStock && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <Card title="基本信息" size="small">
                  <p>股票名称: {selectedStock.name}</p>
                  <p>股票代码: {selectedStock.symbol}</p>
                  <p>所属题材: {selectedStock.theme}</p>
                  <p>市值: {(selectedStock.market_cap / 100000000).toFixed(0)}亿</p>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="龙头指标" size="small">
                  <p>龙头评分: {selectedStock.leader_score.toFixed(1)}</p>
                  <p>龙头类型: {getLeaderTypeName(selectedStock.leader_type)}</p>
                  <p>强度等级: {getStrengthName(selectedStock.strength_level)}</p>
                  <p>连续天数: {selectedStock.continuous_days}天</p>
                </Card>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 16 }}>
              <Col span={12}>
                <Card title="交易数据" size="small">
                  <p>当前价格: ¥{selectedStock.price.toFixed(2)}</p>
                  <p>涨跌幅: {selectedStock.change_pct >= 0 ? '+' : ''}{selectedStock.change_pct.toFixed(2)}%</p>
                  <p>量比: {selectedStock.volume_ratio.toFixed(1)}</p>
                  <p>换手率: {selectedStock.turnover_rate.toFixed(2)}%</p>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="题材地位" size="small">
                  <p>题材排名: 第{selectedStock.theme_position}位</p>
                  <p>题材: {selectedStock.theme}</p>
                  <p>龙头地位: {getLeaderTypeName(selectedStock.leader_type)}</p>
                </Card>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default LeaderStocks;
