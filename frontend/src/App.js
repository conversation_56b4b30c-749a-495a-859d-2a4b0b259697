import React from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { Layout, Menu } from 'antd';
import {
  DashboardOutlined,
  LineChartOutlined,
  FireOutlined,
  CrownOutlined,
  BellOutlined,
  WalletOutlined,
  SettingOutlined
} from '@ant-design/icons';
import Dashboard from './pages/Dashboard';
import MarketSentiment from './pages/MarketSentiment';
import ThemeAnalysis from './pages/ThemeAnalysis';
import LeaderStocks from './pages/LeaderStocks';
import TradingSignals from './pages/TradingSignals';
import Portfolio from './pages/Portfolio';
import Settings from './pages/Settings';

const { Header, Content } = Layout;

const menuItems = [
  {
    key: '/',
    icon: <DashboardOutlined />,
    label: '总览'
  },
  {
    key: '/sentiment',
    icon: <LineChartOutlined />,
    label: '市场情绪'
  },
  {
    key: '/themes',
    icon: <FireOutlined />,
    label: '题材分析'
  },
  {
    key: '/leaders',
    icon: <CrownOutlined />,
    label: '龙头股'
  },
  {
    key: '/signals',
    icon: <BellOutlined />,
    label: '交易信号'
  },
  {
    key: '/portfolio',
    icon: <WalletOutlined />,
    label: '持仓管理'
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: '系统设置'
  }
];

function App() {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedKey, setSelectedKey] = React.useState('/');

  React.useEffect(() => {
    setSelectedKey(location.pathname);
  }, [location.pathname]);

  const handleMenuClick = (e) => {
    setSelectedKey(e.key);
    navigate(e.key);
  };

  return (
    <Layout className="app-layout">
      <Header className="app-header">
        <div className="app-logo">
          AIQuant7
        </div>
        <Menu
          theme="dark"
          mode="horizontal"
          selectedKeys={[selectedKey]}
          items={menuItems}
          onClick={handleMenuClick}
          className="app-menu"
          style={{ flex: 1, minWidth: 0 }}
        />
      </Header>
      
      <Content className="app-content">
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/sentiment" element={<MarketSentiment />} />
          <Route path="/themes" element={<ThemeAnalysis />} />
          <Route path="/leaders" element={<LeaderStocks />} />
          <Route path="/signals" element={<TradingSignals />} />
          <Route path="/portfolio" element={<Portfolio />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </Content>
    </Layout>
  );
}

export default App;
