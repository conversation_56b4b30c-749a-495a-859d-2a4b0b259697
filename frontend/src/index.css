/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f2f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 自定义样式 */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.app-header .ant-layout-header {
  background: transparent;
}

.app-logo {
  color: white;
  font-size: 24px;
  font-weight: bold;
  float: left;
  margin-right: 30px;
}

.app-menu {
  background: transparent;
  border-bottom: none;
}

.app-menu .ant-menu-item {
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 2px solid transparent;
}

.app-menu .ant-menu-item:hover,
.app-menu .ant-menu-item-selected {
  color: white;
  border-bottom-color: white;
  background: rgba(255, 255, 255, 0.1);
}

.app-content {
  padding: 24px;
  min-height: calc(100vh - 64px);
}

.dashboard-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-card .ant-card-head {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-bottom: 1px solid #e8e8e8;
}

.metric-card {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-value {
  font-size: 32px;
  font-weight: bold;
  margin: 10px 0;
}

.metric-label {
  color: #666;
  font-size: 14px;
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-online {
  background-color: #52c41a;
}

.status-offline {
  background-color: #ff4d4f;
}

.status-warning {
  background-color: #faad14;
}

/* 涨跌颜色 */
.price-up {
  color: #ff4d4f;
}

.price-down {
  color: #52c41a;
}

.price-neutral {
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-content {
    padding: 16px;
  }
  
  .metric-card {
    margin-bottom: 16px;
  }
  
  .metric-value {
    font-size: 24px;
  }
}
