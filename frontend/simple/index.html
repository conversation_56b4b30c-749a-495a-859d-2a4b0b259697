<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIQuant7 - 游资超短线交易策略系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .metric-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .metric-card:hover {
            transform: translateY(-2px);
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }
        .price-up { color: #dc3545; }
        .price-down { color: #198754; }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #198754; }
        .status-offline { background-color: #dc3545; }
        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-graph-up"></i> AIQuant7
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard">
                            <i class="bi bi-speedometer2"></i> 总览
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#sentiment">
                            <i class="bi bi-graph-up-arrow"></i> 市场情绪
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#themes">
                            <i class="bi bi-fire"></i> 题材分析
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#leaders">
                            <i class="bi bi-trophy"></i> 龙头股
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#signals">
                            <i class="bi bi-bell"></i> 交易信号
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-4">
        <!-- 系统状态提示 -->
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle"></i>
            <strong>演示模式</strong> 当前显示模拟数据，请启动后端服务获取真实数据
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 核心指标 -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card metric-card text-center">
                    <div class="card-body">
                        <i class="bi bi-arrow-up-circle text-danger" style="font-size: 2rem;"></i>
                        <div class="metric-value text-danger">46</div>
                        <div class="text-muted">涨停股数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card text-center">
                    <div class="card-body">
                        <i class="bi bi-arrow-down-circle text-success" style="font-size: 2rem;"></i>
                        <div class="metric-value text-success">13</div>
                        <div class="text-muted">跌停股数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card text-center">
                    <div class="card-body">
                        <i class="bi bi-fire text-warning" style="font-size: 2rem;"></i>
                        <div class="metric-value text-warning">4</div>
                        <div class="text-muted">最高连板</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card metric-card text-center">
                    <div class="card-body">
                        <i class="bi bi-currency-dollar text-primary" style="font-size: 2rem;"></i>
                        <div class="metric-value text-danger">+25,000</div>
                        <div class="text-muted">总盈亏</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 市场情绪 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-thermometer-half"></i> 市场情绪温度计
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="sentimentChart"></canvas>
                        </div>
                        <div class="text-center mt-3">
                            <span class="badge bg-danger">高潮期</span>
                            <div class="mt-2 text-muted">市场温度: 68.2°C</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 投资组合 -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-wallet2"></i> 投资组合概览
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4">1,000,000</div>
                                    <div class="text-muted">总资产(元)</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4">8</div>
                                    <div class="text-muted">持仓数量(只)</div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="mb-2">总收益率: <span class="text-danger">+2.5%</span></div>
                            <div class="progress">
                                <div class="progress-bar bg-danger" style="width: 25%"></div>
                            </div>
                        </div>
                        <div class="mt-3 text-muted">
                            现金余额: 200,000元
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 热门题材 -->
        <div class="row">
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-fire"></i> 热门题材排行
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>题材名称</th>
                                        <th>类型</th>
                                        <th>评分</th>
                                        <th>个股数量</th>
                                        <th>平均涨幅</th>
                                        <th>热度</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><i class="bi bi-trophy text-warning"></i> 1</td>
                                        <td>新能源汽车</td>
                                        <td><span class="badge bg-success">行业拐点</span></td>
                                        <td>95.5</td>
                                        <td>156只</td>
                                        <td class="price-up">+8.5%</td>
                                        <td><span class="badge bg-danger">极热</span></td>
                                    </tr>
                                    <tr>
                                        <td><i class="bi bi-trophy text-secondary"></i> 2</td>
                                        <td>人工智能</td>
                                        <td><span class="badge bg-primary">主题炒作</span></td>
                                        <td>88.2</td>
                                        <td>89只</td>
                                        <td class="price-up">+6.2%</td>
                                        <td><span class="badge bg-warning">热门</span></td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>碳中和</td>
                                        <td><span class="badge bg-info">政策驱动</span></td>
                                        <td>82.1</td>
                                        <td>234只</td>
                                        <td class="price-up">+4.8%</td>
                                        <td><span class="badge bg-secondary">温热</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统状态 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-gear"></i> 系统状态
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <div class="status-indicator status-online"></div>
                                数据连接: 正常
                            </div>
                            <div class="col-md-4">
                                <div class="status-indicator status-online"></div>
                                策略引擎: 运行中
                            </div>
                            <div class="col-md-4">
                                <div class="status-indicator status-online"></div>
                                风控系统: 正常
                            </div>
                        </div>
                        <div class="text-center mt-3 text-muted">
                            最后更新: <span id="lastUpdate"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 更新时间
        document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();

        // 情绪温度计图表
        const ctx = document.getElementById('sentimentChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['情绪指数', '剩余'],
                datasets: [{
                    data: [75.5, 24.5],
                    backgroundColor: ['#dc3545', '#e9ecef'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // 模拟数据更新
        setInterval(() => {
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }, 30000);

        console.log('AIQuant7 前端界面已加载');
        console.log('这是一个简化版本，完整功能需要启动React应用');
    </script>
</body>
</html>
