#!/usr/bin/env python3
"""
测试策略引擎
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.strategy import strategy_engine
from backend.data.data_manager import data_manager


async def test_strategy_engine():
    """测试策略引擎"""
    print("🧪 测试策略引擎...")
    
    try:
        # 获取市场数据
        print("📊 获取市场数据...")
        market_data = await data_manager.get_realtime_market_data()
        
        # 获取新闻数据
        print("📰 获取新闻数据...")
        news_data = await data_manager.get_news_data(limit=10)
        
        # 运行策略分析
        print("🔍 运行策略分析...")
        result = await strategy_engine.run_strategy_analysis(market_data, news_data)
        
        # 显示分析结果
        print("\n" + "="*60)
        print("📈 策略分析结果")
        print("="*60)
        
        # 市场情绪
        print(f"\n🌡️  市场情绪:")
        print(f"   情绪指数: {result.sentiment.sentiment_score:.1f}")
        print(f"   市场温度: {result.sentiment.market_temperature:.1f}°C")
        print(f"   情绪阶段: {result.sentiment.sentiment_phase}")
        print(f"   趋势方向: {result.sentiment.trend_direction}")
        print(f"   风险等级: {result.sentiment.risk_level}")
        print(f"   入场时机: {result.sentiment.entry_timing}")
        print(f"   阶段描述: {result.sentiment.phase_description}")
        
        # 题材分析
        print(f"\n🔥 题材分析:")
        print(f"   识别题材: {len(result.themes)}个")
        for i, theme in enumerate(result.themes[:5], 1):
            print(f"   {i}. {theme.theme_name} (评分: {theme.theme_score:.1f}, 热度: {theme.hot_level})")
        
        # 龙头股
        print(f"\n👑 龙头股:")
        print(f"   识别龙头: {len(result.leaders)}只")
        for i, leader in enumerate(result.leaders[:5], 1):
            print(f"   {i}. {leader.name} ({leader.symbol}) - 评分: {leader.leader_score:.1f}")
        
        # 交易信号
        print(f"\n📡 交易信号:")
        print(f"   生成信号: {len(result.signals)}个")
        for i, signal in enumerate(result.signals[:5], 1):
            print(f"   {i}. {signal.signal_type.upper()} {signal.name} - 置信度: {signal.confidence:.1f}%")
            print(f"      价格: ¥{signal.price:.2f} -> 目标: ¥{signal.target_price:.2f}")
            print(f"      原因: {signal.reason}")
        
        # 市场总结
        print(f"\n📊 市场总结:")
        summary = result.market_summary
        print(f"   情绪评分: {summary['sentiment_score']:.1f}")
        print(f"   热门题材: {summary['hot_themes']}/{summary['total_themes']}")
        print(f"   强势龙头: {summary['strong_leaders']}/{summary['total_leaders']}")
        print(f"   强信号数: {summary['strong_signals']}/{summary['total_signals']}")
        if summary['top_theme']:
            print(f"   顶级题材: {summary['top_theme']}")
        if summary['top_leader']:
            print(f"   顶级龙头: {summary['top_leader']}")
        
        # 策略建议
        print(f"\n💡 策略建议:")
        print(f"   {result.strategy_advice}")
        
        # 风险评估
        print(f"\n⚠️  风险评估:")
        risk = result.risk_assessment
        print(f"   风险等级: {risk['risk_level']}")
        print(f"   风险评分: {risk['risk_score']:.1f}")
        print(f"   仓位限制: {risk['position_limit']*100:.0f}%")
        print(f"   现金比例: {risk['recommended_cash_ratio']*100:.0f}%")
        if risk['risk_factors']:
            print(f"   风险因素: {', '.join(risk['risk_factors'])}")
        
        print("\n✅ 策略引擎测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 策略引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_individual_components():
    """测试各个组件"""
    print("\n🧪 测试各个策略组件...")
    
    # 获取测试数据
    market_data = await data_manager.get_realtime_market_data()
    
    # 测试情绪分析器
    print("\n1. 测试情绪分析器...")
    from backend.strategy import sentiment_analyzer
    sentiment = sentiment_analyzer.analyze_current_sentiment(market_data)
    print(f"   情绪指数: {sentiment.sentiment_score:.1f}")
    print(f"   情绪阶段: {sentiment.sentiment_phase}")
    
    # 测试题材分析器
    print("\n2. 测试题材分析器...")
    from backend.strategy import theme_analyzer
    themes = theme_analyzer.analyze_themes(market_data)
    print(f"   识别题材: {len(themes)}个")
    if themes:
        print(f"   顶级题材: {themes[0].theme_name} (评分: {themes[0].theme_score:.1f})")
    
    # 测试龙头识别器
    print("\n3. 测试龙头识别器...")
    from backend.strategy import leader_identifier
    leaders = leader_identifier.identify_leaders(market_data)
    print(f"   识别龙头: {len(leaders)}只")
    if leaders:
        print(f"   顶级龙头: {leaders[0].name} (评分: {leaders[0].leader_score:.1f})")
    
    # 测试信号生成器
    print("\n4. 测试信号生成器...")
    from backend.strategy import signal_generator
    from dataclasses import asdict
    signals = signal_generator.generate_signals(
        asdict(sentiment),
        [asdict(t) for t in themes],
        [asdict(l) for l in leaders],
        market_data
    )
    print(f"   生成信号: {len(signals)}个")
    if signals:
        print(f"   首个信号: {signals[0].signal_type.upper()} {signals[0].name}")
    
    print("\n✅ 组件测试完成！")


async def test_strategy_config():
    """测试策略配置"""
    print("\n🧪 测试策略配置...")
    
    # 获取当前配置
    summary = strategy_engine.get_strategy_summary()
    print(f"   引擎状态: {summary['engine_status']}")
    print(f"   分析间隔: {summary['analysis_interval']}秒")
    print(f"   版本: {summary['version']}")
    
    # 更新配置
    new_config = {
        'max_position_count': 15,
        'sentiment_threshold': 75
    }
    
    success = strategy_engine.update_config(new_config)
    print(f"   配置更新: {'成功' if success else '失败'}")
    
    print("\n✅ 配置测试完成！")


async def main():
    """主测试函数"""
    print("🚀 开始策略引擎测试")
    print("=" * 60)
    
    # 测试各个组件
    await test_individual_components()
    
    # 测试策略配置
    await test_strategy_config()
    
    # 测试完整策略引擎
    success = await test_strategy_engine()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 策略引擎测试全部通过！")
        print("\n💡 策略引擎功能:")
        print("   ✅ 市场情绪分析 - 识别市场情绪阶段和风险")
        print("   ✅ 题材热度分析 - 发现热门投资题材")
        print("   ✅ 龙头股识别 - 识别各题材龙头股")
        print("   ✅ 交易信号生成 - 生成买卖交易信号")
        print("   ✅ 综合策略建议 - 提供投资决策建议")
        print("   ✅ 风险评估控制 - 评估和控制投资风险")
        
        print("\n🔗 API接口:")
        print("   📊 情绪分析: /api/v1/sentiment/current")
        print("   🔥 题材排行: /api/v1/themes/ranking")
        print("   👑 龙头排行: /api/v1/leaders/ranking")
        print("   📡 交易信号: /api/v1/signals/latest")
        print("   🎯 策略建议: /api/v1/strategy/analysis")
    else:
        print("❌ 策略引擎测试存在问题，请检查日志")
    
    print("\n⚠️  注意事项:")
    print("   1. 当前使用模拟数据进行测试")
    print("   2. 生产环境请确保数据源稳定")
    print("   3. 策略参数可根据实际情况调整")
    print("   4. 建议结合人工判断进行投资决策")


if __name__ == "__main__":
    asyncio.run(main())
