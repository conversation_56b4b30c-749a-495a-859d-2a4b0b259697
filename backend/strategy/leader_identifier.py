"""
龙头股识别器
识别各题材的龙头股，分析龙头强度和持续性
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass
class LeaderStock:
    """龙头股信息数据类"""
    symbol: str
    name: str
    theme: str
    leader_score: float  # 龙头评分 (0-100)
    market_cap: float  # 市值
    price: float  # 当前价格
    change_pct: float  # 涨跌幅
    volume_ratio: float  # 量比
    turnover_rate: float  # 换手率
    leader_type: str  # 龙头类型: absolute/relative/potential
    continuous_days: int  # 连续强势天数
    theme_position: int  # 在题材中的排名
    strength_level: str  # 强度等级: very_strong/strong/medium/weak


class LeaderIdentifier:
    """龙头股识别器"""
    
    def __init__(self):
        # 龙头评分权重配置
        self.weights = {
            'market_cap': 0.25,      # 市值权重
            'price_performance': 0.30,  # 价格表现权重
            'volume_activity': 0.20,    # 成交活跃度权重
            'continuity': 0.15,         # 持续性权重
            'theme_relevance': 0.10     # 题材相关性权重
        }
        
        # 市值分级标准（亿元）
        self.market_cap_levels = {
            'mega': 1000,      # 超大盘股
            'large': 300,      # 大盘股
            'medium': 100,     # 中盘股
            'small': 50        # 小盘股
        }
    
    def identify_leaders(self, market_data: Dict, theme_data: List[Dict] = None) -> List[LeaderStock]:
        """识别龙头股"""
        try:
            leaders = []
            
            # 获取涨停股票数据
            limit_up_stocks = market_data.get('limit_up_stocks', [])
            
            if not limit_up_stocks:
                logger.warning("没有涨停股票数据，返回默认龙头股")
                return self._get_default_leaders()
            
            # 按题材分组分析
            theme_groups = self._group_stocks_by_theme(limit_up_stocks)
            
            for theme, stocks in theme_groups.items():
                if len(stocks) >= 2:  # 至少2只股票才分析龙头
                    theme_leaders = self._analyze_theme_leaders(theme, stocks)
                    leaders.extend(theme_leaders)
            
            # 按龙头评分排序
            leaders.sort(key=lambda x: x.leader_score, reverse=True)
            
            logger.info(f"龙头股识别完成，共识别{len(leaders)}只龙头股")
            return leaders
            
        except Exception as e:
            logger.error(f"龙头股识别失败: {e}")
            return self._get_default_leaders()
    
    def _group_stocks_by_theme(self, stocks: List[Dict]) -> Dict[str, List[Dict]]:
        """按题材分组股票"""
        # 简化的题材关键词匹配
        theme_keywords = {
            '新能源汽车': ['新能源', '汽车', '锂电', '电池', '充电'],
            '人工智能': ['AI', '人工智能', '机器学习', '算法', '智能'],
            '芯片半导体': ['芯片', '半导体', '集成电路', 'IC'],
            '医药生物': ['医药', '生物', '疫苗', '药业'],
            '5G通信': ['5G', '通信', '基站', '射频'],
            '军工': ['军工', '国防', '航空', '航天'],
            '新材料': ['材料', '石墨烯', '碳纤维'],
            '消费电子': ['电子', '手机', '消费'],
            '数字经济': ['数字', '大数据', '云计算'],
            '其他': []  # 默认分组
        }
        
        theme_groups = defaultdict(list)
        
        for stock in stocks:
            stock_name = stock.get('名称', stock.get('name', ''))
            matched_theme = '其他'
            
            # 匹配题材
            for theme, keywords in theme_keywords.items():
                if any(keyword in stock_name for keyword in keywords):
                    matched_theme = theme
                    break
            
            # 标准化股票数据格式
            standardized_stock = {
                'symbol': stock.get('代码', stock.get('symbol', '')),
                'name': stock_name,
                'price': float(stock.get('最新价', stock.get('price', 0))),
                'change_pct': float(stock.get('涨跌幅', stock.get('change_pct', 0))),
                'volume': float(stock.get('成交量', stock.get('volume', 0))),
                'amount': float(stock.get('成交额', stock.get('amount', 0))),
                'market_cap': float(stock.get('总市值', stock.get('market_cap', 0))),
                'turnover_rate': float(stock.get('换手率', stock.get('turnover_rate', 0)))
            }
            
            theme_groups[matched_theme].append(standardized_stock)
        
        return dict(theme_groups)
    
    def _analyze_theme_leaders(self, theme: str, stocks: List[Dict]) -> List[LeaderStock]:
        """分析题材内的龙头股"""
        leaders = []
        
        # 计算每只股票的龙头评分
        scored_stocks = []
        for stock in stocks:
            score = self._calculate_leader_score(stock, stocks)
            scored_stocks.append((stock, score))
        
        # 按评分排序
        scored_stocks.sort(key=lambda x: x[1], reverse=True)
        
        # 生成龙头股信息
        for i, (stock, score) in enumerate(scored_stocks[:5]):  # 取前5名
            leader_type = self._determine_leader_type(i, score, len(stocks))
            strength_level = self._determine_strength_level(score, stock)
            continuous_days = self._estimate_continuous_days(stock)
            
            leader = LeaderStock(
                symbol=stock['symbol'],
                name=stock['name'],
                theme=theme,
                leader_score=score,
                market_cap=stock['market_cap'],
                price=stock['price'],
                change_pct=stock['change_pct'],
                volume_ratio=self._calculate_volume_ratio(stock),
                turnover_rate=stock['turnover_rate'],
                leader_type=leader_type,
                continuous_days=continuous_days,
                theme_position=i + 1,
                strength_level=strength_level
            )
            
            leaders.append(leader)
        
        return leaders
    
    def _calculate_leader_score(self, stock: Dict, theme_stocks: List[Dict]) -> float:
        """计算龙头评分"""
        # 市值评分
        market_cap_score = self._score_market_cap(stock['market_cap'])
        
        # 价格表现评分
        price_score = self._score_price_performance(stock, theme_stocks)
        
        # 成交活跃度评分
        volume_score = self._score_volume_activity(stock, theme_stocks)
        
        # 持续性评分（基于换手率和涨幅）
        continuity_score = self._score_continuity(stock)
        
        # 题材相关性评分（暂时固定）
        relevance_score = 80.0
        
        # 综合评分
        total_score = (
            market_cap_score * self.weights['market_cap'] +
            price_score * self.weights['price_performance'] +
            volume_score * self.weights['volume_activity'] +
            continuity_score * self.weights['continuity'] +
            relevance_score * self.weights['theme_relevance']
        )
        
        return min(100.0, total_score)
    
    def _score_market_cap(self, market_cap: float) -> float:
        """市值评分"""
        market_cap_billion = market_cap / 100000000  # 转换为亿元
        
        if market_cap_billion >= self.market_cap_levels['mega']:
            return 100.0
        elif market_cap_billion >= self.market_cap_levels['large']:
            return 85.0
        elif market_cap_billion >= self.market_cap_levels['medium']:
            return 70.0
        elif market_cap_billion >= self.market_cap_levels['small']:
            return 55.0
        else:
            return 40.0
    
    def _score_price_performance(self, stock: Dict, theme_stocks: List[Dict]) -> float:
        """价格表现评分"""
        change_pct = stock['change_pct']
        
        # 在题材内的相对表现
        theme_changes = [s['change_pct'] for s in theme_stocks]
        avg_change = sum(theme_changes) / len(theme_changes)
        
        # 相对强度
        relative_strength = change_pct - avg_change
        
        # 基础分数
        base_score = min(100, max(0, change_pct * 10))
        
        # 相对强度加分
        relative_bonus = min(20, max(-20, relative_strength * 2))
        
        return min(100.0, base_score + relative_bonus)
    
    def _score_volume_activity(self, stock: Dict, theme_stocks: List[Dict]) -> float:
        """成交活跃度评分"""
        # 基于成交额和换手率
        amount = stock['amount']
        turnover = stock['turnover_rate']
        
        # 成交额评分
        amount_score = min(50, amount / 100000000)  # 亿元为单位
        
        # 换手率评分
        turnover_score = min(50, turnover * 10)
        
        return amount_score + turnover_score
    
    def _score_continuity(self, stock: Dict) -> float:
        """持续性评分"""
        # 基于换手率和涨幅的平衡
        change_pct = stock['change_pct']
        turnover = stock['turnover_rate']
        
        # 涨幅适中且换手率合理得高分
        if 5 <= change_pct <= 12 and 1 <= turnover <= 5:
            return 90.0
        elif change_pct >= 9.5:  # 涨停
            return 85.0
        elif change_pct >= 7:
            return 75.0
        elif change_pct >= 5:
            return 65.0
        else:
            return 50.0
    
    def _calculate_volume_ratio(self, stock: Dict) -> float:
        """计算量比（模拟）"""
        # 基于成交额估算量比
        amount = stock['amount']
        if amount > 1000000000:  # 10亿以上
            return 3.0 + (amount / 1000000000) * 0.5
        elif amount > 500000000:  # 5-10亿
            return 2.0 + (amount / 500000000)
        else:
            return 1.0 + (amount / 500000000)
    
    def _determine_leader_type(self, position: int, score: float, theme_size: int) -> str:
        """确定龙头类型"""
        if position == 0 and score >= 90:
            return 'absolute'  # 绝对龙头
        elif position <= 1 and score >= 80:
            return 'relative'  # 相对龙头
        else:
            return 'potential'  # 潜在龙头
    
    def _determine_strength_level(self, score: float, stock: Dict) -> str:
        """确定强度等级"""
        if score >= 90 and stock['change_pct'] >= 8:
            return 'very_strong'
        elif score >= 80 and stock['change_pct'] >= 6:
            return 'strong'
        elif score >= 70:
            return 'medium'
        else:
            return 'weak'
    
    def _estimate_continuous_days(self, stock: Dict) -> int:
        """估算连续强势天数"""
        # 基于涨幅和成交量估算
        change_pct = stock['change_pct']
        
        if change_pct >= 9.5:  # 涨停
            return 3
        elif change_pct >= 7:
            return 2
        elif change_pct >= 5:
            return 1
        else:
            return 0
    
    def get_leader_analysis(self, symbol: str) -> Optional[Dict]:
        """获取龙头股详细分析"""
        # 这里应该从数据库获取详细分析
        # 暂时返回模拟数据
        return {
            'symbol': symbol,
            'technical_analysis': {
                'trend': 'upward',
                'support_level': 220.0,
                'resistance_level': 280.0,
                'rsi': 65.5,
                'macd': 'bullish'
            },
            'fundamental_analysis': {
                'pe_ratio': 25.6,
                'pb_ratio': 3.2,
                'roe': 15.8,
                'growth_rate': 28.5
            },
            'risk_assessment': {
                'volatility': 'medium',
                'liquidity': 'high',
                'concentration_risk': 'low'
            }
        }
    
    def compare_leaders(self, symbols: List[str]) -> Dict:
        """比较多只龙头股"""
        # 这里应该进行详细比较分析
        # 暂时返回模拟数据
        return {
            'comparison_metrics': [
                'leader_score', 'market_cap', 'change_pct', 
                'volume_ratio', 'continuous_days'
            ],
            'stocks': [
                {
                    'symbol': symbol,
                    'metrics': {
                        'leader_score': 90.0 + hash(symbol) % 10,
                        'market_cap': 1000 + hash(symbol) % 500,
                        'change_pct': 8.0 + hash(symbol) % 3,
                        'volume_ratio': 2.0 + hash(symbol) % 2,
                        'continuous_days': 3 + hash(symbol) % 3
                    }
                } for symbol in symbols
            ],
            'recommendation': symbols[0] if symbols else None
        }
    
    def _get_default_leaders(self) -> List[LeaderStock]:
        """获取默认龙头股数据"""
        default_leaders = [
            LeaderStock(
                symbol='300750',
                name='宁德时代',
                theme='新能源汽车',
                leader_score=98.5,
                market_cap=1200000000000,
                price=245.80,
                change_pct=8.5,
                volume_ratio=2.3,
                turnover_rate=1.2,
                leader_type='absolute',
                continuous_days=5,
                theme_position=1,
                strength_level='very_strong'
            ),
            LeaderStock(
                symbol='002594',
                name='比亚迪',
                theme='新能源汽车',
                leader_score=95.2,
                market_cap=800000000000,
                price=280.50,
                change_pct=6.8,
                volume_ratio=1.8,
                turnover_rate=0.9,
                leader_type='relative',
                continuous_days=3,
                theme_position=2,
                strength_level='strong'
            ),
            LeaderStock(
                symbol='000725',
                name='京东方A',
                theme='人工智能',
                leader_score=88.7,
                market_cap=150000000000,
                price=4.25,
                change_pct=10.0,
                volume_ratio=3.5,
                turnover_rate=2.8,
                leader_type='absolute',
                continuous_days=2,
                theme_position=1,
                strength_level='very_strong'
            )
        ]
        
        return default_leaders


# 创建全局龙头股识别器实例
leader_identifier = LeaderIdentifier()
