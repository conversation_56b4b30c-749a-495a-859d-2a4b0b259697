"""
交易信号生成器
基于市场情绪、题材热度、龙头股分析生成交易信号
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass
import uuid

logger = logging.getLogger(__name__)


@dataclass
class TradingSignal:
    """交易信号数据类"""
    signal_id: str
    symbol: str
    name: str
    signal_type: str  # buy/sell/hold
    signal_strength: str  # strong/medium/weak
    price: float  # 信号价格
    target_price: float  # 目标价格
    stop_loss: float  # 止损价格
    confidence: float  # 置信度 (0-100)
    reason: str  # 信号原因
    created_time: str  # 创建时间
    status: str  # active/executed/expired/cancelled
    theme: str  # 相关题材
    risk_level: str  # high/medium/low
    expected_return: float  # 预期收益率
    holding_period: int  # 建议持有天数


class SignalGenerator:
    """交易信号生成器"""
    
    def __init__(self):
        # 信号生成参数
        self.signal_params = {
            'min_confidence': 60.0,      # 最小置信度
            'max_signals_per_day': 20,   # 每日最大信号数
            'sentiment_weight': 0.3,     # 情绪权重
            'theme_weight': 0.3,         # 题材权重
            'leader_weight': 0.4         # 龙头权重
        }
        
        # 风险等级配置
        self.risk_configs = {
            'low': {'max_position': 0.1, 'stop_loss_pct': 0.05, 'target_pct': 0.15},
            'medium': {'max_position': 0.15, 'stop_loss_pct': 0.08, 'target_pct': 0.20},
            'high': {'max_position': 0.08, 'stop_loss_pct': 0.12, 'target_pct': 0.30}
        }
    
    def generate_signals(self, sentiment_data: Dict, theme_data: List[Dict], 
                        leader_data: List[Dict], market_data: Dict) -> List[TradingSignal]:
        """生成交易信号"""
        try:
            signals = []
            
            # 基于龙头股生成买入信号
            buy_signals = self._generate_buy_signals(
                sentiment_data, theme_data, leader_data, market_data
            )
            signals.extend(buy_signals)
            
            # 基于市场情绪生成卖出信号
            sell_signals = self._generate_sell_signals(
                sentiment_data, market_data
            )
            signals.extend(sell_signals)
            
            # 过滤和排序信号
            filtered_signals = self._filter_signals(signals)
            
            logger.info(f"生成交易信号{len(filtered_signals)}个")
            return filtered_signals
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
            return self._get_default_signals()
    
    def _generate_buy_signals(self, sentiment_data: Dict, theme_data: List[Dict], 
                             leader_data: List[Dict], market_data: Dict) -> List[TradingSignal]:
        """生成买入信号"""
        buy_signals = []
        
        # 市场情绪检查
        sentiment_score = sentiment_data.get('sentiment_score', 50)
        sentiment_phase = sentiment_data.get('sentiment_phase', 'normal')
        
        # 只在合适的市场环境下生成买入信号
        if sentiment_phase in ['high_tide'] and sentiment_score > 85:
            logger.info("市场情绪过热，暂停生成买入信号")
            return buy_signals
        
        # 基于龙头股生成信号
        for leader_dict in leader_data[:10]:  # 取前10只龙头股
            # 如果是字典，转换为对象属性访问
            if isinstance(leader_dict, dict):
                class LeaderObj:
                    def __init__(self, data):
                        for k, v in data.items():
                            setattr(self, k, v)
                leader = LeaderObj(leader_dict)
            else:
                leader = leader_dict

            if leader.leader_score >= 80 and leader.change_pct >= 5:
                signal = self._create_buy_signal(leader, sentiment_data, theme_data)
                if signal:
                    buy_signals.append(signal)
        
        return buy_signals
    
    def _generate_sell_signals(self, sentiment_data: Dict, market_data: Dict) -> List[TradingSignal]:
        """生成卖出信号"""
        sell_signals = []
        
        # 基于市场情绪生成卖出信号
        sentiment_score = sentiment_data.get('sentiment_score', 50)
        sentiment_phase = sentiment_data.get('sentiment_phase', 'normal')
        
        if sentiment_phase == 'high_tide' and sentiment_score > 90:
            # 市场过热时生成卖出信号
            mock_positions = [
                {'symbol': '000725', 'name': '京东方A', 'price': 4.25, 'theme': '人工智能'},
                {'symbol': '002415', 'name': '海康威视', 'price': 35.80, 'theme': '人工智能'}
            ]
            
            for position in mock_positions:
                signal = self._create_sell_signal(position, sentiment_data)
                if signal:
                    sell_signals.append(signal)
        
        return sell_signals
    
    def _create_buy_signal(self, leader, sentiment_data: Dict, theme_data: List[Dict]) -> Optional[TradingSignal]:
        """创建买入信号"""
        try:
            # 计算信号强度
            strength = self._calculate_signal_strength(leader, sentiment_data)
            
            # 计算置信度
            confidence = self._calculate_confidence(leader, sentiment_data, 'buy')
            
            if confidence < self.signal_params['min_confidence']:
                return None
            
            # 计算目标价和止损价
            current_price = leader.price
            risk_level = self._assess_signal_risk(leader, sentiment_data)
            risk_config = self.risk_configs[risk_level]
            
            target_price = current_price * (1 + risk_config['target_pct'])
            stop_loss = current_price * (1 - risk_config['stop_loss_pct'])
            
            # 生成信号原因
            reason = self._generate_buy_reason(leader, sentiment_data)
            
            # 计算预期收益和持有期
            expected_return = risk_config['target_pct'] * 100
            holding_period = self._estimate_holding_period(leader, sentiment_data)
            
            signal = TradingSignal(
                signal_id=str(uuid.uuid4())[:8],
                symbol=leader.symbol,
                name=leader.name,
                signal_type='buy',
                signal_strength=strength,
                price=current_price,
                target_price=target_price,
                stop_loss=stop_loss,
                confidence=confidence,
                reason=reason,
                created_time=datetime.now().isoformat(),
                status='active',
                theme=leader.theme,
                risk_level=risk_level,
                expected_return=expected_return,
                holding_period=holding_period
            )
            
            return signal
            
        except Exception as e:
            logger.error(f"创建买入信号失败: {e}")
            return None
    
    def _create_sell_signal(self, position: Dict, sentiment_data: Dict) -> Optional[TradingSignal]:
        """创建卖出信号"""
        try:
            current_price = position['price']
            
            # 基于市场情绪的卖出信号
            confidence = 75.0  # 情绪过热时的卖出置信度
            
            target_price = current_price * 0.95  # 目标价格（获利了结）
            stop_loss = current_price * 1.05    # 止损价格（防止继续上涨错失机会）
            
            reason = "市场情绪过热，技术指标背离，建议获利了结"
            
            signal = TradingSignal(
                signal_id=str(uuid.uuid4())[:8],
                symbol=position['symbol'],
                name=position['name'],
                signal_type='sell',
                signal_strength='medium',
                price=current_price,
                target_price=target_price,
                stop_loss=stop_loss,
                confidence=confidence,
                reason=reason,
                created_time=datetime.now().isoformat(),
                status='active',
                theme=position['theme'],
                risk_level='medium',
                expected_return=-5.0,  # 预期小幅下跌
                holding_period=1
            )
            
            return signal
            
        except Exception as e:
            logger.error(f"创建卖出信号失败: {e}")
            return None
    
    def _calculate_signal_strength(self, leader, sentiment_data: Dict) -> str:
        """计算信号强度"""
        leader_score = leader.leader_score
        sentiment_score = sentiment_data.get('sentiment_score', 50)
        
        # 综合评分
        combined_score = (
            leader_score * self.signal_params['leader_weight'] +
            sentiment_score * self.signal_params['sentiment_weight'] +
            80 * self.signal_params['theme_weight']  # 假设题材评分80
        )
        
        if combined_score >= 90:
            return 'strong'
        elif combined_score >= 75:
            return 'medium'
        else:
            return 'weak'
    
    def _calculate_confidence(self, leader, sentiment_data: Dict, signal_type: str) -> float:
        """计算信号置信度"""
        base_confidence = 50.0
        
        # 龙头股评分加分
        leader_bonus = min(30, leader.leader_score * 0.3)
        
        # 市场情绪加分
        sentiment_score = sentiment_data.get('sentiment_score', 50)
        if signal_type == 'buy':
            sentiment_bonus = min(20, max(-20, (sentiment_score - 50) * 0.4))
        else:  # sell
            sentiment_bonus = min(20, max(-20, (80 - sentiment_score) * 0.4))
        
        # 连续性加分
        continuity_bonus = min(10, leader.continuous_days * 2)
        
        confidence = base_confidence + leader_bonus + sentiment_bonus + continuity_bonus
        return min(100.0, max(0.0, confidence))
    
    def _assess_signal_risk(self, leader, sentiment_data: Dict) -> str:
        """评估信号风险等级"""
        sentiment_phase = sentiment_data.get('sentiment_phase', 'normal')
        leader_score = leader.leader_score
        
        if sentiment_phase in ['high_tide'] or leader_score < 70:
            return 'high'
        elif sentiment_phase in ['heating', 'warming'] and leader_score >= 85:
            return 'medium'
        else:
            return 'low'
    
    def _generate_buy_reason(self, leader, sentiment_data: Dict) -> str:
        """生成买入信号原因"""
        reasons = []
        
        if leader.leader_score >= 90:
            reasons.append("绝对龙头地位稳固")
        
        if leader.change_pct >= 9:
            reasons.append("强势涨停")
        elif leader.change_pct >= 7:
            reasons.append("大幅上涨")
        
        if leader.continuous_days >= 3:
            reasons.append("连续强势表现")
        
        if leader.volume_ratio >= 2:
            reasons.append("成交量放大")
        
        sentiment_phase = sentiment_data.get('sentiment_phase', 'normal')
        if sentiment_phase in ['warming', 'heating']:
            reasons.append("市场情绪回暖")
        
        return "，".join(reasons) if reasons else "技术面向好，建议关注"
    
    def _estimate_holding_period(self, leader, sentiment_data: Dict) -> int:
        """估算建议持有天数"""
        base_period = 5
        
        # 根据龙头强度调整
        if leader.leader_score >= 90:
            base_period += 3
        elif leader.leader_score >= 80:
            base_period += 1
        
        # 根据市场情绪调整
        sentiment_phase = sentiment_data.get('sentiment_phase', 'normal')
        if sentiment_phase == 'warming':
            base_period += 2
        elif sentiment_phase == 'high_tide':
            base_period -= 2
        
        return max(1, min(15, base_period))
    
    def _filter_signals(self, signals: List[TradingSignal]) -> List[TradingSignal]:
        """过滤和排序信号"""
        # 按置信度过滤
        filtered = [s for s in signals if s.confidence >= self.signal_params['min_confidence']]
        
        # 按置信度排序
        filtered.sort(key=lambda x: x.confidence, reverse=True)
        
        # 限制信号数量
        max_signals = self.signal_params['max_signals_per_day']
        return filtered[:max_signals]
    
    def get_signal_summary(self, signals: List[TradingSignal]) -> Dict:
        """获取信号汇总"""
        if not signals:
            return {
                'total_signals': 0,
                'buy_signals': 0,
                'sell_signals': 0,
                'strong_signals': 0,
                'avg_confidence': 0.0
            }
        
        buy_count = len([s for s in signals if s.signal_type == 'buy'])
        sell_count = len([s for s in signals if s.signal_type == 'sell'])
        strong_count = len([s for s in signals if s.signal_strength == 'strong'])
        avg_confidence = sum(s.confidence for s in signals) / len(signals)
        
        return {
            'total_signals': len(signals),
            'buy_signals': buy_count,
            'sell_signals': sell_count,
            'strong_signals': strong_count,
            'avg_confidence': avg_confidence
        }
    
    def _get_default_signals(self) -> List[TradingSignal]:
        """获取默认信号数据"""
        default_signals = [
            TradingSignal(
                signal_id='1',
                symbol='300750',
                name='宁德时代',
                signal_type='buy',
                signal_strength='strong',
                price=245.80,
                target_price=280.00,
                stop_loss=220.00,
                confidence=85.5,
                reason='突破关键阻力位，成交量放大',
                created_time=(datetime.now() - timedelta(minutes=5)).isoformat(),
                status='active',
                theme='新能源汽车',
                risk_level='medium',
                expected_return=15.0,
                holding_period=7
            ),
            TradingSignal(
                signal_id='2',
                symbol='000725',
                name='京东方A',
                signal_type='sell',
                signal_strength='medium',
                price=4.25,
                target_price=3.80,
                stop_loss=4.50,
                confidence=72.3,
                reason='技术指标背离，获利了结',
                created_time=(datetime.now() - timedelta(minutes=15)).isoformat(),
                status='active',
                theme='人工智能',
                risk_level='medium',
                expected_return=-10.0,
                holding_period=3
            ),
            TradingSignal(
                signal_id='3',
                symbol='002594',
                name='比亚迪',
                signal_type='buy',
                signal_strength='weak',
                price=280.50,
                target_price=300.00,
                stop_loss=265.00,
                confidence=68.8,
                reason='题材热度回升，资金流入',
                created_time=(datetime.now() - timedelta(minutes=30)).isoformat(),
                status='executed',
                theme='新能源汽车',
                risk_level='low',
                expected_return=8.0,
                holding_period=5
            )
        ]
        
        return default_signals


# 创建全局信号生成器实例
signal_generator = SignalGenerator()
