"""
题材分析器
识别热门题材，分析题材生命周期，为投资决策提供依据
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from collections import defaultdict
import re

logger = logging.getLogger(__name__)


@dataclass
class ThemeInfo:
    """题材信息数据类"""
    theme_id: str
    theme_name: str
    theme_type: str  # policy/industry/event/concept
    theme_score: float  # 题材评分 (0-100)
    stock_count: int  # 相关股票数量
    leader_stocks: List[str]  # 龙头股票代码
    avg_change_pct: float  # 平均涨幅
    hot_level: str  # 热度等级: very_hot/hot/warm/cold
    lifecycle_stage: str  # 生命周期: emerging/growth/mature/decline
    keywords: List[str]  # 关键词
    news_count: int  # 相关新闻数量
    created_date: str  # 题材出现日期
    peak_date: Optional[str]  # 峰值日期


class ThemeAnalyzer:
    """题材分析器"""
    
    def __init__(self):
        # 预定义题材关键词库
        self.theme_keywords = {
            '新能源汽车': ['新能源', '汽车', '锂电池', '充电桩', '电动车', '动力电池'],
            '人工智能': ['AI', '人工智能', '机器学习', '深度学习', '算法', '神经网络'],
            '碳中和': ['碳中和', '碳达峰', '环保', '绿色能源', '节能减排', '清洁能源'],
            '芯片半导体': ['芯片', '半导体', '集成电路', 'IC', '晶圆', '封测'],
            '5G通信': ['5G', '通信', '基站', '射频', '天线', '光通信'],
            '医药生物': ['医药', '生物', '疫苗', '创新药', 'CRO', '医疗器械'],
            '军工': ['军工', '国防', '航空', '航天', '雷达', '导弹'],
            '新材料': ['新材料', '石墨烯', '碳纤维', '稀土', '钛合金', '复合材料'],
            '数字经济': ['数字经济', '大数据', '云计算', '物联网', '区块链', '数字化'],
            '消费电子': ['消费电子', '智能手机', '可穿戴', '耳机', 'VR', 'AR']
        }
        
        # 题材类型映射
        self.theme_types = {
            '新能源汽车': 'industry',
            '人工智能': 'concept',
            '碳中和': 'policy',
            '芯片半导体': 'industry',
            '5G通信': 'industry',
            '医药生物': 'industry',
            '军工': 'policy',
            '新材料': 'industry',
            '数字经济': 'concept',
            '消费电子': 'industry'
        }
    
    def analyze_themes(self, market_data: Dict, news_data: List[Dict] = None) -> List[ThemeInfo]:
        """分析当前热门题材"""
        try:
            themes = []

            # 从涨停股票中提取题材信息
            limit_up_stocks = market_data.get('limit_up_stocks', [])
            logger.info(f"开始题材分析，获取到{len(limit_up_stocks)}只涨停股票")

            # 打印前几只股票的名称用于调试
            if limit_up_stocks:
                sample_stocks = limit_up_stocks[:5]
                stock_names = [stock.get('名称', stock.get('name', '未知')) for stock in sample_stocks]
                logger.info(f"样本股票名称: {stock_names}")

            theme_stocks = self._extract_themes_from_stocks(limit_up_stocks)
            
            # 从新闻中提取题材信息
            if news_data:
                news_themes = self._extract_themes_from_news(news_data)
                theme_stocks = self._merge_theme_data(theme_stocks, news_themes)
            
            # 生成题材信息
            for theme_name, stocks in theme_stocks.items():
                if len(stocks) >= 2:  # 至少2只股票才算题材
                    theme_info = self._create_theme_info(theme_name, stocks, market_data)
                    themes.append(theme_info)
            
            # 按评分排序
            themes.sort(key=lambda x: x.theme_score, reverse=True)
            
            logger.info(f"题材分析完成，识别到{len(themes)}个热门题材")
            return themes
            
        except Exception as e:
            logger.error(f"题材分析失败: {e}")
            return self._get_default_themes()
    
    def _extract_themes_from_stocks(self, stocks: List[Dict]) -> Dict[str, List[Dict]]:
        """从股票数据中提取题材"""
        theme_stocks = defaultdict(list)

        logger.info(f"🔍 开始从{len(stocks)}只涨停股票中提取题材")

        # 行业映射到题材
        industry_theme_mapping = {
            '汽车': '新能源汽车',
            '新能源': '新能源汽车',
            '电池': '新能源汽车',
            '锂电': '新能源汽车',
            '充电': '新能源汽车',
            '软件': '人工智能',
            '计算机': '人工智能',
            '互联网': '数字经济',
            '通信': '5G通信',
            '电子': '消费电子',
            '半导体': '芯片半导体',
            '芯片': '芯片半导体',
            '集成电路': '芯片半导体',
            '医药': '医药生物',
            '生物': '医药生物',
            '化工': '新材料',
            '材料': '新材料',
            '军工': '军工',
            '国防': '军工',
            '航空': '军工',
            '环保': '碳中和',
            '节能': '碳中和'
        }

        for stock in stocks:
            stock_name = stock.get('名称', stock.get('name', ''))
            stock_code = stock.get('代码', stock.get('symbol', ''))
            stock_industry = stock.get('所属行业', stock.get('industry', ''))

            logger.info(f"🔍 分析股票: {stock_code} {stock_name} - 行业: {stock_industry}")

            # 根据股票名称匹配题材
            matched_themes = []

            # 1. 基于关键词匹配
            for theme_name, keywords in self.theme_keywords.items():
                if any(keyword in stock_name for keyword in keywords):
                    matched_themes.append(theme_name)

            # 2. 基于行业信息匹配
            for industry_key, theme_name in industry_theme_mapping.items():
                if industry_key in stock_industry or industry_key in stock_name:
                    if theme_name not in matched_themes:
                        matched_themes.append(theme_name)

            # 3. 如果没有匹配到任何题材，根据行业创建通用题材
            if not matched_themes and stock_industry and stock_industry != '未知':
                # 简化行业名称作为题材
                simplified_industry = stock_industry.replace('行业', '').replace('板块', '')[:4]
                if simplified_industry:
                    matched_themes.append(simplified_industry)

            # 添加到对应题材
            for theme_name in matched_themes:
                theme_stocks[theme_name].append({
                    'symbol': stock_code,
                    'name': stock_name,
                    'change_pct': stock.get('涨跌幅', stock.get('change_pct', 0)),
                    'price': stock.get('最新价', stock.get('price', 0)),
                    'industry': stock_industry
                })

            if matched_themes:
                logger.info(f"🔍 股票 {stock_name} 匹配题材: {matched_themes}")
            else:
                logger.warning(f"🔍 股票 {stock_name} 未匹配到任何题材")

        logger.info(f"🔍 题材提取完成，识别到{len(theme_stocks)}个题材: {list(theme_stocks.keys())}")
        for theme_name, stocks_list in theme_stocks.items():
            logger.info(f"🔍 题材 {theme_name}: {len(stocks_list)}只股票")

        return dict(theme_stocks)
    
    def _extract_themes_from_news(self, news_data: List[Dict]) -> Dict[str, int]:
        """从新闻中提取题材热度"""
        theme_news_count = defaultdict(int)
        
        for news in news_data:
            title = news.get('title', '')
            content = news.get('content', '')
            text = title + ' ' + content
            
            # 匹配题材关键词
            for theme_name, keywords in self.theme_keywords.items():
                if any(keyword in text for keyword in keywords):
                    theme_news_count[theme_name] += 1
        
        return dict(theme_news_count)
    
    def _merge_theme_data(self, theme_stocks: Dict, news_themes: Dict) -> Dict[str, List[Dict]]:
        """合并股票和新闻题材数据"""
        # 新闻数据主要用于增强题材热度，这里简单返回股票数据
        return theme_stocks
    
    def _create_theme_info(self, theme_name: str, stocks: List[Dict], market_data: Dict) -> ThemeInfo:
        """创建题材信息"""
        # 计算平均涨幅
        avg_change_pct = sum(stock.get('change_pct', 0) for stock in stocks) / len(stocks)
        
        # 选择龙头股（涨幅最高的前2只）
        sorted_stocks = sorted(stocks, key=lambda x: x.get('change_pct', 0), reverse=True)
        leader_stocks = [stock['symbol'] for stock in sorted_stocks[:2]]
        
        # 计算题材评分
        theme_score = self._calculate_theme_score(theme_name, stocks, avg_change_pct)
        
        # 判断热度等级
        hot_level = self._determine_hot_level(theme_score, len(stocks), avg_change_pct)
        
        # 判断生命周期阶段
        lifecycle_stage = self._determine_lifecycle_stage(theme_name, theme_score, avg_change_pct)
        
        return ThemeInfo(
            theme_id=f"theme_{hash(theme_name) % 10000}",
            theme_name=theme_name,
            theme_type=self.theme_types.get(theme_name, 'concept'),
            theme_score=theme_score,
            stock_count=len(stocks),
            leader_stocks=leader_stocks,
            avg_change_pct=avg_change_pct,
            hot_level=hot_level,
            lifecycle_stage=lifecycle_stage,
            keywords=self.theme_keywords.get(theme_name, []),
            news_count=0,  # 暂时设为0
            created_date=datetime.now().strftime('%Y-%m-%d'),
            peak_date=None
        )
    
    def _calculate_theme_score(self, theme_name: str, stocks: List[Dict], avg_change_pct: float) -> float:
        """计算题材评分"""
        # 基础分数：股票数量
        stock_count_score = min(50, len(stocks) * 2)
        
        # 涨幅分数
        change_score = min(30, max(0, avg_change_pct * 3))
        
        # 题材类型加权
        type_weight = {
            'policy': 1.2,    # 政策驱动题材权重高
            'industry': 1.1,  # 行业题材权重中等
            'event': 1.0,     # 事件驱动题材权重正常
            'concept': 0.9    # 概念题材权重稍低
        }
        
        theme_type = self.theme_types.get(theme_name, 'concept')
        weight = type_weight.get(theme_type, 1.0)
        
        # 综合评分
        base_score = stock_count_score + change_score
        final_score = min(100, base_score * weight)
        
        return final_score
    
    def _determine_hot_level(self, theme_score: float, stock_count: int, avg_change_pct: float) -> str:
        """判断热度等级"""
        if theme_score >= 90 and avg_change_pct >= 8:
            return 'very_hot'
        elif theme_score >= 75 and avg_change_pct >= 5:
            return 'hot'
        elif theme_score >= 60 and avg_change_pct >= 2:
            return 'warm'
        else:
            return 'cold'
    
    def _determine_lifecycle_stage(self, theme_name: str, theme_score: float, avg_change_pct: float) -> str:
        """判断生命周期阶段"""
        # 这里可以根据历史数据判断，暂时使用简单规则
        if theme_score >= 85 and avg_change_pct >= 8:
            return 'mature'  # 成熟期（可能接近峰值）
        elif theme_score >= 70:
            return 'growth'  # 成长期
        elif theme_score >= 50:
            return 'emerging'  # 萌芽期
        else:
            return 'decline'  # 衰退期
    
    def get_theme_detail(self, theme_id: str) -> Optional[ThemeInfo]:
        """获取题材详情"""
        # 这里应该从数据库或缓存中获取题材详情
        # 暂时返回None
        return None
    
    def get_theme_lifecycle(self, theme_id: str) -> Dict:
        """获取题材生命周期分析"""
        # 这里应该分析题材的历史表现
        # 暂时返回模拟数据
        return {
            'theme_id': theme_id,
            'stages': [
                {'stage': 'emerging', 'duration': 5, 'avg_return': 2.5},
                {'stage': 'growth', 'duration': 10, 'avg_return': 8.2},
                {'stage': 'mature', 'duration': 7, 'avg_return': 15.6},
                {'stage': 'decline', 'duration': 8, 'avg_return': -3.2}
            ],
            'current_stage': 'growth',
            'predicted_peak_date': (datetime.now() + timedelta(days=15)).strftime('%Y-%m-%d')
        }
    
    def _get_default_themes(self) -> List[ThemeInfo]:
        """获取默认题材数据"""
        default_themes = [
            ThemeInfo(
                theme_id='1',
                theme_name='新能源汽车',
                theme_type='industry',
                theme_score=95.5,
                stock_count=156,
                leader_stocks=['300750', '002594'],
                avg_change_pct=8.5,
                hot_level='very_hot',
                lifecycle_stage='growth',
                keywords=['新能源', '汽车', '锂电池', '充电桩'],
                news_count=25,
                created_date='2024-12-01',
                peak_date=None
            ),
            ThemeInfo(
                theme_id='2',
                theme_name='人工智能',
                theme_type='concept',
                theme_score=88.2,
                stock_count=89,
                leader_stocks=['000725', '002415'],
                avg_change_pct=6.2,
                hot_level='hot',
                lifecycle_stage='mature',
                keywords=['AI', '人工智能', '机器学习', '算法'],
                news_count=18,
                created_date='2024-11-15',
                peak_date=None
            ),
            ThemeInfo(
                theme_id='3',
                theme_name='碳中和',
                theme_type='policy',
                theme_score=82.1,
                stock_count=234,
                leader_stocks=['600036', '000002'],
                avg_change_pct=4.8,
                hot_level='warm',
                lifecycle_stage='growth',
                keywords=['碳中和', '碳达峰', '环保', '绿色能源'],
                news_count=12,
                created_date='2024-11-20',
                peak_date=None
            )
        ]
        
        return default_themes


# 创建全局题材分析器实例
theme_analyzer = ThemeAnalyzer()
