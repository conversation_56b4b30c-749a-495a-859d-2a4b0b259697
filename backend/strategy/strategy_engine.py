"""
策略引擎
协调各个分析模块，生成综合的投资策略和交易信号
"""
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

from .sentiment_analyzer import sentiment_analyzer, SentimentMetrics
from .theme_analyzer import theme_analyzer, ThemeInfo
from .leader_identifier import leader_identifier, LeaderStock
from .signal_generator import signal_generator, TradingSignal

logger = logging.getLogger(__name__)


@dataclass
class StrategyResult:
    """策略分析结果"""
    timestamp: str
    sentiment: SentimentMetrics
    themes: List[ThemeInfo]
    leaders: List[LeaderStock]
    signals: List[TradingSignal]
    market_summary: Dict[str, Any]
    strategy_advice: str
    risk_assessment: Dict[str, Any]


class StrategyEngine:
    """策略引擎"""
    
    def __init__(self):
        self.last_analysis_time = None
        self.analysis_interval = 300  # 5分钟分析一次
        self.cache = {}
        
        # 策略配置
        self.config = {
            'max_position_count': 10,
            'max_single_position': 0.2,  # 单仓位最大占比20%
            'stop_loss_pct': 0.08,       # 止损8%
            'take_profit_pct': 0.15,     # 止盈15%
            'sentiment_threshold': 70,    # 情绪阈值
            'leader_threshold': 80        # 龙头评分阈值
        }
    
    async def run_strategy_analysis(self, market_data: Dict, news_data: List[Dict] = None) -> StrategyResult:
        """运行策略分析"""
        try:
            logger.info("开始策略分析...")
            
            # 1. 市场情绪分析
            sentiment = sentiment_analyzer.analyze_current_sentiment(market_data)
            logger.info(f"情绪分析完成: {sentiment.sentiment_score:.1f}")
            
            # 2. 题材分析
            themes = theme_analyzer.analyze_themes(market_data, news_data)
            logger.info(f"题材分析完成: 识别{len(themes)}个题材")
            
            # 3. 龙头股识别
            leaders = leader_identifier.identify_leaders(market_data, [asdict(t) for t in themes])
            logger.info(f"龙头识别完成: 识别{len(leaders)}只龙头股")
            
            # 4. 交易信号生成
            signals = signal_generator.generate_signals(
                asdict(sentiment), 
                [asdict(t) for t in themes],
                [asdict(l) for l in leaders],
                market_data
            )
            logger.info(f"信号生成完成: 生成{len(signals)}个信号")
            
            # 5. 生成市场总结
            market_summary = self._generate_market_summary(sentiment, themes, leaders, signals)
            
            # 6. 生成策略建议
            strategy_advice = self._generate_strategy_advice(sentiment, themes, leaders, signals)
            
            # 7. 风险评估
            risk_assessment = self._assess_overall_risk(sentiment, themes, leaders, signals)
            
            result = StrategyResult(
                timestamp=datetime.now().isoformat(),
                sentiment=sentiment,
                themes=themes,
                leaders=leaders,
                signals=signals,
                market_summary=market_summary,
                strategy_advice=strategy_advice,
                risk_assessment=risk_assessment
            )
            
            self.last_analysis_time = datetime.now()
            logger.info("策略分析完成")
            
            return result
            
        except Exception as e:
            logger.error(f"策略分析失败: {e}")
            return self._get_default_strategy_result()
    
    def _generate_market_summary(self, sentiment: SentimentMetrics, themes: List[ThemeInfo], 
                                leaders: List[LeaderStock], signals: List[TradingSignal]) -> Dict[str, Any]:
        """生成市场总结"""
        # 统计数据
        hot_themes = [t for t in themes if t.hot_level in ['very_hot', 'hot']]
        strong_leaders = [l for l in leaders if l.strength_level in ['very_strong', 'strong']]
        buy_signals = [s for s in signals if s.signal_type == 'buy']
        strong_signals = [s for s in signals if s.signal_strength == 'strong']
        
        return {
            'sentiment_score': sentiment.sentiment_score,
            'sentiment_phase': sentiment.sentiment_phase,
            'market_temperature': sentiment.market_temperature,
            'total_themes': len(themes),
            'hot_themes': len(hot_themes),
            'total_leaders': len(leaders),
            'strong_leaders': len(strong_leaders),
            'total_signals': len(signals),
            'buy_signals': len(buy_signals),
            'strong_signals': len(strong_signals),
            'top_theme': themes[0].theme_name if themes else None,
            'top_leader': leaders[0].name if leaders else None,
            'avg_signal_confidence': sum(s.confidence for s in signals) / len(signals) if signals else 0
        }
    
    def _generate_strategy_advice(self, sentiment: SentimentMetrics, themes: List[ThemeInfo], 
                                 leaders: List[LeaderStock], signals: List[TradingSignal]) -> str:
        """生成策略建议"""
        advice_parts = []
        
        # 基于市场情绪的建议
        if sentiment.sentiment_phase == 'freezing':
            advice_parts.append("市场处于冰点期，建议耐心等待，寻找优质标的建仓机会")
        elif sentiment.sentiment_phase == 'warming':
            advice_parts.append("市场开始回暖，可适当增加仓位，关注热点题材")
        elif sentiment.sentiment_phase == 'heating':
            advice_parts.append("市场升温明显，建议积极参与，但要控制仓位风险")
        elif sentiment.sentiment_phase == 'high_tide':
            advice_parts.append("市场情绪高涨，建议谨慎操作，适当减仓获利了结")
        
        # 基于题材的建议
        if themes:
            top_theme = themes[0]
            if top_theme.hot_level == 'very_hot':
                advice_parts.append(f"重点关注{top_theme.theme_name}题材，但注意高位风险")
            elif top_theme.hot_level == 'hot':
                advice_parts.append(f"积极关注{top_theme.theme_name}题材机会")
        
        # 基于龙头股的建议
        strong_leaders = [l for l in leaders if l.strength_level in ['very_strong', 'strong']]
        if strong_leaders:
            advice_parts.append(f"重点关注{len(strong_leaders)}只强势龙头股")
        
        # 基于信号的建议
        strong_signals = [s for s in signals if s.signal_strength == 'strong']
        if strong_signals:
            advice_parts.append(f"发现{len(strong_signals)}个强信号，建议重点关注")
        
        return "；".join(advice_parts) if advice_parts else "市场状态正常，按既定策略操作"
    
    def _assess_overall_risk(self, sentiment: SentimentMetrics, themes: List[ThemeInfo], 
                           leaders: List[LeaderStock], signals: List[TradingSignal]) -> Dict[str, Any]:
        """评估整体风险"""
        risk_factors = []
        risk_score = 50.0  # 基础风险分数
        
        # 情绪风险
        if sentiment.sentiment_phase == 'high_tide':
            risk_factors.append("市场情绪过热")
            risk_score += 20
        elif sentiment.volatility_index > 0.7:
            risk_factors.append("市场波动率过高")
            risk_score += 15
        
        # 题材风险
        very_hot_themes = [t for t in themes if t.hot_level == 'very_hot']
        if len(very_hot_themes) > 3:
            risk_factors.append("过多极热题材，存在泡沫风险")
            risk_score += 10
        
        # 信号风险
        high_risk_signals = [s for s in signals if s.risk_level == 'high']
        if len(high_risk_signals) > len(signals) * 0.5:
            risk_factors.append("高风险信号占比过高")
            risk_score += 10
        
        # 确定风险等级
        if risk_score >= 80:
            risk_level = 'high'
        elif risk_score >= 60:
            risk_level = 'medium'
        else:
            risk_level = 'low'
        
        return {
            'risk_level': risk_level,
            'risk_score': min(100, risk_score),
            'risk_factors': risk_factors,
            'position_limit': self._calculate_position_limit(risk_level),
            'recommended_cash_ratio': self._calculate_cash_ratio(risk_level)
        }
    
    def _calculate_position_limit(self, risk_level: str) -> float:
        """计算仓位限制"""
        limits = {
            'low': 0.9,      # 低风险时可满仓
            'medium': 0.7,   # 中风险时70%仓位
            'high': 0.5      # 高风险时50%仓位
        }
        return limits.get(risk_level, 0.7)
    
    def _calculate_cash_ratio(self, risk_level: str) -> float:
        """计算建议现金比例"""
        ratios = {
            'low': 0.1,      # 低风险时保留10%现金
            'medium': 0.3,   # 中风险时保留30%现金
            'high': 0.5      # 高风险时保留50%现金
        }
        return ratios.get(risk_level, 0.3)
    
    def get_strategy_summary(self) -> Dict[str, Any]:
        """获取策略总结"""
        return {
            'engine_status': 'running',
            'last_analysis': self.last_analysis_time.isoformat() if self.last_analysis_time else None,
            'analysis_interval': self.analysis_interval,
            'config': self.config,
            'version': '1.0.0'
        }
    
    def update_config(self, new_config: Dict[str, Any]) -> bool:
        """更新策略配置"""
        try:
            self.config.update(new_config)
            logger.info(f"策略配置已更新: {new_config}")
            return True
        except Exception as e:
            logger.error(f"更新策略配置失败: {e}")
            return False
    
    def _get_default_strategy_result(self) -> StrategyResult:
        """获取默认策略结果"""
        from .sentiment_analyzer import SentimentMetrics
        from .theme_analyzer import ThemeInfo
        from .leader_identifier import LeaderStock
        from .signal_generator import TradingSignal
        
        default_sentiment = SentimentMetrics(
            sentiment_score=50.0,
            market_temperature=50.0,
            trend_direction='neutral',
            volatility_index=0.3,
            sentiment_phase='normal',
            phase_description='市场情绪正常',
            risk_level='medium',
            entry_timing='caution'
        )
        
        return StrategyResult(
            timestamp=datetime.now().isoformat(),
            sentiment=default_sentiment,
            themes=[],
            leaders=[],
            signals=[],
            market_summary={
                'sentiment_score': 50.0,
                'total_themes': 0,
                'total_leaders': 0,
                'total_signals': 0
            },
            strategy_advice="系统初始化中，请稍后查看分析结果",
            risk_assessment={
                'risk_level': 'medium',
                'risk_score': 50.0,
                'risk_factors': [],
                'position_limit': 0.7,
                'recommended_cash_ratio': 0.3
            }
        )


# 创建全局策略引擎实例
strategy_engine = StrategyEngine()
