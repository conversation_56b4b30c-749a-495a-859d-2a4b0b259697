"""
API路由定义
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Optional
from datetime import datetime
from loguru import logger

from backend.data.data_manager import data_manager
from backend.core.cache import cache_manager
from backend.core.sentiment.sentiment_engine import sentiment_engine
from backend.core.theme.theme_engine import theme_engine
from backend.core.leader.leader_engine import leader_engine
from backend.core.signal.signal_engine import signal_engine
from backend.core.position.position_manager import position_manager

# 创建API路由器
api_router = APIRouter()


@api_router.get("/")
async def api_root():
    """API根路径"""
    return {
        "message": "AIQuant7 API",
        "version": "1.0.0",
        "endpoints": {
            "market": "/market",
            "sentiment": "/sentiment", 
            "themes": "/themes",
            "signals": "/signals",
            "positions": "/positions"
        }
    }


@api_router.get("/market/realtime")
async def get_realtime_market():
    """获取实时市场数据"""
    try:
        # 先尝试从缓存获取
        cached_data = await cache_manager.get_market_sentiment()
        if cached_data:
            return {
                "status": "success",
                "data": cached_data,
                "source": "cache"
            }
        
        # 从数据源获取
        market_data = await data_manager.get_realtime_market_data()
        
        if market_data:
            # 缓存数据
            await cache_manager.cache_market_sentiment(market_data)
            
            return {
                "status": "success", 
                "data": market_data,
                "source": "realtime"
            }
        else:
            raise HTTPException(status_code=404, detail="无法获取市场数据")
            
    except Exception as e:
        logger.error(f"获取实时市场数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/market/stock/{symbol}")
async def get_stock_data(symbol: str):
    """获取单只股票数据"""
    try:
        # 先尝试从缓存获取
        cached_data = await cache_manager.get_stock_realtime(symbol)
        if cached_data:
            return {
                "status": "success",
                "data": cached_data,
                "source": "cache"
            }
        
        # 从数据源获取
        stock_data = await data_manager.get_stock_realtime_data(symbol)
        
        if stock_data:
            # 缓存数据
            await cache_manager.cache_stock_realtime(symbol, stock_data)
            
            return {
                "status": "success",
                "data": stock_data,
                "source": "realtime"
            }
        else:
            raise HTTPException(status_code=404, detail=f"无法获取股票{symbol}数据")
            
    except Exception as e:
        logger.error(f"获取股票{symbol}数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/market/news")
async def get_news_data(limit: int = 50):
    """获取新闻数据"""
    try:
        news_data = await data_manager.get_news_data(limit)
        
        return {
            "status": "success",
            "data": news_data,
            "count": len(news_data)
        }
        
    except Exception as e:
        logger.error(f"获取新闻数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/sentiment/current")
async def get_current_sentiment():
    """获取当前市场情绪"""
    try:
        # 获取实时市场数据
        market_data = await data_manager.get_realtime_market_data()

        if not market_data:
            raise HTTPException(status_code=404, detail="无法获取市场数据")

        # 计算情绪指标
        sentiment_data = sentiment_engine.process_market_data(market_data)

        # 保存情绪数据
        await sentiment_engine.save_sentiment_data_to_file(sentiment_data)

        # 获取情绪摘要
        sentiment_summary = sentiment_engine.get_sentiment_summary()

        return {
            "status": "success",
            "data": sentiment_summary
        }

    except Exception as e:
        logger.error(f"获取市场情绪失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/sentiment/analysis")
async def get_sentiment_analysis():
    """获取详细的情绪分析"""
    try:
        # 获取情绪摘要
        sentiment_summary = sentiment_engine.get_sentiment_summary()

        if sentiment_summary["status"] != "success":
            return sentiment_summary

        # 判断入场时机
        is_good_timing, timing_reason = sentiment_engine.is_good_entry_timing()

        # 添加交易建议
        sentiment_summary["trading_advice"] = {
            "is_good_entry_timing": is_good_timing,
            "timing_reason": timing_reason,
            "recommended_position": sentiment_engine._get_recommended_position()
        }

        return {
            "status": "success",
            "data": sentiment_summary
        }

    except Exception as e:
        logger.error(f"获取情绪分析失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/sentiment/history")
async def get_sentiment_history(days: int = 7):
    """获取历史情绪数据"""
    try:
        # 加载历史数据
        await sentiment_engine.load_historical_sentiment_data(days)

        # 获取历史数据
        history_data = []
        for sentiment_data in sentiment_engine.sentiment_history[-days:]:
            history_data.append({
                "timestamp": sentiment_data.timestamp.isoformat(),
                "sentiment_score": sentiment_data.sentiment_score,
                "sentiment_phase": sentiment_data.sentiment_phase,
                "market_temperature": sentiment_data.market_temperature,
                "trend_direction": sentiment_data.trend_direction,
                "volatility_index": sentiment_data.volatility_index,
                "limit_up_count": sentiment_data.limit_up_count,
                "limit_down_count": sentiment_data.limit_down_count,
                "max_continuous_boards": sentiment_data.max_continuous_boards
            })

        return {
            "status": "success",
            "data": history_data,
            "count": len(history_data)
        }

    except Exception as e:
        logger.error(f"获取历史情绪数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/themes/ranking")
async def get_theme_ranking(limit: int = 20):
    """获取题材排行"""
    try:
        # 先尝试从缓存获取
        cached_themes = await cache_manager.get_theme_data()
        if cached_themes:
            return {
                "status": "success",
                "data": cached_themes[:limit],
                "source": "cache"
            }

        # 获取新闻数据
        news_data = await data_manager.get_news_data(100)

        if not news_data:
            raise HTTPException(status_code=404, detail="无法获取新闻数据")

        # 识别题材
        themes = theme_engine.identify_themes_from_news(news_data)

        if themes:
            # 缓存题材数据
            await cache_manager.cache_theme_data(themes)

            # 保存到文件
            await theme_engine.save_theme_data_to_file(themes)

            return {
                "status": "success",
                "data": themes[:limit],
                "source": "realtime",
                "count": len(themes)
            }
        else:
            return {
                "status": "success",
                "data": [],
                "message": "暂未识别到热点题材"
            }

    except Exception as e:
        logger.error(f"获取题材排行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/themes/{theme_id}")
async def get_theme_detail(theme_id: str):
    """获取题材详细信息"""
    try:
        # 从历史数据中查找题材
        if theme_id in theme_engine.theme_history:
            theme_data = theme_engine.theme_history[theme_id]

            return {
                "status": "success",
                "data": {
                    "theme_id": theme_data.theme_id,
                    "theme_name": theme_data.theme_name,
                    "theme_type": theme_data.theme_type,
                    "theme_score": theme_data.theme_score,
                    "lifecycle_stage": theme_data.lifecycle_stage,
                    "heat_trend": theme_data.heat_trend,
                    "news_count": theme_data.news_count,
                    "keywords": theme_data.keywords,
                    "related_stocks": theme_data.related_stocks,
                    "first_appeared": theme_data.first_appeared.isoformat(),
                    "last_updated": theme_data.last_updated.isoformat(),
                    "factors": {
                        "policy_factor": theme_data.policy_factor,
                        "industry_factor": theme_data.industry_factor,
                        "event_factor": theme_data.event_factor,
                        "concept_factor": theme_data.concept_factor
                    }
                }
            }
        else:
            raise HTTPException(status_code=404, detail=f"未找到题材: {theme_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取题材详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/themes/analysis/lifecycle")
async def analyze_theme_lifecycle():
    """分析题材生命周期分布"""
    try:
        # 获取所有题材
        themes = theme_engine.get_theme_ranking(100)

        if not themes:
            return {
                "status": "success",
                "data": {
                    "lifecycle_distribution": {},
                    "total_themes": 0
                }
            }

        # 统计生命周期分布
        lifecycle_count = {}
        for theme in themes:
            stage = theme.get('lifecycle_stage', '萌芽期')
            lifecycle_count[stage] = lifecycle_count.get(stage, 0) + 1

        return {
            "status": "success",
            "data": {
                "lifecycle_distribution": lifecycle_count,
                "total_themes": len(themes),
                "analysis": {
                    "active_themes": lifecycle_count.get('发酵期', 0) + lifecycle_count.get('高潮期', 0),
                    "emerging_themes": lifecycle_count.get('萌芽期', 0),
                    "declining_themes": lifecycle_count.get('衰退期', 0) + lifecycle_count.get('分化期', 0)
                }
            }
        }

    except Exception as e:
        logger.error(f"分析题材生命周期失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/leaders/ranking")
async def get_leader_ranking(theme_name: str = None, confidence: str = None, limit: int = 20):
    """获取龙头股排行榜"""
    try:
        # 获取龙头股排行
        leaders = leader_engine.get_leader_ranking(
            theme_name=theme_name,
            confidence_filter=confidence
        )

        return {
            "status": "success",
            "data": leaders[:limit],
            "count": len(leaders),
            "filters": {
                "theme_name": theme_name,
                "confidence": confidence
            }
        }

    except Exception as e:
        logger.error(f"获取龙头股排行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/leaders/{symbol}/analysis")
async def analyze_leader_stock(symbol: str):
    """分析单只龙头股"""
    try:
        # 分析龙头股稳定性
        stability_analysis = leader_engine.analyze_leader_stability(symbol)

        if stability_analysis["status"] != "success":
            raise HTTPException(status_code=404, detail=stability_analysis.get("message", "分析失败"))

        return {
            "status": "success",
            "data": stability_analysis
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析龙头股{symbol}失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/leaders/compare")
async def compare_leader_stocks(symbols: List[str]):
    """比较多只龙头股"""
    try:
        if not symbols or len(symbols) < 2:
            raise HTTPException(status_code=400, detail="至少需要2只股票进行比较")

        if len(symbols) > 10:
            raise HTTPException(status_code=400, detail="最多支持比较10只股票")

        # 比较龙头股
        comparison_result = leader_engine.compare_leaders(symbols)

        if comparison_result["status"] != "success":
            raise HTTPException(status_code=500, detail=comparison_result.get("message", "比较失败"))

        return {
            "status": "success",
            "data": comparison_result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"比较龙头股失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/leaders/themes/{theme_name}/summary")
async def get_theme_leader_summary(theme_name: str):
    """获取题材龙头股摘要"""
    try:
        # 获取题材龙头摘要
        summary = leader_engine.get_theme_leader_summary(theme_name)

        if summary["status"] != "success":
            if summary["status"] == "no_data":
                raise HTTPException(status_code=404, detail=summary.get("message", "未找到数据"))
            else:
                raise HTTPException(status_code=500, detail=summary.get("message", "获取摘要失败"))

        return {
            "status": "success",
            "data": summary
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取题材{theme_name}龙头摘要失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/leaders/identify")
async def identify_theme_leaders(
    theme_name: str,
    candidate_symbols: List[str],
    theme_keywords: List[str] = None
):
    """识别题材龙头股"""
    try:
        if not candidate_symbols:
            raise HTTPException(status_code=400, detail="候选股票列表不能为空")

        # 获取候选股票的详细数据
        candidate_stocks = []
        for symbol in candidate_symbols:
            stock_data = await data_manager.get_stock_realtime_data(symbol)
            if stock_data:
                # 添加一些模拟数据用于测试
                stock_data.update({
                    'name': f'股票{symbol}',
                    'industry': '测试行业',
                    'market_cap': 100.0,  # 模拟市值
                    'continuous_boards': 0,  # 模拟连板数
                    'change_pct': stock_data.get('close', 0) * 0.05  # 模拟涨幅
                })
                candidate_stocks.append(stock_data)

        if not candidate_stocks:
            raise HTTPException(status_code=404, detail="无法获取候选股票数据")

        # 识别龙头股
        leaders = leader_engine.identify_theme_leaders(
            theme_name=theme_name,
            candidate_stocks=candidate_stocks,
            theme_keywords=theme_keywords or [],
            top_n=10
        )

        # 保存龙头股数据
        await leader_engine.save_leader_data_to_file(theme_name)

        # 转换为API响应格式
        leader_data = []
        for leader in leaders:
            leader_dict = {
                'symbol': leader.symbol,
                'name': leader.name,
                'leader_score': leader.leader_score,
                'confidence_level': leader.confidence_level,
                'is_leader': leader.is_leader,
                'return_rank': leader.return_rank,
                'volume_rank': leader.volume_rank,
                'continuous_boards': leader.continuous_boards,
                'concept_purity': leader.concept_purity,
                'market_cap': leader.market_cap,
                'change_pct': leader.change_pct
            }
            leader_data.append(leader_dict)

        return {
            "status": "success",
            "data": {
                "theme_name": theme_name,
                "leaders": leader_data,
                "leader_count": len([l for l in leaders if l.is_leader]),
                "total_candidates": len(candidate_stocks)
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"识别题材{theme_name}龙头股失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/signals/latest")
async def get_latest_signals(limit: int = 20, signal_type: str = None, min_confidence: float = 0.6):
    """获取最新交易信号"""
    try:
        # 先尝试从缓存获取
        cached_signals = await cache_manager.get_trading_signals()
        if cached_signals:
            return {
                "status": "success",
                "data": cached_signals[:limit],
                "source": "cache"
            }

        # 获取市场数据
        market_data = await data_manager.get_realtime_market_data()
        if not market_data:
            raise HTTPException(status_code=404, detail="无法获取市场数据")

        # 获取股票池（简化实现）
        stock_pool = ["000001", "000002", "300750", "002594", "600036", "000858"]

        # 批量生成信号
        all_signals = signal_engine.batch_generate_signals(stock_pool, market_data)

        # 过滤信号
        from backend.core.signal.signal_engine import SignalType
        signal_types = None
        if signal_type:
            if signal_type.lower() == "buy":
                signal_types = [SignalType.BUY]
            elif signal_type.lower() == "sell":
                signal_types = [SignalType.SELL]
            elif signal_type.lower() == "hold":
                signal_types = [SignalType.HOLD]

        filtered_signals = signal_engine.filter_signals_by_criteria(
            all_signals,
            min_confidence=min_confidence,
            signal_types=signal_types
        )

        # 转换为API响应格式
        signal_data = []
        for signal in filtered_signals[:limit]:
            signal_dict = {
                'symbol': signal.symbol,
                'signal_type': signal.signal_type.value,
                'signal_strength': signal.signal_strength.value,
                'confidence': signal.confidence,
                'price': signal.price,
                'target_price': signal.target_price,
                'stop_loss': signal.stop_loss,
                'composite_score': signal.composite_score,
                'reason': signal.reason,
                'risk_level': signal.risk_level,
                'max_position_ratio': signal.max_position_ratio,
                'theme_name': signal.theme_name,
                'is_leader': signal.is_leader,
                'market_sentiment_phase': signal.market_sentiment_phase,
                'timestamp': signal.timestamp.isoformat(),
                'valid_until': signal.valid_until.isoformat()
            }
            signal_data.append(signal_dict)

        # 缓存信号数据
        await cache_manager.cache_trading_signals(signal_data)

        # 保存信号到文件
        await signal_engine.save_signals_to_file(filtered_signals)

        return {
            "status": "success",
            "data": signal_data,
            "count": len(signal_data),
            "total_generated": len(all_signals),
            "source": "realtime"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取交易信号失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/signals/{symbol}")
async def get_stock_signal(symbol: str):
    """获取单只股票的交易信号"""
    try:
        # 获取市场数据
        market_data = await data_manager.get_realtime_market_data()
        if not market_data:
            raise HTTPException(status_code=404, detail="无法获取市场数据")

        # 生成单只股票信号
        signal = signal_engine.generate_comprehensive_signal(symbol, market_data, None)

        if not signal:
            raise HTTPException(status_code=404, detail=f"无法生成股票{symbol}的交易信号")

        # 转换为API响应格式
        signal_data = {
            'symbol': signal.symbol,
            'signal_type': signal.signal_type.value,
            'signal_strength': signal.signal_strength.value,
            'confidence': signal.confidence,
            'price': signal.price,
            'target_price': signal.target_price,
            'stop_loss': signal.stop_loss,
            'sentiment_score': signal.sentiment_score,
            'theme_score': signal.theme_score,
            'leader_score': signal.leader_score,
            'technical_score': signal.technical_score,
            'composite_score': signal.composite_score,
            'signal_sources': signal.signal_sources,
            'reason': signal.reason,
            'risk_level': signal.risk_level,
            'max_position_ratio': signal.max_position_ratio,
            'theme_name': signal.theme_name,
            'is_leader': signal.is_leader,
            'market_sentiment_phase': signal.market_sentiment_phase,
            'timestamp': signal.timestamp.isoformat(),
            'valid_until': signal.valid_until.isoformat()
        }

        return {
            "status": "success",
            "data": signal_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票{symbol}交易信号失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/signals/summary")
async def get_signals_summary():
    """获取交易信号摘要统计"""
    try:
        summary = signal_engine.get_signal_summary()

        return {
            "status": "success",
            "data": summary
        }

    except Exception as e:
        logger.error(f"获取信号摘要失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/positions/current")
async def get_current_positions():
    """获取当前持仓"""
    try:
        # 先尝试从缓存获取
        cached_positions = await cache_manager.get_positions()
        if cached_positions:
            return {
                "status": "success",
                "data": cached_positions,
                "source": "cache"
            }

        # 获取持仓摘要
        position_summary = position_manager.get_position_summary()

        # 获取详细持仓信息
        positions_data = []
        for symbol, position in position_manager.positions.items():
            if position.status.value == "active":
                position_dict = {
                    'symbol': position.symbol,
                    'quantity': position.quantity,
                    'avg_price': position.avg_price,
                    'current_price': position.current_price,
                    'market_value': position.market_value,
                    'cost_basis': position.cost_basis,
                    'unrealized_pnl': position.unrealized_pnl,
                    'unrealized_pnl_pct': position.unrealized_pnl_pct,
                    'weight': position.weight,
                    'buy_date': position.buy_date.isoformat(),
                    'holding_days': position.holding_days,
                    'stop_loss_price': position.stop_loss_price,
                    'take_profit_price': position.take_profit_price,
                    'theme_name': position.theme_name,
                    'is_leader': position.is_leader,
                    'last_updated': position.last_updated.isoformat()
                }
                positions_data.append(position_dict)

        # 缓存持仓数据
        await cache_manager.cache_positions(positions_data)

        return {
            "status": "success",
            "data": {
                "positions": positions_data,
                "summary": position_summary
            },
            "source": "realtime"
        }

    except Exception as e:
        logger.error(f"获取持仓数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/positions/metrics")
async def get_portfolio_metrics():
    """获取投资组合指标"""
    try:
        metrics = position_manager.get_portfolio_metrics()

        metrics_data = {
            'total_value': metrics.total_value,
            'total_cost': metrics.total_cost,
            'total_pnl': metrics.total_pnl,
            'total_pnl_pct': metrics.total_pnl_pct,
            'cash_balance': metrics.cash_balance,
            'position_count': metrics.position_count,
            'portfolio_var': metrics.portfolio_var,
            'max_drawdown': metrics.max_drawdown,
            'sharpe_ratio': metrics.sharpe_ratio,
            'volatility': metrics.volatility,
            'position_weights': metrics.position_weights,
            'theme_weights': metrics.theme_weights,
            'win_rate': metrics.win_rate,
            'avg_win_pct': metrics.avg_win_pct,
            'avg_loss_pct': metrics.avg_loss_pct,
            'profit_factor': metrics.profit_factor,
            'last_updated': metrics.last_updated.isoformat()
        }

        return {
            "status": "success",
            "data": metrics_data
        }

    except Exception as e:
        logger.error(f"获取投资组合指标失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/positions/{symbol}/open")
async def open_position(symbol: str, signal_data: dict):
    """开仓"""
    try:
        # 验证信号数据
        required_fields = ['signal_type', 'confidence', 'price']
        for field in required_fields:
            if field not in signal_data:
                raise HTTPException(status_code=400, detail=f"缺少必要字段: {field}")

        # 创建交易信号对象（简化）
        from backend.core.signal.signal_engine import TradingSignal, SignalType, SignalStrength

        signal = TradingSignal(
            symbol=symbol,
            signal_type=SignalType(signal_data['signal_type']),
            signal_strength=SignalStrength(signal_data.get('signal_strength', 'medium')),
            confidence=signal_data['confidence'],
            price=signal_data['price'],
            target_price=signal_data.get('target_price'),
            stop_loss=signal_data.get('stop_loss'),
            sentiment_score=signal_data.get('sentiment_score', 0.5),
            theme_score=signal_data.get('theme_score', 0.5),
            leader_score=signal_data.get('leader_score', 0.5),
            technical_score=signal_data.get('technical_score', 0.5),
            composite_score=signal_data['confidence'],
            signal_sources=signal_data.get('signal_sources', []),
            reason=signal_data.get('reason', '手动开仓'),
            risk_level=signal_data.get('risk_level', 'medium'),
            max_position_ratio=signal_data.get('max_position_ratio', 0.1),
            timestamp=datetime.now(),
            valid_until=datetime.now(),
            theme_name=signal_data.get('theme_name'),
            is_leader=signal_data.get('is_leader', False),
            market_sentiment_phase=signal_data.get('market_sentiment_phase', '发酵期')
        )

        # 执行开仓
        success = position_manager.open_position(signal, signal_data['price'])

        if success:
            # 保存持仓数据
            await position_manager.save_positions_to_file()

            return {
                "status": "success",
                "message": f"股票{symbol}开仓成功"
            }
        else:
            raise HTTPException(status_code=400, detail=f"股票{symbol}开仓失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"开仓失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/positions/{symbol}/close")
async def close_position(symbol: str, close_data: dict):
    """平仓"""
    try:
        current_price = close_data.get('current_price')
        reason = close_data.get('reason', '手动平仓')

        if not current_price:
            raise HTTPException(status_code=400, detail="缺少当前价格")

        # 执行平仓
        success = position_manager.close_position(symbol, current_price, reason)

        if success:
            # 保存持仓数据
            await position_manager.save_positions_to_file()

            return {
                "status": "success",
                "message": f"股票{symbol}平仓成功"
            }
        else:
            raise HTTPException(status_code=400, detail=f"股票{symbol}平仓失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"平仓失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/positions/risk-control")
async def execute_risk_control():
    """执行风险控制"""
    try:
        # 获取当前价格数据（简化实现）
        price_data = {}
        for symbol in position_manager.positions.keys():
            # 这里应该从数据管理器获取实时价格
            stock_data = await data_manager.get_stock_realtime_data(symbol)
            if stock_data:
                price_data[symbol] = stock_data.get('close', 0)

        # 执行风险控制
        executed_count = position_manager.execute_risk_controls(price_data)

        if executed_count > 0:
            # 保存持仓数据
            await position_manager.save_positions_to_file()

        return {
            "status": "success",
            "message": f"风险控制执行完成，处理{executed_count}个操作"
        }

    except Exception as e:
        logger.error(f"执行风险控制失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/health")
async def health_check():
    """系统健康检查"""
    try:
        # 检查缓存状态
        cache_health = await cache_manager.health_check()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "cache": cache_health,
                "file_storage": {"status": "healthy"},
                "data_manager": {"status": "healthy"}
            }
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
