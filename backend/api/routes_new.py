"""
API路由定义 - 新版本
"""
from fastapi import APIRouter, HTTPException
from typing import Dict, List, Optional
from datetime import datetime
import logging
from dataclasses import asdict

from backend.data.data_manager import data_manager
from backend.strategy import (
    sentiment_analyzer, theme_analyzer, leader_identifier,
    signal_generator, strategy_engine
)

logger = logging.getLogger(__name__)

# 创建API路由器
api_router = APIRouter()


@api_router.get("/")
async def api_root():
    """API根路径"""
    return {
        "message": "AIQuant7 API",
        "version": "1.0.0",
        "endpoints": {
            "market": "/market",
            "sentiment": "/sentiment", 
            "themes": "/themes",
            "leaders": "/leaders",
            "signals": "/signals",
            "positions": "/positions"
        }
    }


@api_router.get("/market/realtime")
async def get_realtime_market():
    """获取实时市场数据"""
    try:
        # 获取市场数据
        market_data = await data_manager.get_realtime_market_data()
        
        return {
            "status": "success",
            "data": market_data,
            "source": "realtime"
        }

    except Exception as e:
        logger.error(f"获取实时市场数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/market/stock/{symbol}/realtime")
async def get_stock_realtime_data(symbol: str):
    """获取单只股票实时数据"""
    try:
        stock_data = await data_manager.get_stock_realtime_data(symbol)
        
        if stock_data:
            return {
                "status": "success",
                "data": stock_data,
                "source": "realtime"
            }
        else:
            raise HTTPException(status_code=404, detail=f"无法获取股票{symbol}数据")
            
    except Exception as e:
        logger.error(f"获取股票{symbol}数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/market/news")
async def get_news_data(limit: int = 50):
    """获取新闻数据"""
    try:
        news_data = await data_manager.get_news_data(limit)
        
        return {
            "status": "success",
            "data": news_data,
            "count": len(news_data)
        }
        
    except Exception as e:
        logger.error(f"获取新闻数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/sentiment/current")
async def get_current_sentiment():
    """获取当前市场情绪"""
    try:
        # 获取市场数据
        market_data = await data_manager.get_realtime_market_data()
        
        # 分析市场情绪
        sentiment = sentiment_analyzer.analyze_current_sentiment(market_data)
        
        # 转换为字典格式
        result = asdict(sentiment)
        result['timestamp'] = datetime.now().isoformat()
        
        return {
            "status": "success",
            "data": result,
            "source": "realtime"
        }
            
    except Exception as e:
        logger.error(f"获取市场情绪失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/sentiment/history")
async def get_sentiment_history(days: int = 30):
    """获取历史情绪数据"""
    try:
        # 获取当前市场数据作为基准
        current_market_data = await data_manager.get_realtime_market_data()

        # 传递市场数据给sentiment_analyzer
        history_data = sentiment_analyzer.analyze_sentiment_history(days, current_market_data)

        return {
            "status": "success",
            "data": history_data,
            "count": len(history_data)
        }

    except Exception as e:
        logger.error(f"获取历史情绪数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/themes/ranking")
async def get_theme_ranking(limit: int = 20):
    """获取题材排行"""
    try:
        # 获取市场数据和新闻数据
        market_data = await data_manager.get_realtime_market_data()
        news_data = await data_manager.get_news_data(100)

        # 分析题材
        themes = theme_analyzer.analyze_themes(market_data, news_data)

        # 转换为字典格式
        theme_data = [asdict(theme) for theme in themes[:limit]]

        return {
            "status": "success",
            "data": theme_data,
            "source": "realtime",
            "count": len(theme_data)
        }

    except Exception as e:
        logger.error(f"获取题材排行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/themes/{theme_id}")
async def get_theme_detail(theme_id: str):
    """获取题材详细信息"""
    try:
        theme_detail = theme_analyzer.get_theme_detail(theme_id)
        
        if theme_detail:
            return {
                "status": "success",
                "data": asdict(theme_detail)
            }
        else:
            raise HTTPException(status_code=404, detail=f"未找到题材: {theme_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取题材详情失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/themes/{theme_id}/lifecycle")
async def get_theme_lifecycle(theme_id: str):
    """获取题材生命周期分析"""
    try:
        lifecycle_data = theme_analyzer.get_theme_lifecycle(theme_id)
        
        return {
            "status": "success",
            "data": lifecycle_data
        }

    except Exception as e:
        logger.error(f"获取题材生命周期失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/leaders/ranking")
async def get_leader_ranking(limit: int = 50):
    """获取龙头股排行榜"""
    try:
        # 获取市场数据
        market_data = await data_manager.get_realtime_market_data()
        
        # 识别龙头股
        leaders = leader_identifier.identify_leaders(market_data)

        # 转换为字典格式
        leader_data = [asdict(leader) for leader in leaders[:limit]]

        return {
            "status": "success",
            "data": leader_data,
            "count": len(leader_data)
        }

    except Exception as e:
        logger.error(f"获取龙头股排行失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/leaders/{symbol}/analysis")
async def get_leader_analysis(symbol: str):
    """获取龙头股分析"""
    try:
        analysis = leader_identifier.get_leader_analysis(symbol)
        
        return {
            "status": "success",
            "data": analysis
        }

    except Exception as e:
        logger.error(f"分析龙头股{symbol}失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/leaders/comparison")
async def compare_leaders(request: Dict[str, List[str]]):
    """比较多只龙头股"""
    try:
        symbols = request.get('symbols', [])
        
        if not symbols or len(symbols) < 2:
            raise HTTPException(status_code=400, detail="至少需要2只股票进行比较")

        comparison_result = leader_identifier.compare_leaders(symbols)

        return {
            "status": "success",
            "data": comparison_result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"比较龙头股失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/signals/latest")
async def get_latest_signals(limit: int = 100):
    """获取最新交易信号"""
    try:
        # 获取市场数据
        market_data = await data_manager.get_realtime_market_data()
        
        # 分析情绪、题材、龙头
        sentiment = sentiment_analyzer.analyze_current_sentiment(market_data)
        themes = theme_analyzer.analyze_themes(market_data)
        leaders = leader_identifier.identify_leaders(market_data)
        
        # 生成交易信号
        signals = signal_generator.generate_signals(
            asdict(sentiment),
            [asdict(t) for t in themes],
            [asdict(l) for l in leaders],
            market_data
        )

        # 转换为字典格式
        signal_data = [asdict(signal) for signal in signals[:limit]]

        return {
            "status": "success",
            "data": signal_data,
            "count": len(signal_data)
        }

    except Exception as e:
        logger.error(f"获取交易信号失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/signals/{symbol}")
async def get_stock_signal(symbol: str):
    """获取单只股票的交易信号"""
    try:
        # 获取股票实时数据
        stock_data = await data_manager.get_stock_realtime_data(symbol)
        
        if not stock_data:
            raise HTTPException(status_code=404, detail=f"无法获取股票{symbol}数据")

        # 这里可以基于股票数据生成特定信号
        # 暂时返回模拟信号
        signal = {
            "symbol": symbol,
            "signal_type": "hold",
            "confidence": 60.0,
            "reason": "数据分析中",
            "timestamp": datetime.now().isoformat()
        }

        return {
            "status": "success",
            "data": signal
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票{symbol}信号失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/signals/summary")
async def get_signal_summary():
    """获取信号汇总"""
    try:
        # 获取最新信号
        market_data = await data_manager.get_realtime_market_data()
        sentiment = sentiment_analyzer.analyze_current_sentiment(market_data)
        themes = theme_analyzer.analyze_themes(market_data)
        leaders = leader_identifier.identify_leaders(market_data)
        
        signals = signal_generator.generate_signals(
            asdict(sentiment),
            [asdict(t) for t in themes],
            [asdict(l) for l in leaders],
            market_data
        )

        # 生成汇总
        summary = signal_generator.get_signal_summary(signals)

        return {
            "status": "success",
            "data": summary
        }

    except Exception as e:
        logger.error(f"获取信号汇总失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/positions/current")
async def get_current_positions():
    """获取当前持仓"""
    try:
        # 返回模拟持仓数据
        positions = [
            {
                "position_id": "1",
                "symbol": "300750",
                "name": "宁德时代",
                "quantity": 1000,
                "avg_cost": 220.50,
                "current_price": 245.80,
                "market_value": 245800,
                "pnl": 25300,
                "pnl_pct": 11.47,
                "open_date": "2024-12-01"
            }
        ]

        return {
            "status": "success",
            "data": positions,
            "count": len(positions)
        }

    except Exception as e:
        logger.error(f"获取当前持仓失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/positions/metrics")
async def get_portfolio_metrics():
    """获取投资组合指标"""
    try:
        # 返回模拟组合指标
        metrics = {
            "total_value": 1000000,
            "total_cost": 950000,
            "total_pnl": 50000,
            "total_pnl_pct": 5.26,
            "cash_balance": 614000,
            "position_count": 2,
            "max_drawdown": -8.5,
            "sharpe_ratio": 1.25,
            "win_rate": 68.5
        }

        return {
            "status": "success",
            "data": metrics
        }

    except Exception as e:
        logger.error(f"获取投资组合指标失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
