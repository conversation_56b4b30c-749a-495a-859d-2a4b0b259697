"""
数据库连接和管理
"""
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import String, DateTime, Float, Integer, Text, Boolean, JSO<PERSON>
from datetime import datetime
from typing import Optional, AsyncGenerator
from loguru import logger

from config.settings import settings


class Base(DeclarativeBase):
    """数据库模型基类"""
    pass


class MarketData(Base):
    """市场数据表"""
    __tablename__ = "market_data"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    symbol: Mapped[str] = mapped_column(String(20), nullable=False, index=True)
    timestamp: Mapped[datetime] = mapped_column(DateTime, nullable=False, index=True)
    open: Mapped[float] = mapped_column(Float, nullable=False)
    high: Mapped[float] = mapped_column(Float, nullable=False)
    low: Mapped[float] = mapped_column(Float, nullable=False)
    close: Mapped[float] = mapped_column(Float, nullable=False)
    volume: Mapped[float] = mapped_column(Float, nullable=False)
    amount: Mapped[float] = mapped_column(Float, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)


class SentimentData(Base):
    """市场情绪数据表"""
    __tablename__ = "sentiment_data"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    date: Mapped[datetime] = mapped_column(DateTime, nullable=False, index=True)
    limit_up_count: Mapped[int] = mapped_column(Integer, nullable=False)
    limit_down_count: Mapped[int] = mapped_column(Integer, nullable=False)
    max_continuous_boards: Mapped[int] = mapped_column(Integer, nullable=False)
    sentiment_score: Mapped[float] = mapped_column(Float, nullable=False)
    sentiment_phase: Mapped[str] = mapped_column(String(20), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)


class ThemeData(Base):
    """题材数据表"""
    __tablename__ = "theme_data"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    theme_name: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    theme_type: Mapped[str] = mapped_column(String(20), nullable=False)  # policy, industry, event, concept
    policy_factor: Mapped[float] = mapped_column(Float, default=0.0)
    industry_factor: Mapped[float] = mapped_column(Float, default=0.0)
    event_factor: Mapped[float] = mapped_column(Float, default=0.0)
    concept_factor: Mapped[float] = mapped_column(Float, default=0.0)
    theme_score: Mapped[float] = mapped_column(Float, nullable=False)
    lifecycle_stage: Mapped[str] = mapped_column(String(20), nullable=False)
    related_stocks: Mapped[Optional[str]] = mapped_column(Text)  # JSON格式存储相关股票
    news_count: Mapped[int] = mapped_column(Integer, default=0)
    date: Mapped[datetime] = mapped_column(DateTime, nullable=False, index=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)


class StockData(Base):
    """股票基础数据表"""
    __tablename__ = "stock_data"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    symbol: Mapped[str] = mapped_column(String(20), nullable=False, unique=True, index=True)
    name: Mapped[str] = mapped_column(String(50), nullable=False)
    industry: Mapped[Optional[str]] = mapped_column(String(50))
    market: Mapped[str] = mapped_column(String(10), nullable=False)  # 主板、创业板、科创板等
    list_date: Mapped[Optional[datetime]] = mapped_column(DateTime)
    market_cap: Mapped[Optional[float]] = mapped_column(Float)  # 流通市值
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class TradingSignal(Base):
    """交易信号表"""
    __tablename__ = "trading_signals"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    symbol: Mapped[str] = mapped_column(String(20), nullable=False, index=True)
    signal_type: Mapped[str] = mapped_column(String(10), nullable=False)  # buy, sell, hold
    strength: Mapped[float] = mapped_column(Float, nullable=False)  # 信号强度 0-1
    price: Mapped[float] = mapped_column(Float, nullable=False)
    reason: Mapped[str] = mapped_column(Text, nullable=False)
    sentiment_score: Mapped[Optional[float]] = mapped_column(Float)
    theme_score: Mapped[Optional[float]] = mapped_column(Float)
    technical_score: Mapped[Optional[float]] = mapped_column(Float)
    timestamp: Mapped[datetime] = mapped_column(DateTime, nullable=False, index=True)
    is_executed: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)


class Position(Base):
    """持仓数据表"""
    __tablename__ = "positions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    symbol: Mapped[str] = mapped_column(String(20), nullable=False, index=True)
    quantity: Mapped[float] = mapped_column(Float, nullable=False)
    avg_price: Mapped[float] = mapped_column(Float, nullable=False)
    current_price: Mapped[float] = mapped_column(Float, nullable=False)
    market_value: Mapped[float] = mapped_column(Float, nullable=False)
    unrealized_pnl: Mapped[float] = mapped_column(Float, nullable=False)
    weight: Mapped[float] = mapped_column(Float, nullable=False)  # 仓位权重
    buy_date: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    holding_days: Mapped[int] = mapped_column(Integer, default=0)
    theme_name: Mapped[Optional[str]] = mapped_column(String(100))
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


# 数据库引擎和会话
engine = None
async_session_maker = None


async def init_database():
    """初始化数据库连接"""
    global engine, async_session_maker
    
    try:
        # 创建异步引擎
        engine = create_async_engine(
            settings.database.postgres_url,
            echo=settings.debug,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600,
        )
        
        # 创建会话工厂
        async_session_maker = async_sessionmaker(
            engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # 创建表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("数据库初始化成功")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话"""
    if async_session_maker is None:
        raise RuntimeError("数据库未初始化")
    
    async with async_session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def close_database():
    """关闭数据库连接"""
    global engine
    if engine:
        await engine.dispose()
        logger.info("数据库连接已关闭")
