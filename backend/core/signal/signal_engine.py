"""
交易信号生成引擎
综合市场情绪、题材热度、龙头股识别和技术分析，生成高质量交易信号
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from loguru import logger
from dataclasses import dataclass
from enum import Enum

from config.settings import settings
from backend.core.sentiment.sentiment_engine import sentiment_engine
from backend.core.theme.theme_engine import theme_engine
from backend.core.leader.leader_engine import leader_engine
from backend.core.technical.technical_engine import technical_engine


class SignalType(Enum):
    """信号类型枚举"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"


class SignalStrength(Enum):
    """信号强度枚举"""
    WEAK = "weak"
    MEDIUM = "medium"
    STRONG = "strong"
    VERY_STRONG = "very_strong"


@dataclass
class TradingSignal:
    """交易信号数据结构"""
    symbol: str
    signal_type: SignalType
    signal_strength: SignalStrength
    confidence: float
    price: float
    target_price: Optional[float]
    stop_loss: Optional[float]
    
    # 各模块评分
    sentiment_score: float
    theme_score: float
    leader_score: float
    technical_score: float
    
    # 综合评分
    composite_score: float
    
    # 信号来源和原因
    signal_sources: List[str]
    reason: str
    
    # 风险评估
    risk_level: str  # low, medium, high
    max_position_ratio: float
    
    # 时间信息
    timestamp: datetime
    valid_until: datetime
    
    # 额外信息
    theme_name: Optional[str]
    is_leader: bool
    market_sentiment_phase: str


class TradingSignalEngine:
    """交易信号生成引擎"""
    
    def __init__(self):
        # 各模块权重配置
        self.module_weights = {
            'sentiment': 0.25,    # 市场情绪权重
            'theme': 0.30,        # 题材热度权重
            'leader': 0.25,       # 龙头股权重
            'technical': 0.20     # 技术分析权重
        }
        
        # 信号强度阈值
        self.strength_thresholds = {
            'weak': 0.4,
            'medium': 0.6,
            'strong': 0.8,
            'very_strong': 0.9
        }
        
        # 风险等级阈值
        self.risk_thresholds = {
            'low': 0.3,
            'medium': 0.6,
            'high': 1.0
        }
        
        # 信号有效期（小时）
        self.signal_validity_hours = 4
        
        # 历史信号记录
        self.signal_history: List[TradingSignal] = []
        
        logger.info("交易信号生成引擎初始化完成")
    
    def generate_comprehensive_signal(self, symbol: str, market_data: Dict, 
                                    stock_data: pd.DataFrame) -> Optional[TradingSignal]:
        """
        生成综合交易信号
        
        Args:
            symbol: 股票代码
            market_data: 市场数据
            stock_data: 股票历史数据
            
        Returns:
            TradingSignal: 交易信号对象
        """
        try:
            if stock_data.empty:
                logger.warning(f"股票{symbol}数据为空，无法生成信号")
                return None
            
            current_price = stock_data['close'].iloc[-1]
            
            # 1. 获取市场情绪分析
            sentiment_data = sentiment_engine.process_market_data(market_data)
            sentiment_score = self._normalize_sentiment_score(sentiment_data)
            
            # 2. 获取题材分析
            theme_score, theme_name = self._analyze_theme_relevance(symbol)
            
            # 3. 获取龙头股分析
            leader_score, is_leader = self._analyze_leader_status(symbol, theme_name)
            
            # 4. 获取技术分析
            technical_indicators = technical_engine.calculate_technical_indicators(stock_data)
            technical_score = technical_indicators.get('technical_score', 0.0)
            
            # 5. 计算综合评分
            composite_score = self._calculate_composite_score(
                sentiment_score, theme_score, leader_score, technical_score
            )
            
            # 6. 确定信号类型和强度
            signal_type, signal_strength = self._determine_signal_type_and_strength(
                composite_score, sentiment_data, technical_indicators
            )
            
            # 7. 计算目标价和止损价
            target_price, stop_loss = self._calculate_price_targets(
                current_price, signal_type, technical_indicators, composite_score
            )
            
            # 8. 评估风险和仓位
            risk_level, max_position_ratio = self._assess_risk_and_position(
                sentiment_data, theme_score, leader_score, technical_score
            )
            
            # 9. 生成信号原因说明
            signal_sources, reason = self._generate_signal_explanation(
                sentiment_score, theme_score, leader_score, technical_score,
                sentiment_data, theme_name, is_leader
            )
            
            # 10. 创建交易信号对象
            trading_signal = TradingSignal(
                symbol=symbol,
                signal_type=signal_type,
                signal_strength=signal_strength,
                confidence=composite_score,
                price=current_price,
                target_price=target_price,
                stop_loss=stop_loss,
                sentiment_score=sentiment_score,
                theme_score=theme_score,
                leader_score=leader_score,
                technical_score=technical_score,
                composite_score=composite_score,
                signal_sources=signal_sources,
                reason=reason,
                risk_level=risk_level,
                max_position_ratio=max_position_ratio,
                timestamp=datetime.now(),
                valid_until=datetime.now() + timedelta(hours=self.signal_validity_hours),
                theme_name=theme_name,
                is_leader=is_leader,
                market_sentiment_phase=sentiment_data.sentiment_phase
            )
            
            # 记录信号历史
            self.signal_history.append(trading_signal)
            
            logger.info(f"生成{symbol}交易信号: {signal_type.value}, 强度: {signal_strength.value}, "
                       f"置信度: {composite_score:.3f}")
            
            return trading_signal
            
        except Exception as e:
            logger.error(f"生成{symbol}交易信号失败: {e}")
            return None
    
    def _normalize_sentiment_score(self, sentiment_data) -> float:
        """标准化情绪评分到0-1范围"""
        try:
            # 基于情绪周期给分
            phase_scores = {
                "冰点期": 0.2,
                "回暖期": 0.7,
                "发酵期": 0.9,
                "高潮期": 0.6,  # 高潮期风险较高，适当降分
                "退潮期": 0.3,
                "混沌期": 0.1
            }
            
            base_score = phase_scores.get(sentiment_data.sentiment_phase, 0.5)
            
            # 根据市场温度调整
            temp_adjustment = (sentiment_data.market_temperature - 50) / 100
            
            # 根据趋势方向调整
            trend_adjustment = 0.0
            if sentiment_data.trend_direction == "上升":
                trend_adjustment = 0.1
            elif sentiment_data.trend_direction == "下降":
                trend_adjustment = -0.1
            
            final_score = base_score + temp_adjustment + trend_adjustment
            return max(0.0, min(1.0, final_score))
            
        except Exception as e:
            logger.error(f"标准化情绪评分失败: {e}")
            return 0.5
    
    def _analyze_theme_relevance(self, symbol: str) -> Tuple[float, Optional[str]]:
        """分析股票的题材相关性"""
        try:
            # 获取当前热门题材
            themes = theme_engine.get_theme_ranking(20)
            
            if not themes:
                return 0.0, None
            
            # 查找股票相关的题材
            best_theme_score = 0.0
            best_theme_name = None
            
            for theme in themes:
                theme_name = theme.get('theme_name', '')
                theme_score = theme.get('theme_score', 0.0)
                
                # 这里应该检查股票是否属于该题材
                # 简化实现：基于题材评分
                if theme_score > best_theme_score:
                    best_theme_score = theme_score
                    best_theme_name = theme_name
            
            # 标准化题材评分
            normalized_score = min(best_theme_score / 10.0, 1.0) if best_theme_score > 0 else 0.0
            
            return normalized_score, best_theme_name
            
        except Exception as e:
            logger.error(f"分析题材相关性失败: {e}")
            return 0.0, None
    
    def _analyze_leader_status(self, symbol: str, theme_name: Optional[str]) -> Tuple[float, bool]:
        """分析股票的龙头地位"""
        try:
            if not theme_name:
                return 0.0, False
            
            # 获取题材龙头摘要
            summary = leader_engine.get_theme_leader_summary(theme_name)
            
            if summary.get("status") != "success":
                return 0.0, False
            
            # 检查是否为龙头股
            leaders = leader_engine.get_leader_ranking(theme_name=theme_name)
            
            for leader in leaders:
                if leader['symbol'] == symbol:
                    leader_score = leader['leader_score']
                    is_leader = leader_score >= 0.4  # 龙头阈值
                    return leader_score, is_leader
            
            return 0.0, False
            
        except Exception as e:
            logger.error(f"分析龙头地位失败: {e}")
            return 0.0, False

    def _calculate_composite_score(self, sentiment_score: float, theme_score: float,
                                 leader_score: float, technical_score: float) -> float:
        """计算综合评分"""
        try:
            composite_score = (
                sentiment_score * self.module_weights['sentiment'] +
                theme_score * self.module_weights['theme'] +
                leader_score * self.module_weights['leader'] +
                technical_score * self.module_weights['technical']
            )

            return max(0.0, min(1.0, composite_score))

        except Exception as e:
            logger.error(f"计算综合评分失败: {e}")
            return 0.0

    def _determine_signal_type_and_strength(self, composite_score: float,
                                          sentiment_data, technical_indicators: Dict) -> Tuple[SignalType, SignalStrength]:
        """确定信号类型和强度"""
        try:
            # 基于综合评分确定信号类型
            if composite_score >= 0.6:
                signal_type = SignalType.BUY
            elif composite_score <= 0.3:
                signal_type = SignalType.SELL
            else:
                signal_type = SignalType.HOLD

            # 确定信号强度
            if composite_score >= self.strength_thresholds['very_strong']:
                strength = SignalStrength.VERY_STRONG
            elif composite_score >= self.strength_thresholds['strong']:
                strength = SignalStrength.STRONG
            elif composite_score >= self.strength_thresholds['medium']:
                strength = SignalStrength.MEDIUM
            else:
                strength = SignalStrength.WEAK

            # 特殊情况调整
            if sentiment_data.sentiment_phase in ["高潮期", "退潮期"]:
                # 高潮期和退潮期降低买入信号强度
                if signal_type == SignalType.BUY:
                    if strength == SignalStrength.VERY_STRONG:
                        strength = SignalStrength.STRONG
                    elif strength == SignalStrength.STRONG:
                        strength = SignalStrength.MEDIUM

            return signal_type, strength

        except Exception as e:
            logger.error(f"确定信号类型和强度失败: {e}")
            return SignalType.HOLD, SignalStrength.WEAK

    def _calculate_price_targets(self, current_price: float, signal_type: SignalType,
                               technical_indicators: Dict, composite_score: float) -> Tuple[Optional[float], Optional[float]]:
        """计算目标价和止损价"""
        try:
            if signal_type == SignalType.HOLD:
                return None, None

            # 基于技术指标计算基础目标
            base_target_ratio = 0.05 + composite_score * 0.10  # 5%-15%的目标收益
            base_stop_ratio = 0.03 + (1 - composite_score) * 0.05  # 3%-8%的止损

            if signal_type == SignalType.BUY:
                target_price = current_price * (1 + base_target_ratio)
                stop_loss = current_price * (1 - base_stop_ratio)

                # 基于技术指标调整
                if technical_indicators.get('bb_upper'):
                    # 如果有布林带上轨，可以作为目标参考
                    bb_target = technical_indicators['bb_upper']
                    if bb_target > target_price:
                        target_price = min(bb_target, current_price * 1.20)  # 最大20%涨幅

                if technical_indicators.get('ma20'):
                    # MA20作为止损参考
                    ma20_stop = technical_indicators['ma20']
                    if ma20_stop < current_price:
                        stop_loss = max(stop_loss, ma20_stop)

            else:  # SELL
                target_price = current_price * (1 - base_target_ratio)
                stop_loss = current_price * (1 + base_stop_ratio)

                # 基于技术指标调整
                if technical_indicators.get('bb_lower'):
                    bb_target = technical_indicators['bb_lower']
                    if bb_target < target_price:
                        target_price = max(bb_target, current_price * 0.80)  # 最大20%跌幅

            return target_price, stop_loss

        except Exception as e:
            logger.error(f"计算价格目标失败: {e}")
            return None, None

    def _assess_risk_and_position(self, sentiment_data, theme_score: float,
                                leader_score: float, technical_score: float) -> Tuple[str, float]:
        """评估风险等级和最大仓位比例"""
        try:
            # 计算风险评分
            risk_factors = []

            # 市场情绪风险
            if sentiment_data.sentiment_phase in ["高潮期", "退潮期"]:
                risk_factors.append(0.8)
            elif sentiment_data.sentiment_phase in ["冰点期", "混沌期"]:
                risk_factors.append(0.6)
            else:
                risk_factors.append(0.3)

            # 题材风险（题材过热风险）
            if theme_score > 0.8:
                risk_factors.append(0.7)
            elif theme_score < 0.3:
                risk_factors.append(0.5)
            else:
                risk_factors.append(0.2)

            # 技术风险
            if technical_score < 0.3:
                risk_factors.append(0.6)
            else:
                risk_factors.append(0.2)

            # 波动率风险
            if sentiment_data.volatility_index > 60:
                risk_factors.append(0.7)
            else:
                risk_factors.append(0.3)

            # 综合风险评分
            avg_risk = np.mean(risk_factors)

            # 确定风险等级
            if avg_risk >= self.risk_thresholds['high']:
                risk_level = "high"
                max_position = 0.05  # 高风险最大5%仓位
            elif avg_risk >= self.risk_thresholds['medium']:
                risk_level = "medium"
                max_position = 0.10  # 中风险最大10%仓位
            else:
                risk_level = "low"
                max_position = 0.15  # 低风险最大15%仓位

            # 龙头股可以适当增加仓位
            if leader_score > 0.7:
                max_position *= 1.2

            # 确保不超过配置的最大单票仓位
            max_single_position = settings.strategy.position_limits['max_single_position']
            max_position = min(max_position, max_single_position)

            return risk_level, max_position

        except Exception as e:
            logger.error(f"评估风险和仓位失败: {e}")
            return "medium", 0.08

    def _generate_signal_explanation(self, sentiment_score: float, theme_score: float,
                                   leader_score: float, technical_score: float,
                                   sentiment_data, theme_name: Optional[str], is_leader: bool) -> Tuple[List[str], str]:
        """生成信号来源和原因说明"""
        try:
            sources = []
            reasons = []

            # 情绪因素
            if sentiment_score > 0.6:
                sources.append("market_sentiment")
                reasons.append(f"市场情绪{sentiment_data.sentiment_phase}，温度{sentiment_data.market_temperature:.0f}")

            # 题材因素
            if theme_score > 0.5 and theme_name:
                sources.append("theme_hotspot")
                reasons.append(f"热点题材'{theme_name}'，评分{theme_score:.2f}")

            # 龙头因素
            if is_leader and leader_score > 0.6:
                sources.append("leader_stock")
                reasons.append(f"题材龙头股，龙头评分{leader_score:.2f}")

            # 技术因素
            if technical_score > 0.6:
                sources.append("technical_analysis")
                reasons.append(f"技术面良好，技术评分{technical_score:.2f}")

            # 组合原因说明
            if not reasons:
                reason = "综合评分偏低，建议观望"
            else:
                reason = "；".join(reasons)

            return sources, reason

        except Exception as e:
            logger.error(f"生成信号说明失败: {e}")
            return [], "信号生成异常"

    def batch_generate_signals(self, symbol_list: List[str], market_data: Dict) -> List[TradingSignal]:
        """
        批量生成交易信号

        Args:
            symbol_list: 股票代码列表
            market_data: 市场数据

        Returns:
            List[TradingSignal]: 交易信号列表
        """
        try:
            signals = []

            for symbol in symbol_list:
                try:
                    # 获取股票历史数据（这里应该从数据管理器获取）
                    # 简化实现：创建模拟数据
                    stock_data = self._get_stock_data(symbol)

                    if stock_data is not None and not stock_data.empty:
                        signal = self.generate_comprehensive_signal(symbol, market_data, stock_data)
                        if signal:
                            signals.append(signal)

                except Exception as e:
                    logger.error(f"生成{symbol}信号失败: {e}")
                    continue

            # 按综合评分排序
            signals.sort(key=lambda x: x.composite_score, reverse=True)

            logger.info(f"批量生成信号完成，共{len(signals)}个有效信号")
            return signals

        except Exception as e:
            logger.error(f"批量生成信号失败: {e}")
            return []

    def _get_stock_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """获取股票数据（简化实现）"""
        try:
            # 这里应该调用数据管理器获取真实数据
            # 简化实现：返回模拟数据
            dates = pd.date_range(start='2023-01-01', periods=30, freq='D')
            np.random.seed(hash(symbol) % 2**32)  # 基于股票代码的固定随机种子

            base_price = 50 + (hash(symbol) % 100)
            prices = [base_price]

            for _ in range(29):
                change = np.random.normal(0, 0.02)  # 2%日波动
                new_price = prices[-1] * (1 + change)
                prices.append(max(new_price, 1.0))  # 确保价格为正

            return pd.DataFrame({
                'date': dates,
                'open': [p * 0.99 for p in prices],
                'high': [p * 1.02 for p in prices],
                'low': [p * 0.98 for p in prices],
                'close': prices,
                'volume': np.random.randint(1000000, 5000000, 30),
                'amount': [p * v for p, v in zip(prices, np.random.randint(1000000, 5000000, 30))],
                'symbol': [symbol] * 30
            })

        except Exception as e:
            logger.error(f"获取{symbol}数据失败: {e}")
            return None

    def filter_signals_by_criteria(self, signals: List[TradingSignal],
                                 min_confidence: float = 0.6,
                                 signal_types: List[SignalType] = None,
                                 max_risk_level: str = "medium") -> List[TradingSignal]:
        """
        根据条件过滤信号

        Args:
            signals: 信号列表
            min_confidence: 最小置信度
            signal_types: 允许的信号类型
            max_risk_level: 最大风险等级

        Returns:
            List[TradingSignal]: 过滤后的信号列表
        """
        try:
            if signal_types is None:
                signal_types = [SignalType.BUY, SignalType.SELL]

            risk_order = {"low": 1, "medium": 2, "high": 3}
            max_risk_value = risk_order.get(max_risk_level, 2)

            filtered_signals = []

            for signal in signals:
                # 置信度过滤
                if signal.confidence < min_confidence:
                    continue

                # 信号类型过滤
                if signal.signal_type not in signal_types:
                    continue

                # 风险等级过滤
                signal_risk_value = risk_order.get(signal.risk_level, 3)
                if signal_risk_value > max_risk_value:
                    continue

                # 信号有效性检查
                if signal.valid_until < datetime.now():
                    continue

                filtered_signals.append(signal)

            logger.info(f"信号过滤完成：{len(signals)} -> {len(filtered_signals)}")
            return filtered_signals

        except Exception as e:
            logger.error(f"过滤信号失败: {e}")
            return signals

    def get_signal_summary(self) -> Dict:
        """获取信号摘要统计"""
        try:
            if not self.signal_history:
                return {
                    "total_signals": 0,
                    "signal_distribution": {},
                    "average_confidence": 0.0,
                    "risk_distribution": {}
                }

            # 统计信号分布
            signal_counts = {}
            risk_counts = {}
            confidences = []

            for signal in self.signal_history:
                # 信号类型分布
                signal_type = signal.signal_type.value
                signal_counts[signal_type] = signal_counts.get(signal_type, 0) + 1

                # 风险分布
                risk_level = signal.risk_level
                risk_counts[risk_level] = risk_counts.get(risk_level, 0) + 1

                # 置信度
                confidences.append(signal.confidence)

            return {
                "total_signals": len(self.signal_history),
                "signal_distribution": signal_counts,
                "average_confidence": np.mean(confidences),
                "risk_distribution": risk_counts,
                "latest_signal_time": self.signal_history[-1].timestamp.isoformat() if self.signal_history else None
            }

        except Exception as e:
            logger.error(f"获取信号摘要失败: {e}")
            return {}

    async def save_signals_to_file(self, signals: List[TradingSignal], date_str: str = None):
        """保存信号到文件"""
        try:
            from backend.data.file_storage import file_storage

            if not signals:
                return False

            if date_str is None:
                date_str = datetime.now().strftime("%Y%m%d")

            # 转换为DataFrame
            signal_records = []
            for signal in signals:
                record = {
                    'symbol': signal.symbol,
                    'signal_type': signal.signal_type.value,
                    'signal_strength': signal.signal_strength.value,
                    'confidence': signal.confidence,
                    'price': signal.price,
                    'target_price': signal.target_price,
                    'stop_loss': signal.stop_loss,
                    'sentiment_score': signal.sentiment_score,
                    'theme_score': signal.theme_score,
                    'leader_score': signal.leader_score,
                    'technical_score': signal.technical_score,
                    'composite_score': signal.composite_score,
                    'signal_sources': ','.join(signal.signal_sources),
                    'reason': signal.reason,
                    'risk_level': signal.risk_level,
                    'max_position_ratio': signal.max_position_ratio,
                    'timestamp': signal.timestamp,
                    'valid_until': signal.valid_until,
                    'theme_name': signal.theme_name,
                    'is_leader': signal.is_leader,
                    'market_sentiment_phase': signal.market_sentiment_phase
                }
                signal_records.append(record)

            df = pd.DataFrame(signal_records)

            # 保存到文件
            success = await file_storage.save_signal_data(df, date_str)

            if success:
                logger.info(f"信号数据已保存: {date_str}, 共{len(signals)}个信号")
            else:
                logger.error(f"保存信号数据失败: {date_str}")

            return success

        except Exception as e:
            logger.error(f"保存信号到文件失败: {e}")
            return False


# 全局交易信号引擎实例
signal_engine = TradingSignalEngine()
