"""
仓位管理系统
包含动态仓位计算、风险控制和资金管理
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from loguru import logger
from dataclasses import dataclass
from enum import Enum

from config.settings import settings
from backend.core.sentiment.sentiment_engine import sentiment_engine
from backend.core.signal.signal_engine import TradingSignal, SignalType


class PositionStatus(Enum):
    """持仓状态枚举"""
    ACTIVE = "active"
    CLOSED = "closed"
    SUSPENDED = "suspended"


@dataclass
class Position:
    """持仓数据结构"""
    symbol: str
    quantity: float
    avg_price: float
    current_price: float
    market_value: float
    cost_basis: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    weight: float  # 仓位权重
    
    # 交易信息
    buy_date: datetime
    holding_days: int
    buy_signal: Optional[TradingSignal]
    
    # 风险控制
    stop_loss_price: Optional[float]
    take_profit_price: Optional[float]
    max_loss_pct: float
    
    # 题材信息
    theme_name: Optional[str]
    is_leader: bool
    
    # 状态信息
    status: PositionStatus
    last_updated: datetime
    
    # 风险指标
    var_1d: Optional[float]  # 1日风险价值
    max_drawdown: float
    sharpe_ratio: Optional[float]


@dataclass
class PortfolioMetrics:
    """投资组合指标"""
    total_value: float
    total_cost: float
    total_pnl: float
    total_pnl_pct: float
    cash_balance: float
    position_count: int
    
    # 风险指标
    portfolio_var: float
    max_drawdown: float
    sharpe_ratio: float
    volatility: float
    
    # 仓位分布
    position_weights: Dict[str, float]
    theme_weights: Dict[str, float]
    
    # 绩效指标
    win_rate: float
    avg_win_pct: float
    avg_loss_pct: float
    profit_factor: float
    
    last_updated: datetime


class PositionManager:
    """仓位管理器"""
    
    def __init__(self, initial_capital: float = 1000000.0):
        # 初始资金
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.cash_balance = initial_capital
        
        # 持仓记录
        self.positions: Dict[str, Position] = {}
        self.closed_positions: List[Position] = []
        
        # 风险控制参数
        self.risk_params = settings.strategy.risk_params
        self.position_limits = settings.strategy.position_limits
        
        # 绩效记录
        self.daily_returns: List[float] = []
        self.daily_values: List[float] = [initial_capital]
        self.trade_history: List[Dict] = []
        
        logger.info(f"仓位管理器初始化完成，初始资金: {initial_capital:,.2f}")
    
    def calculate_position_size(self, signal: TradingSignal, current_price: float) -> float:
        """
        计算建仓数量
        
        Args:
            signal: 交易信号
            current_price: 当前价格
            
        Returns:
            float: 建仓数量
        """
        try:
            if signal.signal_type != SignalType.BUY:
                return 0.0
            
            # 基础仓位计算
            base_position_value = self._calculate_base_position_value(signal)
            
            # 风险调整
            risk_adjusted_value = self._apply_risk_adjustment(base_position_value, signal)
            
            # 流动性调整
            liquidity_adjusted_value = self._apply_liquidity_adjustment(risk_adjusted_value, signal.symbol)
            
            # 相关性调整
            correlation_adjusted_value = self._apply_correlation_adjustment(
                liquidity_adjusted_value, signal.theme_name
            )
            
            # 转换为股数
            quantity = correlation_adjusted_value / current_price
            
            # 确保为整数股（A股最小交易单位100股）
            quantity = int(quantity / 100) * 100
            
            logger.debug(f"计算{signal.symbol}仓位: 基础{base_position_value:,.0f} -> "
                        f"风险调整{risk_adjusted_value:,.0f} -> "
                        f"最终{quantity}股")
            
            return quantity
            
        except Exception as e:
            logger.error(f"计算仓位大小失败: {e}")
            return 0.0
    
    def _calculate_base_position_value(self, signal: TradingSignal) -> float:
        """计算基础仓位价值"""
        try:
            # 基于信号强度和置信度计算基础仓位
            confidence_factor = signal.confidence
            strength_factors = {
                'weak': 0.5,
                'medium': 0.7,
                'strong': 1.0,
                'very_strong': 1.2
            }
            strength_factor = strength_factors.get(signal.signal_strength.value, 0.7)
            
            # 基础仓位比例
            base_ratio = signal.max_position_ratio * confidence_factor * strength_factor
            
            # 基于市场情绪调整
            sentiment_summary = sentiment_engine.get_sentiment_summary()
            if sentiment_summary.get("status") == "success":
                current_sentiment = sentiment_summary["current"]
                phase = current_sentiment.get("sentiment_phase", "混沌期")
                
                # 根据情绪周期调整仓位
                sentiment_limits = settings.strategy.sentiment_position_limits
                max_sentiment_ratio = sentiment_limits.get(phase, 0.5)
                base_ratio = min(base_ratio, max_sentiment_ratio)
            
            # 计算仓位价值
            position_value = self.current_capital * base_ratio
            
            # 确保不超过可用资金
            position_value = min(position_value, self.cash_balance * 0.95)  # 保留5%现金
            
            return position_value
            
        except Exception as e:
            logger.error(f"计算基础仓位价值失败: {e}")
            return 0.0
    
    def _apply_risk_adjustment(self, position_value: float, signal: TradingSignal) -> float:
        """应用风险调整"""
        try:
            # 基于风险等级调整
            risk_factors = {
                'low': 1.0,
                'medium': 0.8,
                'high': 0.6
            }
            risk_factor = risk_factors.get(signal.risk_level, 0.8)
            
            # 基于止损距离调整
            if signal.stop_loss and signal.price:
                stop_loss_pct = abs(signal.price - signal.stop_loss) / signal.price
                if stop_loss_pct > 0.08:  # 止损距离超过8%，减少仓位
                    risk_factor *= 0.7
                elif stop_loss_pct < 0.03:  # 止损距离小于3%，可能增加仓位
                    risk_factor *= 1.1
            
            return position_value * risk_factor
            
        except Exception as e:
            logger.error(f"应用风险调整失败: {e}")
            return position_value
    
    def _apply_liquidity_adjustment(self, position_value: float, symbol: str) -> float:
        """应用流动性调整"""
        try:
            # 简化实现：基于股票代码判断流动性
            # 实际应用中应该基于成交量、换手率等指标
            
            # 主板大盘股流动性好
            if symbol.startswith('60') or symbol.startswith('00'):
                liquidity_factor = 1.0
            # 创业板和科创板流动性一般
            elif symbol.startswith('30') or symbol.startswith('68'):
                liquidity_factor = 0.9
            else:
                liquidity_factor = 0.8
            
            return position_value * liquidity_factor
            
        except Exception as e:
            logger.error(f"应用流动性调整失败: {e}")
            return position_value
    
    def _apply_correlation_adjustment(self, position_value: float, theme_name: Optional[str]) -> float:
        """应用相关性调整（同题材仓位限制）"""
        try:
            if not theme_name:
                return position_value
            
            # 计算同题材当前仓位
            theme_positions = [pos for pos in self.positions.values() 
                             if pos.theme_name == theme_name and pos.status == PositionStatus.ACTIVE]
            
            current_theme_value = sum(pos.market_value for pos in theme_positions)
            current_theme_ratio = current_theme_value / self.current_capital
            
            # 检查题材仓位限制
            max_theme_ratio = self.position_limits['max_theme_position']
            
            if current_theme_ratio >= max_theme_ratio:
                logger.warning(f"题材{theme_name}仓位已达上限{max_theme_ratio:.1%}")
                return 0.0
            
            # 调整仓位以不超过题材限制
            remaining_theme_capacity = (max_theme_ratio - current_theme_ratio) * self.current_capital
            adjusted_value = min(position_value, remaining_theme_capacity)
            
            return adjusted_value
            
        except Exception as e:
            logger.error(f"应用相关性调整失败: {e}")
            return position_value
    
    def open_position(self, signal: TradingSignal, current_price: float) -> bool:
        """
        开仓
        
        Args:
            signal: 交易信号
            current_price: 当前价格
            
        Returns:
            bool: 是否成功开仓
        """
        try:
            if signal.signal_type != SignalType.BUY:
                logger.warning(f"非买入信号，无法开仓: {signal.symbol}")
                return False
            
            # 检查是否已有持仓
            if signal.symbol in self.positions and self.positions[signal.symbol].status == PositionStatus.ACTIVE:
                logger.warning(f"股票{signal.symbol}已有持仓，无法重复开仓")
                return False
            
            # 计算仓位大小
            quantity = self.calculate_position_size(signal, current_price)
            
            if quantity <= 0:
                logger.warning(f"计算仓位为0，无法开仓: {signal.symbol}")
                return False
            
            # 计算交易成本
            position_value = quantity * current_price
            transaction_cost = position_value * 0.001  # 简化：0.1%交易成本
            total_cost = position_value + transaction_cost
            
            # 检查资金充足性
            if total_cost > self.cash_balance:
                logger.warning(f"资金不足，无法开仓: {signal.symbol}, 需要{total_cost:,.2f}, 可用{self.cash_balance:,.2f}")
                return False
            
            # 创建持仓记录
            position = Position(
                symbol=signal.symbol,
                quantity=quantity,
                avg_price=current_price,
                current_price=current_price,
                market_value=position_value,
                cost_basis=total_cost,
                unrealized_pnl=0.0,
                unrealized_pnl_pct=0.0,
                weight=position_value / self.current_capital,
                buy_date=datetime.now(),
                holding_days=0,
                buy_signal=signal,
                stop_loss_price=signal.stop_loss,
                take_profit_price=signal.target_price,
                max_loss_pct=self.risk_params['stop_loss_ratio'],
                theme_name=signal.theme_name,
                is_leader=signal.is_leader,
                status=PositionStatus.ACTIVE,
                last_updated=datetime.now(),
                var_1d=None,
                max_drawdown=0.0,
                sharpe_ratio=None
            )
            
            # 更新持仓和资金
            self.positions[signal.symbol] = position
            self.cash_balance -= total_cost
            
            # 记录交易历史
            trade_record = {
                'symbol': signal.symbol,
                'action': 'buy',
                'quantity': quantity,
                'price': current_price,
                'value': position_value,
                'cost': total_cost,
                'timestamp': datetime.now(),
                'signal': signal
            }
            self.trade_history.append(trade_record)
            
            logger.info(f"开仓成功: {signal.symbol}, 数量: {quantity}, 价格: {current_price:.2f}, "
                       f"价值: {position_value:,.2f}")
            
            return True

        except Exception as e:
            logger.error(f"开仓失败: {e}")
            return False

    def close_position(self, symbol: str, current_price: float, reason: str = "manual") -> bool:
        """
        平仓

        Args:
            symbol: 股票代码
            current_price: 当前价格
            reason: 平仓原因

        Returns:
            bool: 是否成功平仓
        """
        try:
            if symbol not in self.positions:
                logger.warning(f"未找到持仓: {symbol}")
                return False

            position = self.positions[symbol]

            if position.status != PositionStatus.ACTIVE:
                logger.warning(f"持仓状态异常，无法平仓: {symbol}, 状态: {position.status}")
                return False

            # 计算交易价值和成本
            sell_value = position.quantity * current_price
            transaction_cost = sell_value * 0.001  # 简化：0.1%交易成本
            net_proceeds = sell_value - transaction_cost

            # 计算盈亏
            realized_pnl = net_proceeds - position.cost_basis
            realized_pnl_pct = realized_pnl / position.cost_basis

            # 更新持仓状态
            position.status = PositionStatus.CLOSED
            position.current_price = current_price
            position.market_value = sell_value
            position.unrealized_pnl = realized_pnl
            position.unrealized_pnl_pct = realized_pnl_pct
            position.last_updated = datetime.now()

            # 更新资金
            self.cash_balance += net_proceeds

            # 移动到已平仓列表
            self.closed_positions.append(position)
            del self.positions[symbol]

            # 记录交易历史
            trade_record = {
                'symbol': symbol,
                'action': 'sell',
                'quantity': position.quantity,
                'price': current_price,
                'value': sell_value,
                'proceeds': net_proceeds,
                'pnl': realized_pnl,
                'pnl_pct': realized_pnl_pct,
                'holding_days': position.holding_days,
                'reason': reason,
                'timestamp': datetime.now()
            }
            self.trade_history.append(trade_record)

            logger.info(f"平仓成功: {symbol}, 价格: {current_price:.2f}, "
                       f"盈亏: {realized_pnl:,.2f} ({realized_pnl_pct:.2%}), 原因: {reason}")

            return True

        except Exception as e:
            logger.error(f"平仓失败: {e}")
            return False

    def update_positions(self, price_data: Dict[str, float]):
        """
        更新持仓信息

        Args:
            price_data: 股票价格数据 {symbol: current_price}
        """
        try:
            total_value = self.cash_balance

            for symbol, position in self.positions.items():
                if position.status != PositionStatus.ACTIVE:
                    continue

                current_price = price_data.get(symbol)
                if current_price is None:
                    logger.warning(f"未获取到{symbol}的价格数据")
                    continue

                # 更新价格和市值
                position.current_price = current_price
                position.market_value = position.quantity * current_price

                # 更新盈亏
                position.unrealized_pnl = position.market_value - position.cost_basis
                position.unrealized_pnl_pct = position.unrealized_pnl / position.cost_basis

                # 更新持仓天数
                position.holding_days = (datetime.now() - position.buy_date).days

                # 更新仓位权重
                position.weight = position.market_value / self.current_capital

                # 更新最大回撤
                if position.unrealized_pnl_pct < position.max_drawdown:
                    position.max_drawdown = position.unrealized_pnl_pct

                position.last_updated = datetime.now()

                total_value += position.market_value

            # 更新总资产
            self.current_capital = total_value

            # 记录每日净值
            self.daily_values.append(total_value)

            # 计算日收益率
            if len(self.daily_values) > 1:
                daily_return = (total_value - self.daily_values[-2]) / self.daily_values[-2]
                self.daily_returns.append(daily_return)

            logger.debug(f"持仓更新完成，总资产: {total_value:,.2f}")

        except Exception as e:
            logger.error(f"更新持仓失败: {e}")

    def check_risk_controls(self, price_data: Dict[str, float]) -> List[Dict]:
        """
        检查风险控制条件

        Args:
            price_data: 股票价格数据

        Returns:
            List[Dict]: 需要执行的风控操作列表
        """
        try:
            risk_actions = []

            for symbol, position in self.positions.items():
                if position.status != PositionStatus.ACTIVE:
                    continue

                current_price = price_data.get(symbol)
                if current_price is None:
                    continue

                # 检查止损
                if position.stop_loss_price and current_price <= position.stop_loss_price:
                    risk_actions.append({
                        'symbol': symbol,
                        'action': 'stop_loss',
                        'current_price': current_price,
                        'trigger_price': position.stop_loss_price,
                        'reason': f'触发止损，当前价{current_price:.2f} <= 止损价{position.stop_loss_price:.2f}'
                    })

                # 检查止盈
                if position.take_profit_price and current_price >= position.take_profit_price:
                    risk_actions.append({
                        'symbol': symbol,
                        'action': 'take_profit',
                        'current_price': current_price,
                        'trigger_price': position.take_profit_price,
                        'reason': f'触发止盈，当前价{current_price:.2f} >= 止盈价{position.take_profit_price:.2f}'
                    })

                # 检查最大亏损比例
                loss_pct = (position.avg_price - current_price) / position.avg_price
                if loss_pct > position.max_loss_pct:
                    risk_actions.append({
                        'symbol': symbol,
                        'action': 'max_loss',
                        'current_price': current_price,
                        'loss_pct': loss_pct,
                        'reason': f'超过最大亏损比例，当前亏损{loss_pct:.2%} > 限制{position.max_loss_pct:.2%}'
                    })

                # 检查最大持仓天数
                max_holding_days = self.risk_params['max_holding_days']
                if position.holding_days >= max_holding_days:
                    risk_actions.append({
                        'symbol': symbol,
                        'action': 'max_holding',
                        'current_price': current_price,
                        'holding_days': position.holding_days,
                        'reason': f'超过最大持仓天数，当前{position.holding_days}天 >= 限制{max_holding_days}天'
                    })

            # 检查总仓位风险
            total_position_ratio = sum(pos.weight for pos in self.positions.values()
                                     if pos.status == PositionStatus.ACTIVE)
            max_total_position = self.position_limits['max_total_position']

            if total_position_ratio > max_total_position:
                # 找出权重最大的持仓进行减仓
                largest_position = max(
                    [pos for pos in self.positions.values() if pos.status == PositionStatus.ACTIVE],
                    key=lambda x: x.weight,
                    default=None
                )

                if largest_position:
                    risk_actions.append({
                        'symbol': largest_position.symbol,
                        'action': 'reduce_position',
                        'current_price': price_data.get(largest_position.symbol),
                        'total_ratio': total_position_ratio,
                        'reason': f'总仓位过高{total_position_ratio:.2%} > 限制{max_total_position:.2%}'
                    })

            return risk_actions

        except Exception as e:
            logger.error(f"检查风险控制失败: {e}")
            return []

    def execute_risk_controls(self, price_data: Dict[str, float]) -> int:
        """
        执行风险控制

        Args:
            price_data: 股票价格数据

        Returns:
            int: 执行的风控操作数量
        """
        try:
            risk_actions = self.check_risk_controls(price_data)
            executed_count = 0

            for action in risk_actions:
                symbol = action['symbol']
                current_price = action['current_price']
                reason = action['reason']

                if action['action'] in ['stop_loss', 'take_profit', 'max_loss', 'max_holding']:
                    # 全部平仓
                    if self.close_position(symbol, current_price, reason):
                        executed_count += 1
                        logger.info(f"风控平仓: {symbol}, {reason}")

                elif action['action'] == 'reduce_position':
                    # 减仓50%
                    if symbol in self.positions:
                        position = self.positions[symbol]
                        reduce_quantity = int(position.quantity * 0.5 / 100) * 100  # 减仓50%，保持100股整数倍

                        if reduce_quantity > 0:
                            # 简化实现：直接调整数量
                            position.quantity -= reduce_quantity
                            position.market_value = position.quantity * current_price
                            position.weight = position.market_value / self.current_capital

                            # 增加现金
                            proceeds = reduce_quantity * current_price * 0.999  # 扣除交易成本
                            self.cash_balance += proceeds

                            executed_count += 1
                            logger.info(f"风控减仓: {symbol}, 减少{reduce_quantity}股, {reason}")

            return executed_count

        except Exception as e:
            logger.error(f"执行风险控制失败: {e}")
            return 0

    def get_portfolio_metrics(self) -> PortfolioMetrics:
        """获取投资组合指标"""
        try:
            # 基础指标
            total_value = self.current_capital
            total_cost = self.initial_capital
            total_pnl = total_value - total_cost
            total_pnl_pct = total_pnl / total_cost

            active_positions = [pos for pos in self.positions.values()
                              if pos.status == PositionStatus.ACTIVE]
            position_count = len(active_positions)

            # 仓位权重分布
            position_weights = {}
            theme_weights = {}

            for position in active_positions:
                position_weights[position.symbol] = position.weight

                if position.theme_name:
                    if position.theme_name not in theme_weights:
                        theme_weights[position.theme_name] = 0.0
                    theme_weights[position.theme_name] += position.weight

            # 风险指标
            portfolio_var = self._calculate_portfolio_var()
            max_drawdown = self._calculate_max_drawdown()
            sharpe_ratio = self._calculate_sharpe_ratio()
            volatility = self._calculate_volatility()

            # 绩效指标
            win_rate, avg_win_pct, avg_loss_pct, profit_factor = self._calculate_performance_metrics()

            return PortfolioMetrics(
                total_value=total_value,
                total_cost=total_cost,
                total_pnl=total_pnl,
                total_pnl_pct=total_pnl_pct,
                cash_balance=self.cash_balance,
                position_count=position_count,
                portfolio_var=portfolio_var,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                volatility=volatility,
                position_weights=position_weights,
                theme_weights=theme_weights,
                win_rate=win_rate,
                avg_win_pct=avg_win_pct,
                avg_loss_pct=avg_loss_pct,
                profit_factor=profit_factor,
                last_updated=datetime.now()
            )

        except Exception as e:
            logger.error(f"获取投资组合指标失败: {e}")
            return PortfolioMetrics(
                total_value=self.current_capital,
                total_cost=self.initial_capital,
                total_pnl=0.0,
                total_pnl_pct=0.0,
                cash_balance=self.cash_balance,
                position_count=0,
                portfolio_var=0.0,
                max_drawdown=0.0,
                sharpe_ratio=0.0,
                volatility=0.0,
                position_weights={},
                theme_weights={},
                win_rate=0.0,
                avg_win_pct=0.0,
                avg_loss_pct=0.0,
                profit_factor=0.0,
                last_updated=datetime.now()
            )

    def _calculate_portfolio_var(self, confidence_level: float = 0.05) -> float:
        """计算投资组合风险价值（VaR）"""
        try:
            if len(self.daily_returns) < 30:
                return 0.0

            # 使用历史模拟法计算VaR
            returns = np.array(self.daily_returns[-252:])  # 最近一年数据
            var_value = np.percentile(returns, confidence_level * 100)

            return abs(var_value * self.current_capital)

        except Exception as e:
            logger.error(f"计算VaR失败: {e}")
            return 0.0

    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        try:
            if len(self.daily_values) < 2:
                return 0.0

            values = np.array(self.daily_values)
            peak = np.maximum.accumulate(values)
            drawdown = (values - peak) / peak

            return abs(np.min(drawdown))

        except Exception as e:
            logger.error(f"计算最大回撤失败: {e}")
            return 0.0

    def _calculate_sharpe_ratio(self, risk_free_rate: float = 0.03) -> float:
        """计算夏普比率"""
        try:
            if len(self.daily_returns) < 30:
                return 0.0

            returns = np.array(self.daily_returns)
            excess_returns = returns - risk_free_rate / 252  # 日化无风险利率

            if np.std(excess_returns) == 0:
                return 0.0

            return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)

        except Exception as e:
            logger.error(f"计算夏普比率失败: {e}")
            return 0.0

    def _calculate_volatility(self) -> float:
        """计算年化波动率"""
        try:
            if len(self.daily_returns) < 30:
                return 0.0

            returns = np.array(self.daily_returns)
            return np.std(returns) * np.sqrt(252)

        except Exception as e:
            logger.error(f"计算波动率失败: {e}")
            return 0.0

    def _calculate_performance_metrics(self) -> Tuple[float, float, float, float]:
        """计算绩效指标"""
        try:
            if not self.closed_positions:
                return 0.0, 0.0, 0.0, 0.0

            # 统计盈亏交易
            winning_trades = [pos for pos in self.closed_positions if pos.unrealized_pnl > 0]
            losing_trades = [pos for pos in self.closed_positions if pos.unrealized_pnl < 0]

            total_trades = len(self.closed_positions)
            win_count = len(winning_trades)

            # 胜率
            win_rate = win_count / total_trades if total_trades > 0 else 0.0

            # 平均盈利和亏损百分比
            avg_win_pct = np.mean([pos.unrealized_pnl_pct for pos in winning_trades]) if winning_trades else 0.0
            avg_loss_pct = np.mean([abs(pos.unrealized_pnl_pct) for pos in losing_trades]) if losing_trades else 0.0

            # 盈亏比
            total_profit = sum(pos.unrealized_pnl for pos in winning_trades)
            total_loss = abs(sum(pos.unrealized_pnl for pos in losing_trades))
            profit_factor = total_profit / total_loss if total_loss > 0 else 0.0

            return win_rate, avg_win_pct, avg_loss_pct, profit_factor

        except Exception as e:
            logger.error(f"计算绩效指标失败: {e}")
            return 0.0, 0.0, 0.0, 0.0

    async def save_positions_to_file(self, date_str: str = None):
        """保存持仓数据到文件"""
        try:
            from backend.data.file_storage import file_storage

            if date_str is None:
                date_str = datetime.now().strftime("%Y%m%d")

            # 转换持仓数据为DataFrame
            position_records = []
            for position in self.positions.values():
                record = {
                    'symbol': position.symbol,
                    'quantity': position.quantity,
                    'avg_price': position.avg_price,
                    'current_price': position.current_price,
                    'market_value': position.market_value,
                    'cost_basis': position.cost_basis,
                    'unrealized_pnl': position.unrealized_pnl,
                    'unrealized_pnl_pct': position.unrealized_pnl_pct,
                    'weight': position.weight,
                    'buy_date': position.buy_date,
                    'holding_days': position.holding_days,
                    'stop_loss_price': position.stop_loss_price,
                    'take_profit_price': position.take_profit_price,
                    'theme_name': position.theme_name,
                    'is_leader': position.is_leader,
                    'status': position.status.value,
                    'last_updated': position.last_updated
                }
                position_records.append(record)

            if position_records:
                df = pd.DataFrame(position_records)
                success = await file_storage.save_position_data(df)

                if success:
                    logger.info(f"持仓数据已保存: {date_str}, 共{len(position_records)}个持仓")
                else:
                    logger.error(f"保存持仓数据失败: {date_str}")

                return success
            else:
                logger.info("无持仓数据需要保存")
                return True

        except Exception as e:
            logger.error(f"保存持仓数据到文件失败: {e}")
            return False

    def get_position_summary(self) -> Dict:
        """获取持仓摘要"""
        try:
            active_positions = [pos for pos in self.positions.values()
                              if pos.status == PositionStatus.ACTIVE]

            if not active_positions:
                return {
                    "total_positions": 0,
                    "total_value": self.cash_balance,
                    "cash_ratio": 1.0,
                    "position_ratio": 0.0
                }

            total_position_value = sum(pos.market_value for pos in active_positions)
            total_pnl = sum(pos.unrealized_pnl for pos in active_positions)

            # 按主题分组
            theme_summary = {}
            for pos in active_positions:
                theme = pos.theme_name or "其他"
                if theme not in theme_summary:
                    theme_summary[theme] = {
                        "count": 0,
                        "value": 0.0,
                        "pnl": 0.0
                    }

                theme_summary[theme]["count"] += 1
                theme_summary[theme]["value"] += pos.market_value
                theme_summary[theme]["pnl"] += pos.unrealized_pnl

            return {
                "total_positions": len(active_positions),
                "total_value": total_position_value,
                "total_pnl": total_pnl,
                "cash_balance": self.cash_balance,
                "cash_ratio": self.cash_balance / self.current_capital,
                "position_ratio": total_position_value / self.current_capital,
                "theme_summary": theme_summary,
                "top_positions": sorted(
                    [{"symbol": pos.symbol, "value": pos.market_value, "pnl_pct": pos.unrealized_pnl_pct}
                     for pos in active_positions],
                    key=lambda x: x["value"],
                    reverse=True
                )[:5]
            }

        except Exception as e:
            logger.error(f"获取持仓摘要失败: {e}")
            return {}


# 全局仓位管理器实例
position_manager = PositionManager()
