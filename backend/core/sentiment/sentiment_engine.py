"""
市场情绪计算引擎
基于涨跌停数据计算市场情绪指标，识别情绪周期
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from loguru import logger
from dataclasses import dataclass

from config.settings import settings


@dataclass
class SentimentData:
    """情绪数据结构"""
    timestamp: datetime
    limit_up_count: int
    limit_down_count: int
    max_continuous_boards: int
    sentiment_score: float
    sentiment_phase: str
    market_temperature: float
    trend_direction: str
    volatility_index: float


class MarketSentimentEngine:
    """市场情绪计算引擎"""
    
    def __init__(self):
        # 从配置获取情绪权重参数
        self.sentiment_params = settings.strategy.sentiment_weights
        
        # 情绪阈值配置
        self.phase_thresholds = {
            'ice_point': 20,      # 冰点期阈值
            'warming': 50,        # 回暖期阈值  
            'fermentation': 80,   # 发酵期阈值
            'climax': 120,        # 高潮期阈值
        }
        
        # 历史情绪数据缓存
        self.sentiment_history: List[SentimentData] = []
        self.max_history_length = 30  # 保留30天历史数据
        
        logger.info("市场情绪计算引擎初始化完成")
    
    def calculate_sentiment_score(self, market_data: Dict) -> float:
        """
        计算市场情绪值
        情绪值 = (涨停股数 × 2) - (跌停股数 × 3) + (连板高度 × 5)
        
        Args:
            market_data: 包含涨跌停数据的字典
            
        Returns:
            float: 情绪评分
        """
        try:
            limit_up_count = market_data.get('limit_up_count', 0)
            limit_down_count = market_data.get('limit_down_count', 0)
            max_continuous_boards = market_data.get('max_continuous_boards', 0)
            
            # 基础情绪计算
            sentiment_score = (
                limit_up_count * self.sentiment_params['limit_up_weight'] +
                limit_down_count * self.sentiment_params['limit_down_weight'] +
                max_continuous_boards * self.sentiment_params['continuous_board_weight']
            )
            
            # 考虑成交量因子（如果有的话）
            volume_factor = market_data.get('volume_factor', 1.0)
            sentiment_score *= volume_factor * self.sentiment_params.get('volume_weight', 1.0)
            
            logger.debug(f"情绪计算: 涨停{limit_up_count}, 跌停{limit_down_count}, 连板{max_continuous_boards}, 得分{sentiment_score:.2f}")
            
            return sentiment_score
            
        except Exception as e:
            logger.error(f"计算情绪评分失败: {e}")
            return 0.0
    
    def get_sentiment_phase(self, sentiment_score: float) -> str:
        """
        识别市场情绪周期
        
        Args:
            sentiment_score: 情绪评分
            
        Returns:
            str: 情绪周期阶段
        """
        try:
            if sentiment_score < self.phase_thresholds['ice_point']:
                return "冰点期"
            elif sentiment_score < self.phase_thresholds['warming']:
                return "回暖期"
            elif sentiment_score < self.phase_thresholds['fermentation']:
                return "发酵期"
            elif sentiment_score < self.phase_thresholds['climax']:
                return "高潮期"
            else:
                return "退潮期"  # 超高情绪通常预示着反转
                
        except Exception as e:
            logger.error(f"识别情绪周期失败: {e}")
            return "混沌期"
    
    def calculate_market_temperature(self, sentiment_score: float, historical_scores: List[float]) -> float:
        """
        计算市场温度（相对于历史水平）
        
        Args:
            sentiment_score: 当前情绪评分
            historical_scores: 历史情绪评分列表
            
        Returns:
            float: 市场温度 (0-100)
        """
        try:
            if not historical_scores:
                return 50.0  # 默认中性温度
            
            # 计算历史分位数
            percentile = np.percentile(historical_scores, 
                                     [10, 25, 50, 75, 90])
            
            if sentiment_score <= percentile[0]:
                return 10.0  # 极冷
            elif sentiment_score <= percentile[1]:
                return 25.0  # 偏冷
            elif sentiment_score <= percentile[2]:
                return 50.0  # 中性
            elif sentiment_score <= percentile[3]:
                return 75.0  # 偏热
            elif sentiment_score <= percentile[4]:
                return 90.0  # 很热
            else:
                return 100.0  # 极热
                
        except Exception as e:
            logger.error(f"计算市场温度失败: {e}")
            return 50.0
    
    def analyze_trend_direction(self, recent_scores: List[float]) -> str:
        """
        分析情绪趋势方向
        
        Args:
            recent_scores: 最近的情绪评分列表
            
        Returns:
            str: 趋势方向 (上升/下降/震荡)
        """
        try:
            if len(recent_scores) < 3:
                return "震荡"
            
            # 计算移动平均
            short_ma = np.mean(recent_scores[-3:])
            long_ma = np.mean(recent_scores[-7:]) if len(recent_scores) >= 7 else np.mean(recent_scores)
            
            # 计算趋势强度
            trend_strength = abs(short_ma - long_ma) / long_ma if long_ma != 0 else 0
            
            if trend_strength < 0.1:
                return "震荡"
            elif short_ma > long_ma:
                return "上升"
            else:
                return "下降"
                
        except Exception as e:
            logger.error(f"分析趋势方向失败: {e}")
            return "震荡"
    
    def calculate_volatility_index(self, recent_scores: List[float]) -> float:
        """
        计算情绪波动率指数
        
        Args:
            recent_scores: 最近的情绪评分列表
            
        Returns:
            float: 波动率指数 (0-100)
        """
        try:
            if len(recent_scores) < 2:
                return 0.0
            
            # 计算标准差
            std_dev = np.std(recent_scores)
            mean_score = np.mean(recent_scores)
            
            # 变异系数
            cv = std_dev / mean_score if mean_score != 0 else 0
            
            # 转换为0-100的指数
            volatility_index = min(cv * 100, 100)
            
            return volatility_index
            
        except Exception as e:
            logger.error(f"计算波动率指数失败: {e}")
            return 0.0
    
    def process_market_data(self, market_data: Dict) -> SentimentData:
        """
        处理市场数据，生成完整的情绪分析结果
        
        Args:
            market_data: 市场数据字典
            
        Returns:
            SentimentData: 情绪分析结果
        """
        try:
            # 计算基础情绪评分
            sentiment_score = self.calculate_sentiment_score(market_data)
            
            # 识别情绪周期
            sentiment_phase = self.get_sentiment_phase(sentiment_score)
            
            # 获取历史评分用于计算相对指标
            historical_scores = [data.sentiment_score for data in self.sentiment_history]
            recent_scores = historical_scores[-10:] if len(historical_scores) >= 10 else historical_scores
            
            # 计算市场温度
            market_temperature = self.calculate_market_temperature(sentiment_score, historical_scores)
            
            # 分析趋势方向
            trend_direction = self.analyze_trend_direction(recent_scores + [sentiment_score])
            
            # 计算波动率指数
            volatility_index = self.calculate_volatility_index(recent_scores + [sentiment_score])
            
            # 创建情绪数据对象
            sentiment_data = SentimentData(
                timestamp=market_data.get('timestamp', datetime.now()),
                limit_up_count=market_data.get('limit_up_count', 0),
                limit_down_count=market_data.get('limit_down_count', 0),
                max_continuous_boards=market_data.get('max_continuous_boards', 0),
                sentiment_score=sentiment_score,
                sentiment_phase=sentiment_phase,
                market_temperature=market_temperature,
                trend_direction=trend_direction,
                volatility_index=volatility_index
            )
            
            # 更新历史数据
            self._update_sentiment_history(sentiment_data)
            
            logger.info(f"情绪分析完成: {sentiment_phase}, 评分{sentiment_score:.2f}, 温度{market_temperature:.1f}")
            
            return sentiment_data
            
        except Exception as e:
            logger.error(f"处理市场数据失败: {e}")
            # 返回默认的情绪数据
            return SentimentData(
                timestamp=datetime.now(),
                limit_up_count=0,
                limit_down_count=0,
                max_continuous_boards=0,
                sentiment_score=0.0,
                sentiment_phase="混沌期",
                market_temperature=50.0,
                trend_direction="震荡",
                volatility_index=0.0
            )
    
    def _update_sentiment_history(self, sentiment_data: SentimentData):
        """更新情绪历史数据"""
        try:
            self.sentiment_history.append(sentiment_data)
            
            # 保持历史数据长度限制
            if len(self.sentiment_history) > self.max_history_length:
                self.sentiment_history = self.sentiment_history[-self.max_history_length:]
                
        except Exception as e:
            logger.error(f"更新情绪历史数据失败: {e}")
    
    def get_sentiment_summary(self) -> Dict:
        """获取情绪分析摘要"""
        try:
            if not self.sentiment_history:
                return {
                    "status": "no_data",
                    "message": "暂无情绪数据"
                }
            
            latest = self.sentiment_history[-1]
            
            # 计算统计信息
            recent_scores = [data.sentiment_score for data in self.sentiment_history[-7:]]
            avg_score = np.mean(recent_scores)
            max_score = np.max(recent_scores)
            min_score = np.min(recent_scores)
            
            return {
                "status": "success",
                "current": {
                    "sentiment_score": latest.sentiment_score,
                    "sentiment_phase": latest.sentiment_phase,
                    "market_temperature": latest.market_temperature,
                    "trend_direction": latest.trend_direction,
                    "volatility_index": latest.volatility_index,
                    "timestamp": latest.timestamp.isoformat()
                },
                "statistics": {
                    "avg_score_7d": avg_score,
                    "max_score_7d": max_score,
                    "min_score_7d": min_score,
                    "data_points": len(self.sentiment_history)
                },
                "market_conditions": {
                    "limit_up_count": latest.limit_up_count,
                    "limit_down_count": latest.limit_down_count,
                    "max_continuous_boards": latest.max_continuous_boards
                }
            }
            
        except Exception as e:
            logger.error(f"获取情绪摘要失败: {e}")
            return {
                "status": "error",
                "message": str(e)
            }
    
    def is_good_entry_timing(self) -> Tuple[bool, str]:
        """
        判断是否为良好的入场时机
        
        Returns:
            Tuple[bool, str]: (是否适合入场, 原因说明)
        """
        try:
            if not self.sentiment_history:
                return False, "暂无情绪数据"
            
            latest = self.sentiment_history[-1]
            
            # 适合入场的情绪阶段
            good_phases = ["回暖期", "发酵期"]
            
            if latest.sentiment_phase in good_phases:
                if latest.trend_direction == "上升":
                    return True, f"市场处于{latest.sentiment_phase}且趋势向上，适合入场"
                elif latest.trend_direction == "震荡" and latest.market_temperature < 80:
                    return True, f"市场处于{latest.sentiment_phase}且温度适中，可考虑入场"
            
            # 不适合入场的情况
            if latest.sentiment_phase == "冰点期":
                return False, "市场情绪冰点，等待回暖信号"
            elif latest.sentiment_phase == "高潮期":
                return False, "市场情绪过热，风险较高"
            elif latest.sentiment_phase == "退潮期":
                return False, "市场情绪退潮，避免追高"
            else:
                return False, f"市场情绪{latest.sentiment_phase}，暂不适合入场"
                
        except Exception as e:
            logger.error(f"判断入场时机失败: {e}")
            return False, "分析失败"

    def _get_recommended_position(self) -> float:
        """
        根据当前情绪状态推荐仓位比例

        Returns:
            float: 推荐仓位比例 (0-1)
        """
        try:
            if not self.sentiment_history:
                return 0.0

            latest = self.sentiment_history[-1]

            # 基于情绪周期的仓位建议
            position_limits = settings.strategy.sentiment_position_limits
            base_position = position_limits.get(latest.sentiment_phase, 0.0)

            # 根据趋势方向调整
            if latest.trend_direction == "上升":
                base_position *= 1.1  # 上升趋势增加10%
            elif latest.trend_direction == "下降":
                base_position *= 0.8  # 下降趋势减少20%

            # 根据波动率调整
            if latest.volatility_index > 50:
                base_position *= 0.9  # 高波动减少仓位

            # 确保在合理范围内
            return min(max(base_position, 0.0), 1.0)

        except Exception as e:
            logger.error(f"计算推荐仓位失败: {e}")
            return 0.0


    async def save_sentiment_data_to_file(self, sentiment_data: SentimentData, date_str: str = None):
        """保存情绪数据到文件"""
        try:
            from backend.data.file_storage import file_storage

            if date_str is None:
                date_str = sentiment_data.timestamp.strftime("%Y%m%d")

            # 转换为DataFrame
            df = pd.DataFrame([{
                'timestamp': sentiment_data.timestamp,
                'limit_up_count': sentiment_data.limit_up_count,
                'limit_down_count': sentiment_data.limit_down_count,
                'max_continuous_boards': sentiment_data.max_continuous_boards,
                'sentiment_score': sentiment_data.sentiment_score,
                'sentiment_phase': sentiment_data.sentiment_phase,
                'market_temperature': sentiment_data.market_temperature,
                'trend_direction': sentiment_data.trend_direction,
                'volatility_index': sentiment_data.volatility_index
            }])

            # 保存到文件
            success = await file_storage.save_sentiment_data(df, date_str)

            if success:
                logger.info(f"情绪数据已保存到文件: {date_str}")
            else:
                logger.error(f"保存情绪数据失败: {date_str}")

            return success

        except Exception as e:
            logger.error(f"保存情绪数据到文件失败: {e}")
            return False

    async def load_historical_sentiment_data(self, days: int = 30):
        """从文件加载历史情绪数据"""
        try:
            from backend.data.file_storage import file_storage

            self.sentiment_history.clear()

            # 加载最近N天的数据
            for i in range(days):
                date = datetime.now() - timedelta(days=i)
                date_str = date.strftime("%Y%m%d")

                df = await file_storage.load_sentiment_data(date_str)

                if df is not None and not df.empty:
                    for _, row in df.iterrows():
                        sentiment_data = SentimentData(
                            timestamp=pd.to_datetime(row['timestamp']),
                            limit_up_count=int(row['limit_up_count']),
                            limit_down_count=int(row['limit_down_count']),
                            max_continuous_boards=int(row['max_continuous_boards']),
                            sentiment_score=float(row['sentiment_score']),
                            sentiment_phase=str(row['sentiment_phase']),
                            market_temperature=float(row['market_temperature']),
                            trend_direction=str(row['trend_direction']),
                            volatility_index=float(row['volatility_index'])
                        )
                        self.sentiment_history.append(sentiment_data)

            # 按时间排序
            self.sentiment_history.sort(key=lambda x: x.timestamp)

            logger.info(f"加载历史情绪数据完成，共{len(self.sentiment_history)}条记录")

        except Exception as e:
            logger.error(f"加载历史情绪数据失败: {e}")


# 全局情绪引擎实例
sentiment_engine = MarketSentimentEngine()
