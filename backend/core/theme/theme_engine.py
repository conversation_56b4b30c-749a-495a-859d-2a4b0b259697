"""
题材识别与评分引擎
基于新闻文本分析识别热点题材，计算题材评分和生命周期
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Set
from loguru import logger
from dataclasses import dataclass
import re
import json
from collections import Counter, defaultdict

# NLP相关导入
try:
    import jieba
    import jieba.analyse
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    logger.warning("jieba未安装，中文分词功能将不可用")

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.cluster import DBSCAN
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("scikit-learn未安装，文本聚类功能将不可用")

from config.settings import settings


@dataclass
class ThemeData:
    """题材数据结构"""
    theme_id: str
    theme_name: str
    theme_type: str  # policy, industry, event, concept
    policy_factor: float
    industry_factor: float
    event_factor: float
    concept_factor: float
    theme_score: float
    lifecycle_stage: str
    related_stocks: List[str]
    news_count: int
    keywords: List[str]
    first_appeared: datetime
    last_updated: datetime
    heat_trend: str  # rising, stable, declining


class ThemeIdentificationEngine:
    """题材识别与评分引擎"""
    
    def __init__(self):
        # 从配置获取题材权重参数
        self.theme_weights = settings.strategy.theme_weights

        # 定义关键词库
        self.policy_keywords = {
            "政策", "国家", "政府", "发改委", "央行", "财政部", "工信部", "证监会",
            "银保监会", "国务院", "十四五", "双碳", "碳中和", "碳达峰", "新基建",
            "数字经济", "乡村振兴", "共同富裕", "一带一路", "京津冀", "长三角",
            "粤港澳", "自贸区", "国企改革", "混改", "科创板", "注册制"
        }

        self.industry_keywords = {
            "新能源", "光伏", "风电", "储能", "锂电池", "电动车", "新能源汽车",
            "半导体", "芯片", "集成电路", "人工智能", "AI", "机器人", "自动化",
            "5G", "6G", "物联网", "云计算", "大数据", "区块链", "元宇宙",
            "生物医药", "医疗器械", "疫苗", "创新药", "CRO", "医美",
            "军工", "航空", "航天", "船舶", "核电", "特高压"
        }

        self.event_keywords = {
            "并购", "重组", "收购", "分拆", "上市", "IPO", "定增", "回购",
            "业绩", "预增", "扭亏", "中标", "合同", "订单", "协议",
            "突破", "研发", "专利", "技术", "产品", "量产", "投产"
        }

        self.concept_keywords = {
            "概念", "题材", "热点", "炒作", "资金", "游资", "龙头",
            "妖股", "涨停", "连板", "打板", "首板", "二板", "三板",
            "分歧", "一致", "情绪", "人气", "活跃", "强势"
        }

        # 加载题材关键词
        self.theme_keywords = self._load_theme_keywords()

        # 历史题材数据
        self.theme_history: Dict[str, ThemeData] = {}

        # 初始化jieba
        if JIEBA_AVAILABLE:
            self._init_jieba()

        logger.info("题材识别引擎初始化完成")

    def _load_theme_keywords(self) -> Dict[str, Set[str]]:
        """加载题材关键词库"""
        return {
            "policy": self.policy_keywords,
            "industry": self.industry_keywords,
            "event": self.event_keywords,
            "concept": self.concept_keywords
        }
    
    def _init_jieba(self):
        """初始化jieba分词器"""
        try:
            # 添加自定义词典
            for keyword_set in self.theme_keywords.values():
                for keyword in keyword_set:
                    jieba.add_word(keyword)
            
            # 设置jieba日志级别
            jieba.setLogLevel(20)
            
        except Exception as e:
            logger.error(f"初始化jieba失败: {e}")
    
    def extract_keywords_from_text(self, text: str, top_k: int = 10) -> List[str]:
        """从文本中提取关键词"""
        try:
            if not JIEBA_AVAILABLE:
                # 简单的关键词提取
                words = re.findall(r'[\u4e00-\u9fff]+', text)
                return list(set(words))[:top_k]
            
            # 使用jieba提取关键词
            keywords = jieba.analyse.extract_tags(text, topK=top_k, withWeight=False)
            return keywords
            
        except Exception as e:
            logger.error(f"提取关键词失败: {e}")
            return []
    
    def classify_theme_type(self, keywords: List[str], text: str) -> Dict[str, float]:
        """分类题材类型并计算各类型得分"""
        try:
            type_scores = {
                "policy": 0.0,
                "industry": 0.0,
                "event": 0.0,
                "concept": 0.0
            }
            
            # 计算每种类型的匹配度
            for theme_type, keyword_set in self.theme_keywords.items():
                matches = 0
                for keyword in keywords:
                    if keyword in keyword_set:
                        matches += 1
                    # 检查文本中是否包含关键词
                    for theme_keyword in keyword_set:
                        if theme_keyword in text:
                            matches += 0.5
                
                # 归一化得分
                if keywords:
                    type_scores[theme_type] = matches / len(keywords)
            
            return type_scores
            
        except Exception as e:
            logger.error(f"分类题材类型失败: {e}")
            return {"policy": 0.0, "industry": 0.0, "event": 0.0, "concept": 0.0}
    
    def calculate_theme_score(self, theme_data: Dict) -> float:
        """
        计算题材评分
        题材评分 = 政策权重×4 + 行业权重×3 + 事件权重×2 + 主题权重×1
        """
        try:
            policy_score = theme_data.get('policy_factor', 0)
            industry_score = theme_data.get('industry_factor', 0)
            event_score = theme_data.get('event_factor', 0)
            concept_score = theme_data.get('concept_factor', 0)
            
            total_score = (
                policy_score * self.theme_weights['policy'] +
                industry_score * self.theme_weights['industry'] +
                event_score * self.theme_weights['event'] +
                concept_score * self.theme_weights['concept']
            )
            
            # 考虑新闻热度加成
            news_count = theme_data.get('news_count', 0)
            heat_bonus = min(news_count * 0.1, 2.0)  # 最多加2分
            
            return total_score + heat_bonus
            
        except Exception as e:
            logger.error(f"计算题材评分失败: {e}")
            return 0.0
    
    def identify_themes_from_news(self, news_list: List[Dict]) -> List[Dict]:
        """从新闻中识别热点题材"""
        try:
            if not news_list:
                return []
            
            theme_candidates = defaultdict(lambda: {
                'keywords': Counter(),
                'news_count': 0,
                'texts': [],
                'type_scores': defaultdict(float),
                'first_seen': datetime.now(),
                'last_seen': datetime.now()
            })
            
            # 处理每条新闻
            for news in news_list:
                title = news.get('title', '')
                content = news.get('content', '')
                text = f"{title} {content}"
                
                if not text.strip():
                    continue
                
                # 提取关键词
                keywords = self.extract_keywords_from_text(text)
                
                if not keywords:
                    continue
                
                # 分类题材类型
                type_scores = self.classify_theme_type(keywords, text)
                
                # 找到主要题材类型
                main_type = max(type_scores.items(), key=lambda x: x[1])[0]
                
                if type_scores[main_type] < 0.1:  # 阈值过滤
                    continue
                
                # 生成题材名称（使用最频繁的关键词）
                theme_name = keywords[0] if keywords else "未知题材"
                
                # 更新题材候选
                candidate = theme_candidates[theme_name]
                candidate['keywords'].update(keywords)
                candidate['news_count'] += 1
                candidate['texts'].append(text)
                
                # 更新类型得分
                for t_type, score in type_scores.items():
                    candidate['type_scores'][t_type] += score
                
                # 更新时间
                news_time = news.get('publish_time', datetime.now())
                if isinstance(news_time, str):
                    try:
                        news_time = datetime.fromisoformat(news_time.replace('Z', '+00:00'))
                    except:
                        news_time = datetime.now()
                
                if news_time < candidate['first_seen']:
                    candidate['first_seen'] = news_time
                if news_time > candidate['last_seen']:
                    candidate['last_seen'] = news_time
            
            # 转换为题材列表
            themes = []
            for theme_name, candidate in theme_candidates.items():
                if candidate['news_count'] < 2:  # 至少2条新闻才算题材
                    continue
                
                # 归一化类型得分
                total_news = candidate['news_count']
                normalized_scores = {
                    t_type: score / total_news 
                    for t_type, score in candidate['type_scores'].items()
                }
                
                # 计算题材评分
                theme_data = {
                    'policy_factor': normalized_scores['policy'],
                    'industry_factor': normalized_scores['industry'],
                    'event_factor': normalized_scores['event'],
                    'concept_factor': normalized_scores['concept'],
                    'news_count': candidate['news_count']
                }
                
                theme_score = self.calculate_theme_score(theme_data)
                
                # 确定主要类型
                main_type = max(normalized_scores.items(), key=lambda x: x[1])[0]
                
                theme = {
                    'theme_id': f"{theme_name}_{datetime.now().strftime('%Y%m%d')}",
                    'theme_name': theme_name,
                    'theme_type': main_type,
                    'policy_factor': normalized_scores['policy'],
                    'industry_factor': normalized_scores['industry'],
                    'event_factor': normalized_scores['event'],
                    'concept_factor': normalized_scores['concept'],
                    'theme_score': theme_score,
                    'news_count': candidate['news_count'],
                    'keywords': list(candidate['keywords'].keys())[:10],
                    'first_appeared': candidate['first_seen'],
                    'last_updated': candidate['last_seen']
                }
                
                themes.append(theme)
            
            # 按评分排序
            themes.sort(key=lambda x: x['theme_score'], reverse=True)
            
            logger.info(f"识别到{len(themes)}个题材")
            return themes

        except Exception as e:
            logger.error(f"从新闻识别题材失败: {e}")
            return []

    def track_theme_lifecycle(self, theme_id: str, historical_data: pd.DataFrame = None) -> str:
        """
        跟踪题材生命周期

        Args:
            theme_id: 题材ID
            historical_data: 相关股票历史数据

        Returns:
            str: 生命周期阶段
        """
        try:
            if historical_data is None or historical_data.empty:
                return "萌芽期"

            # 计算题材相关股票的表现指标
            if 'return' not in historical_data.columns:
                # 如果没有收益率列，计算收益率
                if 'close' in historical_data.columns:
                    historical_data['return'] = historical_data['close'].pct_change()
                else:
                    return "萌芽期"

            if 'volume' not in historical_data.columns:
                return "萌芽期"

            # 计算关键指标
            recent_data = historical_data.tail(5)  # 最近5天
            avg_return = recent_data['return'].mean()

            # 成交量趋势
            volume_ma_3 = recent_data['volume'].tail(3).mean()
            volume_ma_10 = historical_data['volume'].tail(10).mean()
            volume_trend = volume_ma_3 / volume_ma_10 if volume_ma_10 > 0 else 1.0

            # 参与度（涨幅超过3%的股票比例）
            positive_stocks = len(recent_data[recent_data['return'] > 0.03])
            participation_rate = positive_stocks / len(recent_data) if len(recent_data) > 0 else 0

            # 判断生命周期阶段
            if avg_return < 0.02 and volume_trend < 1.2:
                return "萌芽期"
            elif avg_return >= 0.02 and volume_trend >= 1.2 and participation_rate < 0.5:
                return "发酵期"
            elif avg_return > 0.05 and participation_rate >= 0.5:
                return "高潮期"
            elif avg_return > 0.02 and participation_rate < 0.3:
                return "分化期"
            else:
                return "衰退期"

        except Exception as e:
            logger.error(f"跟踪题材生命周期失败: {e}")
            return "萌芽期"

    def find_related_stocks(self, theme_keywords: List[str], stock_pool: List[Dict] = None) -> List[str]:
        """
        根据题材关键词找到相关股票

        Args:
            theme_keywords: 题材关键词列表
            stock_pool: 股票池数据

        Returns:
            List[str]: 相关股票代码列表
        """
        try:
            if not theme_keywords:
                return []

            related_stocks = []

            # 如果没有提供股票池，返回空列表
            if not stock_pool:
                logger.warning("未提供股票池数据，无法匹配相关股票")
                return []

            # 遍历股票池，匹配关键词
            for stock in stock_pool:
                stock_name = stock.get('name', '')
                stock_code = stock.get('symbol', '')
                industry = stock.get('industry', '')

                # 检查股票名称和行业是否包含题材关键词
                match_score = 0
                for keyword in theme_keywords:
                    if keyword in stock_name:
                        match_score += 2
                    if keyword in industry:
                        match_score += 1

                # 如果匹配度足够高，加入相关股票列表
                if match_score >= 1:
                    related_stocks.append(stock_code)

            return related_stocks[:20]  # 最多返回20只相关股票

        except Exception as e:
            logger.error(f"查找相关股票失败: {e}")
            return []

    def update_theme_data(self, theme: Dict, new_news: List[Dict]) -> Dict:
        """
        更新题材数据

        Args:
            theme: 现有题材数据
            new_news: 新的新闻数据

        Returns:
            Dict: 更新后的题材数据
        """
        try:
            if not new_news:
                return theme

            # 提取新新闻的关键词
            all_keywords = Counter()
            type_scores = defaultdict(float)

            for news in new_news:
                title = news.get('title', '')
                content = news.get('content', '')
                text = f"{title} {content}"

                keywords = self.extract_keywords_from_text(text)
                all_keywords.update(keywords)

                # 更新类型得分
                news_type_scores = self.classify_theme_type(keywords, text)
                for t_type, score in news_type_scores.items():
                    type_scores[t_type] += score

            # 更新题材数据
            old_news_count = theme.get('news_count', 0)
            new_news_count = len(new_news)
            total_news_count = old_news_count + new_news_count

            # 加权平均更新类型得分
            for t_type in ['policy', 'industry', 'event', 'concept']:
                old_score = theme.get(f'{t_type}_factor', 0) * old_news_count
                new_score = type_scores[t_type]
                theme[f'{t_type}_factor'] = (old_score + new_score) / total_news_count

            # 更新其他字段
            theme['news_count'] = total_news_count
            theme['last_updated'] = datetime.now()

            # 更新关键词（合并并保留最频繁的）
            old_keywords = theme.get('keywords', [])
            combined_keywords = Counter(old_keywords)
            combined_keywords.update(all_keywords.keys())
            theme['keywords'] = [kw for kw, _ in combined_keywords.most_common(10)]

            # 重新计算题材评分
            theme['theme_score'] = self.calculate_theme_score(theme)

            return theme

        except Exception as e:
            logger.error(f"更新题材数据失败: {e}")
            return theme

    def get_theme_ranking(self, limit: int = 20) -> List[Dict]:
        """
        获取题材排行榜

        Args:
            limit: 返回题材数量限制

        Returns:
            List[Dict]: 排序后的题材列表
        """
        try:
            themes = list(self.theme_history.values())

            if not themes:
                return []

            # 转换为字典格式
            theme_list = []
            for theme_data in themes:
                theme_dict = {
                    'theme_id': theme_data.theme_id,
                    'theme_name': theme_data.theme_name,
                    'theme_type': theme_data.theme_type,
                    'theme_score': theme_data.theme_score,
                    'lifecycle_stage': theme_data.lifecycle_stage,
                    'news_count': theme_data.news_count,
                    'related_stocks_count': len(theme_data.related_stocks),
                    'heat_trend': theme_data.heat_trend,
                    'last_updated': theme_data.last_updated.isoformat()
                }
                theme_list.append(theme_dict)

            # 按评分排序
            theme_list.sort(key=lambda x: x['theme_score'], reverse=True)

            return theme_list[:limit]

        except Exception as e:
            logger.error(f"获取题材排行榜失败: {e}")
            return []

    def analyze_theme_heat_trend(self, theme_id: str, days: int = 7) -> str:
        """
        分析题材热度趋势

        Args:
            theme_id: 题材ID
            days: 分析天数

        Returns:
            str: 热度趋势 (rising/stable/declining)
        """
        try:
            # 这里应该从历史数据中分析趋势
            # 简化实现：基于最近更新时间判断
            if theme_id not in self.theme_history:
                return "stable"

            theme = self.theme_history[theme_id]
            hours_since_update = (datetime.now() - theme.last_updated).total_seconds() / 3600

            if hours_since_update < 6:
                return "rising"
            elif hours_since_update < 24:
                return "stable"
            else:
                return "declining"

        except Exception as e:
            logger.error(f"分析题材热度趋势失败: {e}")
            return "stable"

    async def save_theme_data_to_file(self, themes: List[Dict], date_str: str = None):
        """保存题材数据到文件"""
        try:
            from backend.data.file_storage import file_storage

            if not themes:
                return False

            if date_str is None:
                date_str = datetime.now().strftime("%Y%m%d")

            # 转换为DataFrame
            df = pd.DataFrame(themes)

            # 保存到文件
            success = await file_storage.save_theme_data(df, date_str)

            if success:
                logger.info(f"题材数据已保存到文件: {date_str}, 共{len(themes)}个题材")
            else:
                logger.error(f"保存题材数据失败: {date_str}")

            return success

        except Exception as e:
            logger.error(f"保存题材数据到文件失败: {e}")
            return False

    async def load_theme_data_from_file(self, date_str: str = None) -> List[Dict]:
        """从文件加载题材数据"""
        try:
            from backend.data.file_storage import file_storage

            if date_str is None:
                date_str = datetime.now().strftime("%Y%m%d")

            df = await file_storage.load_theme_data(date_str)

            if df is not None and not df.empty:
                themes = df.to_dict('records')
                logger.info(f"从文件加载题材数据: {date_str}, 共{len(themes)}个题材")
                return themes
            else:
                logger.warning(f"未找到题材数据文件: {date_str}")
                return []

        except Exception as e:
            logger.error(f"从文件加载题材数据失败: {e}")
            return []


# 全局题材识别引擎实例
theme_engine = ThemeIdentificationEngine()
