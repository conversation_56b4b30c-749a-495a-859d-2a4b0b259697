"""
技术指标分析引擎
包含技术指标计算、突破形态识别和资金流向分析
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from loguru import logger
from dataclasses import dataclass

# 技术指标库
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    logger.warning("talib未安装，技术指标计算功能将受限")

from config.settings import settings


@dataclass
class TechnicalIndicators:
    """技术指标数据结构"""
    symbol: str
    timestamp: datetime
    
    # 趋势指标
    macd: Optional[float]
    macd_signal: Optional[float]
    macd_hist: Optional[float]
    
    # 动量指标
    rsi: Optional[float]
    k: Optional[float]
    d: Optional[float]
    j: Optional[float]
    
    # 布林带
    bb_upper: Optional[float]
    bb_middle: Optional[float]
    bb_lower: Optional[float]
    bb_position: Optional[float]  # 价格在布林带中的位置
    
    # 成交量指标
    volume_ratio: Optional[float]  # 量比
    obv: Optional[float]  # 能量潮
    
    # 移动平均线
    ma5: Optional[float]
    ma10: Optional[float]
    ma20: Optional[float]
    ma60: Optional[float]
    
    # 综合评分
    technical_score: float
    signal_strength: float


@dataclass
class BreakoutPattern:
    """突破形态数据结构"""
    symbol: str
    pattern_type: str  # breakout_up, breakout_down, consolidation
    confidence: float
    price_breakout: bool
    volume_confirmation: bool
    ma_support: bool
    resistance_level: Optional[float]
    support_level: Optional[float]
    target_price: Optional[float]
    stop_loss: Optional[float]


@dataclass
class FundFlowData:
    """资金流向数据结构"""
    symbol: str
    timestamp: datetime
    net_inflow: float
    big_order_ratio: float
    super_large_inflow: float
    large_inflow: float
    medium_inflow: float
    small_inflow: float
    flow_trend: str  # positive, negative, neutral
    flow_strength: float


class TechnicalAnalysisEngine:
    """技术指标分析引擎"""
    
    def __init__(self):
        # 技术指标权重配置
        self.indicator_weights = {
            'trend': 0.3,      # 趋势指标权重
            'momentum': 0.25,  # 动量指标权重
            'volume': 0.25,    # 成交量指标权重
            'pattern': 0.2     # 形态指标权重
        }
        
        # 信号阈值
        self.signal_thresholds = {
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'macd_bullish': 0,
            'volume_surge': 2.0,
            'breakout_volume': 1.5
        }
        
        logger.info("技术分析引擎初始化完成")
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> Dict:
        """
        计算主要技术指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            Dict: 技术指标字典
        """
        try:
            if df.empty or len(df) < 20:
                logger.warning("数据不足，无法计算技术指标")
                return {}
            
            # 确保数据列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    logger.error(f"缺少必要的数据列: {col}")
                    return {}
            
            close = df['close'].values.astype(float)
            high = df['high'].values.astype(float)
            low = df['low'].values.astype(float)
            volume = df['volume'].values.astype(float)
            
            indicators = {}
            
            if TALIB_AVAILABLE:
                # 使用talib计算指标
                indicators.update(self._calculate_with_talib(df))
            else:
                # 使用自定义方法计算指标
                indicators.update(self._calculate_without_talib(df))
            
            # 计算量比
            if len(volume) >= 20:
                recent_volume = volume[-1]
                avg_volume = np.mean(volume[-20:])
                indicators['volume_ratio'] = recent_volume / avg_volume if avg_volume > 0 else 1.0
            else:
                indicators['volume_ratio'] = 1.0
            
            # 计算移动平均线
            indicators.update(self._calculate_moving_averages(close))
            
            # 计算综合技术评分
            indicators['technical_score'] = self._calculate_technical_score(indicators)
            indicators['signal_strength'] = self._calculate_signal_strength(indicators)
            
            logger.debug(f"技术指标计算完成，共{len(indicators)}个指标")
            return indicators
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {}
    
    def _calculate_with_talib(self, df: pd.DataFrame) -> Dict:
        """使用talib计算技术指标"""
        try:
            close = df['close'].values.astype(float)
            high = df['high'].values.astype(float)
            low = df['low'].values.astype(float)
            volume = df['volume'].values.astype(float)
            
            indicators = {}
            
            # MACD
            macd, macd_signal, macd_hist = talib.MACD(close)
            indicators.update({
                'macd': macd[-1] if not np.isnan(macd[-1]) else None,
                'macd_signal': macd_signal[-1] if not np.isnan(macd_signal[-1]) else None,
                'macd_hist': macd_hist[-1] if not np.isnan(macd_hist[-1]) else None
            })
            
            # RSI
            rsi = talib.RSI(close, timeperiod=14)
            indicators['rsi'] = rsi[-1] if not np.isnan(rsi[-1]) else None
            
            # 布林带
            bb_upper, bb_middle, bb_lower = talib.BBANDS(close)
            indicators.update({
                'bb_upper': bb_upper[-1] if not np.isnan(bb_upper[-1]) else None,
                'bb_middle': bb_middle[-1] if not np.isnan(bb_middle[-1]) else None,
                'bb_lower': bb_lower[-1] if not np.isnan(bb_lower[-1]) else None
            })
            
            # 布林带位置
            if indicators['bb_upper'] and indicators['bb_lower']:
                bb_range = indicators['bb_upper'] - indicators['bb_lower']
                if bb_range > 0:
                    indicators['bb_position'] = (close[-1] - indicators['bb_lower']) / bb_range
                else:
                    indicators['bb_position'] = 0.5
            
            # KDJ
            k, d = talib.STOCH(high, low, close)
            indicators.update({
                'k': k[-1] if not np.isnan(k[-1]) else None,
                'd': d[-1] if not np.isnan(d[-1]) else None
            })
            
            if indicators['k'] and indicators['d']:
                indicators['j'] = 3 * indicators['k'] - 2 * indicators['d']
            
            # OBV (能量潮)
            obv = talib.OBV(close, volume)
            indicators['obv'] = obv[-1] if not np.isnan(obv[-1]) else None
            
            return indicators
            
        except Exception as e:
            logger.error(f"使用talib计算指标失败: {e}")
            return {}
    
    def _calculate_without_talib(self, df: pd.DataFrame) -> Dict:
        """不使用talib的简化指标计算"""
        try:
            close = df['close'].values.astype(float)
            high = df['high'].values.astype(float)
            low = df['low'].values.astype(float)
            
            indicators = {}
            
            # 简化RSI计算
            if len(close) >= 14:
                delta = np.diff(close)
                gain = np.where(delta > 0, delta, 0)
                loss = np.where(delta < 0, -delta, 0)
                
                avg_gain = np.mean(gain[-14:])
                avg_loss = np.mean(loss[-14:])
                
                if avg_loss > 0:
                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))
                    indicators['rsi'] = rsi
                else:
                    indicators['rsi'] = 100
            
            # 简化MACD计算
            if len(close) >= 26:
                ema12 = self._calculate_ema(close, 12)
                ema26 = self._calculate_ema(close, 26)
                macd = ema12 - ema26
                macd_signal = self._calculate_ema(np.array([macd]), 9)[0]
                
                indicators.update({
                    'macd': macd,
                    'macd_signal': macd_signal,
                    'macd_hist': macd - macd_signal
                })
            
            # 简化布林带计算
            if len(close) >= 20:
                ma20 = np.mean(close[-20:])
                std20 = np.std(close[-20:])
                
                indicators.update({
                    'bb_upper': ma20 + 2 * std20,
                    'bb_middle': ma20,
                    'bb_lower': ma20 - 2 * std20
                })
                
                bb_range = indicators['bb_upper'] - indicators['bb_lower']
                if bb_range > 0:
                    indicators['bb_position'] = (close[-1] - indicators['bb_lower']) / bb_range
                else:
                    indicators['bb_position'] = 0.5
            
            return indicators
            
        except Exception as e:
            logger.error(f"简化指标计算失败: {e}")
            return {}
    
    def _calculate_ema(self, data: np.ndarray, period: int) -> float:
        """计算指数移动平均"""
        try:
            if len(data) < period:
                return np.mean(data)
            
            alpha = 2.0 / (period + 1)
            ema = data[0]
            
            for price in data[1:]:
                ema = alpha * price + (1 - alpha) * ema
            
            return ema
            
        except Exception as e:
            logger.error(f"计算EMA失败: {e}")
            return 0.0
    
    def _calculate_moving_averages(self, close: np.ndarray) -> Dict:
        """计算移动平均线"""
        try:
            mas = {}
            
            for period in [5, 10, 20, 60]:
                if len(close) >= period:
                    mas[f'ma{period}'] = np.mean(close[-period:])
                else:
                    mas[f'ma{period}'] = None
            
            return mas
            
        except Exception as e:
            logger.error(f"计算移动平均线失败: {e}")
            return {}
    
    def _calculate_technical_score(self, indicators: Dict) -> float:
        """计算综合技术评分"""
        try:
            score = 0.0
            total_weight = 0.0
            
            # 趋势评分
            trend_score = 0.0
            if indicators.get('macd_hist') is not None:
                if indicators['macd_hist'] > 0:
                    trend_score += 0.5
                if indicators.get('macd') and indicators.get('macd_signal'):
                    if indicators['macd'] > indicators['macd_signal']:
                        trend_score += 0.5
            
            # 动量评分
            momentum_score = 0.0
            if indicators.get('rsi') is not None:
                rsi = indicators['rsi']
                if 30 < rsi < 70:  # 正常范围
                    momentum_score += 0.3
                elif rsi > 50:  # 偏强
                    momentum_score += 0.5
                
                if rsi < 30:  # 超卖，可能反弹
                    momentum_score += 0.7
            
            # 成交量评分
            volume_score = 0.0
            if indicators.get('volume_ratio') is not None:
                vol_ratio = indicators['volume_ratio']
                if vol_ratio > 1.5:  # 放量
                    volume_score += 0.6
                elif vol_ratio > 1.0:
                    volume_score += 0.3
            
            # 加权计算总分
            score = (
                trend_score * self.indicator_weights['trend'] +
                momentum_score * self.indicator_weights['momentum'] +
                volume_score * self.indicator_weights['volume']
            )
            
            return min(max(score, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"计算技术评分失败: {e}")
            return 0.0
    
    def _calculate_signal_strength(self, indicators: Dict) -> float:
        """计算信号强度"""
        try:
            strength = 0.0
            
            # MACD信号强度
            if indicators.get('macd_hist') is not None:
                strength += abs(indicators['macd_hist']) * 0.3
            
            # RSI偏离程度
            if indicators.get('rsi') is not None:
                rsi = indicators['rsi']
                if rsi < 30 or rsi > 70:
                    strength += abs(rsi - 50) / 50 * 0.3
            
            # 成交量放大程度
            if indicators.get('volume_ratio') is not None:
                vol_ratio = indicators['volume_ratio']
                if vol_ratio > 1.0:
                    strength += min((vol_ratio - 1.0) * 0.4, 0.4)
            
            return min(strength, 1.0)
            
        except Exception as e:
            logger.error(f"计算信号强度失败: {e}")
            return 0.0

    def check_breakout_pattern(self, df: pd.DataFrame) -> BreakoutPattern:
        """
        检查突破形态

        Args:
            df: 包含OHLCV数据的DataFrame

        Returns:
            BreakoutPattern: 突破形态数据
        """
        try:
            if df.empty or len(df) < 20:
                return BreakoutPattern(
                    symbol=df.get('symbol', 'Unknown')[0] if 'symbol' in df.columns else 'Unknown',
                    pattern_type='consolidation',
                    confidence=0.0,
                    price_breakout=False,
                    volume_confirmation=False,
                    ma_support=False,
                    resistance_level=None,
                    support_level=None,
                    target_price=None,
                    stop_loss=None
                )

            close = df['close'].values.astype(float)
            high = df['high'].values.astype(float)
            low = df['low'].values.astype(float)
            volume = df['volume'].values.astype(float)

            symbol = df['symbol'].iloc[0] if 'symbol' in df.columns else 'Unknown'

            # 计算移动平均线
            ma20 = np.mean(close[-20:])
            ma5 = np.mean(close[-5:])

            # 检查价格突破
            recent_high = np.max(high[-20:-1])  # 排除当日
            recent_low = np.min(low[-20:-1])
            current_price = close[-1]

            price_breakout_up = current_price > recent_high
            price_breakout_down = current_price < recent_low

            # 检查成交量确认
            avg_volume = np.mean(volume[-10:])
            current_volume = volume[-1]
            volume_confirmation = current_volume > avg_volume * self.signal_thresholds['breakout_volume']

            # 检查均线支撑
            ma_support = current_price > ma20

            # 确定突破类型和置信度
            if price_breakout_up and volume_confirmation and ma_support:
                pattern_type = 'breakout_up'
                confidence = 0.8
                target_price = current_price + (current_price - recent_low) * 0.618  # 黄金分割
                stop_loss = recent_high * 0.95
            elif price_breakout_down and volume_confirmation:
                pattern_type = 'breakout_down'
                confidence = 0.7
                target_price = current_price - (recent_high - current_price) * 0.618
                stop_loss = recent_low * 1.05
            elif price_breakout_up and ma_support:
                pattern_type = 'breakout_up'
                confidence = 0.6
                target_price = current_price * 1.1
                stop_loss = ma20
            else:
                pattern_type = 'consolidation'
                confidence = 0.3
                target_price = None
                stop_loss = None

            return BreakoutPattern(
                symbol=symbol,
                pattern_type=pattern_type,
                confidence=confidence,
                price_breakout=price_breakout_up or price_breakout_down,
                volume_confirmation=volume_confirmation,
                ma_support=ma_support,
                resistance_level=recent_high,
                support_level=recent_low,
                target_price=target_price,
                stop_loss=stop_loss
            )

        except Exception as e:
            logger.error(f"检查突破形态失败: {e}")
            return BreakoutPattern(
                symbol='Unknown',
                pattern_type='consolidation',
                confidence=0.0,
                price_breakout=False,
                volume_confirmation=False,
                ma_support=False,
                resistance_level=None,
                support_level=None,
                target_price=None,
                stop_loss=None
            )

    def analyze_fund_flow(self, df: pd.DataFrame, level2_data: Dict = None) -> FundFlowData:
        """
        资金流向分析

        Args:
            df: 包含OHLCV数据的DataFrame
            level2_data: Level-2数据（可选）

        Returns:
            FundFlowData: 资金流向数据
        """
        try:
            if df.empty:
                return FundFlowData(
                    symbol='Unknown',
                    timestamp=datetime.now(),
                    net_inflow=0.0,
                    big_order_ratio=0.0,
                    super_large_inflow=0.0,
                    large_inflow=0.0,
                    medium_inflow=0.0,
                    small_inflow=0.0,
                    flow_trend='neutral',
                    flow_strength=0.0
                )

            close = df['close'].values.astype(float)
            volume = df['volume'].values.astype(float)
            amount = df['amount'].values.astype(float) if 'amount' in df.columns else volume * close

            symbol = df['symbol'].iloc[0] if 'symbol' in df.columns else 'Unknown'

            # 简化的资金流向计算（基于价量关系）
            price_changes = np.diff(close)
            volume_weighted_flow = 0.0

            for i in range(len(price_changes)):
                if price_changes[i] > 0:
                    # 价格上涨，视为资金流入
                    volume_weighted_flow += volume[i + 1] * amount[i + 1] / 1e8  # 转换为亿元
                elif price_changes[i] < 0:
                    # 价格下跌，视为资金流出
                    volume_weighted_flow -= volume[i + 1] * amount[i + 1] / 1e8

            # 大单占比估算（基于成交量突增）
            avg_volume = np.mean(volume)
            big_volume_threshold = avg_volume * 2
            big_order_count = len(volume[volume > big_volume_threshold])
            big_order_ratio = big_order_count / len(volume) if len(volume) > 0 else 0

            # 资金流向分类（简化）
            total_amount = np.sum(amount) / 1e8
            super_large_inflow = volume_weighted_flow * 0.3 if volume_weighted_flow > 0 else 0
            large_inflow = volume_weighted_flow * 0.3 if volume_weighted_flow > 0 else 0
            medium_inflow = volume_weighted_flow * 0.25 if volume_weighted_flow > 0 else 0
            small_inflow = volume_weighted_flow * 0.15 if volume_weighted_flow > 0 else 0

            # 确定流向趋势
            if volume_weighted_flow > total_amount * 0.02:
                flow_trend = 'positive'
            elif volume_weighted_flow < -total_amount * 0.02:
                flow_trend = 'negative'
            else:
                flow_trend = 'neutral'

            # 计算流向强度
            flow_strength = min(abs(volume_weighted_flow) / (total_amount + 1e-6), 1.0)

            return FundFlowData(
                symbol=symbol,
                timestamp=datetime.now(),
                net_inflow=volume_weighted_flow,
                big_order_ratio=big_order_ratio,
                super_large_inflow=super_large_inflow,
                large_inflow=large_inflow,
                medium_inflow=medium_inflow,
                small_inflow=small_inflow,
                flow_trend=flow_trend,
                flow_strength=flow_strength
            )

        except Exception as e:
            logger.error(f"分析资金流向失败: {e}")
            return FundFlowData(
                symbol='Unknown',
                timestamp=datetime.now(),
                net_inflow=0.0,
                big_order_ratio=0.0,
                super_large_inflow=0.0,
                large_inflow=0.0,
                medium_inflow=0.0,
                small_inflow=0.0,
                flow_trend='neutral',
                flow_strength=0.0
            )


# 全局技术分析引擎实例
technical_engine = TechnicalAnalysisEngine()
