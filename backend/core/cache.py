"""
Redis缓存管理器
"""
import json
import pickle
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timedelta
import asyncio
from loguru import logger

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("redis未安装，缓存功能将不可用")

from config.settings import settings


class CacheManager:
    """Redis缓存管理器"""
    
    def __init__(self):
        self.redis_client = None
        self.is_connected = False
    
    async def init_redis(self):
        """初始化Redis连接"""
        if not REDIS_AVAILABLE:
            logger.warning("Redis不可用，跳过初始化")
            return False
        
        try:
            self.redis_client = redis.Redis(
                host=settings.data_storage.redis_host,
                port=settings.data_storage.redis_port,
                password=settings.data_storage.redis_password or None,
                db=settings.data_storage.redis_db,
                decode_responses=False,  # 保持二进制模式以支持pickle
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # 测试连接
            await self.redis_client.ping()
            self.is_connected = True
            logger.info("Redis连接成功")
            return True
            
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self.is_connected = False
            return False
    
    async def close(self):
        """关闭Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            self.is_connected = False
            logger.info("Redis连接已关闭")
    
    def _serialize_value(self, value: Any) -> bytes:
        """序列化值"""
        try:
            # 尝试JSON序列化（更快，更小）
            if isinstance(value, (dict, list, str, int, float, bool)) or value is None:
                return json.dumps(value, ensure_ascii=False, default=str).encode('utf-8')
            else:
                # 使用pickle序列化复杂对象
                return pickle.dumps(value)
        except Exception:
            # 回退到pickle
            return pickle.dumps(value)
    
    def _deserialize_value(self, data: bytes) -> Any:
        """反序列化值"""
        try:
            # 尝试JSON反序列化
            return json.loads(data.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError):
            # 回退到pickle
            return pickle.loads(data)
    
    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置缓存值"""
        if not self.is_connected:
            return False
        
        try:
            serialized_value = self._serialize_value(value)
            
            if expire:
                result = await self.redis_client.setex(key, expire, serialized_value)
            else:
                result = await self.redis_client.set(key, serialized_value)
            
            return bool(result)
            
        except Exception as e:
            logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self.is_connected:
            return None
        
        try:
            data = await self.redis_client.get(key)
            if data is None:
                return None
            
            return self._deserialize_value(data)
            
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {e}")
            return None
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        if not self.is_connected:
            return False
        
        try:
            result = await self.redis_client.delete(key)
            return bool(result)
            
        except Exception as e:
            logger.error(f"删除缓存失败 {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        if not self.is_connected:
            return False
        
        try:
            result = await self.redis_client.exists(key)
            return bool(result)
            
        except Exception as e:
            logger.error(f"检查缓存存在性失败 {key}: {e}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """设置缓存过期时间"""
        if not self.is_connected:
            return False
        
        try:
            result = await self.redis_client.expire(key, seconds)
            return bool(result)
            
        except Exception as e:
            logger.error(f"设置缓存过期时间失败 {key}: {e}")
            return False
    
    async def ttl(self, key: str) -> int:
        """获取缓存剩余时间"""
        if not self.is_connected:
            return -1
        
        try:
            return await self.redis_client.ttl(key)
            
        except Exception as e:
            logger.error(f"获取缓存TTL失败 {key}: {e}")
            return -1
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配的键列表"""
        if not self.is_connected:
            return []
        
        try:
            keys = await self.redis_client.keys(pattern)
            return [key.decode('utf-8') if isinstance(key, bytes) else key for key in keys]
            
        except Exception as e:
            logger.error(f"获取键列表失败 {pattern}: {e}")
            return []
    
    async def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的所有键"""
        if not self.is_connected:
            return 0
        
        try:
            keys = await self.keys(pattern)
            if keys:
                deleted = await self.redis_client.delete(*keys)
                logger.info(f"清除缓存: 模式={pattern}, 删除={deleted}个键")
                return deleted
            return 0
            
        except Exception as e:
            logger.error(f"清除缓存失败 {pattern}: {e}")
            return 0
    
    # 业务相关的缓存方法
    async def cache_market_sentiment(self, sentiment_data: Dict, expire: int = 300) -> bool:
        """缓存市场情绪数据（5分钟过期）"""
        key = f"sentiment:{datetime.now().strftime('%Y%m%d')}"
        return await self.set(key, sentiment_data, expire)
    
    async def get_market_sentiment(self) -> Optional[Dict]:
        """获取市场情绪缓存"""
        key = f"sentiment:{datetime.now().strftime('%Y%m%d')}"
        return await self.get(key)
    
    async def cache_stock_realtime(self, symbol: str, stock_data: Dict, expire: int = 60) -> bool:
        """缓存股票实时数据（1分钟过期）"""
        key = f"stock:realtime:{symbol}"
        return await self.set(key, stock_data, expire)
    
    async def get_stock_realtime(self, symbol: str) -> Optional[Dict]:
        """获取股票实时数据缓存"""
        key = f"stock:realtime:{symbol}"
        return await self.get(key)
    
    async def cache_theme_data(self, theme_data: List[Dict], expire: int = 1800) -> bool:
        """缓存题材数据（30分钟过期）"""
        key = f"themes:{datetime.now().strftime('%Y%m%d')}"
        return await self.set(key, theme_data, expire)
    
    async def get_theme_data(self) -> Optional[List[Dict]]:
        """获取题材数据缓存"""
        key = f"themes:{datetime.now().strftime('%Y%m%d')}"
        return await self.get(key)
    
    async def cache_trading_signals(self, signals: List[Dict], expire: int = 180) -> bool:
        """缓存交易信号（3分钟过期）"""
        key = f"signals:{datetime.now().strftime('%Y%m%d_%H%M')}"
        return await self.set(key, signals, expire)
    
    async def get_trading_signals(self) -> Optional[List[Dict]]:
        """获取交易信号缓存"""
        key = f"signals:{datetime.now().strftime('%Y%m%d_%H%M')}"
        return await self.get(key)
    
    async def cache_positions(self, positions: List[Dict], expire: int = 60) -> bool:
        """缓存持仓数据（1分钟过期）"""
        key = "positions:current"
        return await self.set(key, positions, expire)
    
    async def get_positions(self) -> Optional[List[Dict]]:
        """获取持仓数据缓存"""
        key = "positions:current"
        return await self.get(key)
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        if not self.is_connected:
            return {
                "status": "disconnected",
                "error": "Redis未连接"
            }
        
        try:
            # 测试基本操作
            test_key = "health_check_test"
            test_value = {"timestamp": datetime.now().isoformat()}
            
            await self.set(test_key, test_value, 10)
            retrieved_value = await self.get(test_key)
            await self.delete(test_key)
            
            if retrieved_value == test_value:
                return {
                    "status": "healthy",
                    "connected": True,
                    "test_passed": True
                }
            else:
                return {
                    "status": "unhealthy",
                    "connected": True,
                    "test_passed": False,
                    "error": "数据读写测试失败"
                }
                
        except Exception as e:
            return {
                "status": "unhealthy",
                "connected": self.is_connected,
                "error": str(e)
            }


# 全局缓存管理器实例
cache_manager = CacheManager()


async def init_redis():
    """初始化Redis连接"""
    return await cache_manager.init_redis()
