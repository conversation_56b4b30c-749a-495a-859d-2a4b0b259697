"""
龙头股识别引擎
基于多维度指标识别和评分题材龙头股
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from loguru import logger
from dataclasses import dataclass
from collections import defaultdict

from config.settings import settings


@dataclass
class LeaderStockData:
    """龙头股数据结构"""
    symbol: str
    name: str
    theme_name: str
    leader_score: float
    return_rank: int
    volume_rank: int
    continuous_boards: int
    concept_purity: float
    market_cap: float
    market_cap_score: float
    price: float
    change_pct: float
    volume: float
    amount: float
    pe_ratio: Optional[float]
    pb_ratio: Optional[float]
    industry: str
    is_leader: bool
    confidence_level: str  # high, medium, low
    last_updated: datetime


class LeaderStockIdentification:
    """龙头股识别引擎"""
    
    def __init__(self):
        # 从配置获取龙头股权重参数
        self.weights = settings.strategy.leader_weights
        
        # 龙头股评分阈值
        self.leader_thresholds = {
            'high_confidence': 0.8,    # 高置信度龙头
            'medium_confidence': 0.6,  # 中等置信度龙头
            'low_confidence': 0.4      # 低置信度龙头
        }
        
        # 市值适中范围（亿元）
        self.optimal_market_cap_range = (50, 200)
        
        # 历史龙头股数据
        self.leader_history: Dict[str, List[LeaderStockData]] = defaultdict(list)
        
        logger.info("龙头股识别引擎初始化完成")
    
    def calculate_leader_score(self, stock_data: Dict) -> float:
        """
        计算龙头股评分
        龙头评分 = (涨幅排名×0.3) + (成交额排名×0.25) + (连板高度×0.2) + (概念纯正度×0.15) + (流通市值适中度×0.1)
        
        Args:
            stock_data: 股票数据字典
            
        Returns:
            float: 龙头股评分 (0-1)
        """
        try:
            # 获取基础数据
            return_rank = stock_data.get('return_rank', 999)
            total_stocks = stock_data.get('total_stocks', 1000)
            volume_rank = stock_data.get('volume_rank', 999)
            continuous_boards = stock_data.get('continuous_boards', 0)
            concept_purity = stock_data.get('concept_match_degree', 0.0)
            market_cap = stock_data.get('market_cap', 0.0)
            
            # 计算各项得分
            return_score = self._normalize_rank(return_rank, total_stocks)
            volume_score = self._normalize_rank(volume_rank, total_stocks)
            board_score = min(continuous_boards / 5.0, 1.0)  # 标准化到0-1，5连板为满分
            concept_score = min(max(concept_purity, 0.0), 1.0)  # 确保在0-1范围内
            market_cap_score = self._calculate_market_cap_score(market_cap)
            
            # 加权计算总分
            leader_score = (
                return_score * self.weights['return_rank'] +
                volume_score * self.weights['volume_rank'] +
                board_score * self.weights['board_height'] +
                concept_score * self.weights['concept_purity'] +
                market_cap_score * self.weights['market_cap']
            )
            
            logger.debug(f"龙头评分计算: 涨幅{return_score:.3f}, 成交额{volume_score:.3f}, "
                        f"连板{board_score:.3f}, 纯正度{concept_score:.3f}, "
                        f"市值{market_cap_score:.3f}, 总分{leader_score:.3f}")
            
            return leader_score
            
        except Exception as e:
            logger.error(f"计算龙头股评分失败: {e}")
            return 0.0
    
    def _normalize_rank(self, rank: int, total: int) -> float:
        """
        将排名转换为0-1分数（排名越高分数越高）
        
        Args:
            rank: 排名（1为最高）
            total: 总数
            
        Returns:
            float: 归一化分数
        """
        if total <= 0:
            return 0.0
        return max(0.0, (total - rank + 1) / total)
    
    def _calculate_market_cap_score(self, market_cap: float) -> float:
        """
        计算流通市值适中度得分
        
        Args:
            market_cap: 流通市值（亿元）
            
        Returns:
            float: 市值得分 (0-1)
        """
        try:
            min_cap, max_cap = self.optimal_market_cap_range
            
            if min_cap <= market_cap <= max_cap:
                return 1.0  # 最佳范围，满分
            elif market_cap < min_cap:
                # 市值过小，线性递减
                return max(0.1, market_cap / min_cap)
            else:
                # 市值过大，反比例递减
                return max(0.1, max_cap / market_cap)
                
        except Exception as e:
            logger.error(f"计算市值得分失败: {e}")
            return 0.5
    
    def calculate_concept_purity(self, stock_symbol: str, theme_keywords: List[str], 
                                stock_info: Dict) -> float:
        """
        计算股票的概念纯正度
        
        Args:
            stock_symbol: 股票代码
            theme_keywords: 题材关键词列表
            stock_info: 股票基础信息
            
        Returns:
            float: 概念纯正度 (0-1)
        """
        try:
            if not theme_keywords:
                return 0.0
            
            stock_name = stock_info.get('name', '')
            industry = stock_info.get('industry', '')
            business_scope = stock_info.get('business_scope', '')
            
            # 计算匹配度
            match_score = 0.0
            total_keywords = len(theme_keywords)
            
            for keyword in theme_keywords:
                # 股票名称匹配（权重最高）
                if keyword in stock_name:
                    match_score += 3.0
                
                # 行业匹配
                if keyword in industry:
                    match_score += 2.0
                
                # 业务范围匹配
                if keyword in business_scope:
                    match_score += 1.0
            
            # 归一化到0-1
            max_possible_score = total_keywords * 3.0  # 最高分是所有关键词都在名称中匹配
            purity_score = min(match_score / max_possible_score, 1.0) if max_possible_score > 0 else 0.0
            
            return purity_score
            
        except Exception as e:
            logger.error(f"计算概念纯正度失败: {e}")
            return 0.0
    
    def identify_theme_leaders(self, theme_name: str, candidate_stocks: List[Dict], 
                              theme_keywords: List[str] = None, top_n: int = 5) -> List[LeaderStockData]:
        """
        识别题材龙头股
        
        Args:
            theme_name: 题材名称
            candidate_stocks: 候选股票列表
            theme_keywords: 题材关键词
            top_n: 返回前N只龙头股
            
        Returns:
            List[LeaderStockData]: 龙头股列表
        """
        try:
            if not candidate_stocks:
                logger.warning(f"题材{theme_name}没有候选股票")
                return []
            
            leaders = []
            total_stocks = len(candidate_stocks)
            
            # 按涨幅和成交额排序，计算排名
            sorted_by_return = sorted(candidate_stocks, 
                                    key=lambda x: x.get('change_pct', 0), reverse=True)
            sorted_by_volume = sorted(candidate_stocks, 
                                    key=lambda x: x.get('amount', 0), reverse=True)
            
            # 创建排名映射
            return_ranks = {stock['symbol']: i + 1 for i, stock in enumerate(sorted_by_return)}
            volume_ranks = {stock['symbol']: i + 1 for i, stock in enumerate(sorted_by_volume)}
            
            # 计算每只股票的龙头评分
            for stock in candidate_stocks:
                symbol = stock['symbol']
                
                # 准备评分数据
                stock_data = {
                    'return_rank': return_ranks.get(symbol, total_stocks),
                    'volume_rank': volume_ranks.get(symbol, total_stocks),
                    'total_stocks': total_stocks,
                    'continuous_boards': stock.get('continuous_boards', 0),
                    'market_cap': stock.get('market_cap', 0.0),
                    'concept_match_degree': self.calculate_concept_purity(
                        symbol, theme_keywords or [], stock
                    )
                }
                
                # 计算龙头评分
                leader_score = self.calculate_leader_score(stock_data)
                
                # 确定置信度等级
                confidence_level = self._get_confidence_level(leader_score)
                
                # 判断是否为龙头
                is_leader = leader_score >= self.leader_thresholds['low_confidence']
                
                # 创建龙头股数据对象
                leader_data = LeaderStockData(
                    symbol=symbol,
                    name=stock.get('name', ''),
                    theme_name=theme_name,
                    leader_score=leader_score,
                    return_rank=return_ranks.get(symbol, total_stocks),
                    volume_rank=volume_ranks.get(symbol, total_stocks),
                    continuous_boards=stock.get('continuous_boards', 0),
                    concept_purity=stock_data['concept_match_degree'],
                    market_cap=stock.get('market_cap', 0.0),
                    market_cap_score=self._calculate_market_cap_score(stock.get('market_cap', 0.0)),
                    price=stock.get('price', 0.0),
                    change_pct=stock.get('change_pct', 0.0),
                    volume=stock.get('volume', 0.0),
                    amount=stock.get('amount', 0.0),
                    pe_ratio=stock.get('pe_ratio'),
                    pb_ratio=stock.get('pb_ratio'),
                    industry=stock.get('industry', ''),
                    is_leader=is_leader,
                    confidence_level=confidence_level,
                    last_updated=datetime.now()
                )
                
                leaders.append(leader_data)
            
            # 按龙头评分排序
            leaders.sort(key=lambda x: x.leader_score, reverse=True)
            
            # 更新历史记录
            self.leader_history[theme_name] = leaders[:top_n]
            
            logger.info(f"识别题材{theme_name}龙头股完成，共{len(leaders)}只候选股票，"
                       f"识别出{len([l for l in leaders if l.is_leader])}只龙头股")
            
            return leaders[:top_n]
            
        except Exception as e:
            logger.error(f"识别题材龙头股失败: {e}")
            return []
    
    def _get_confidence_level(self, leader_score: float) -> str:
        """
        根据评分确定置信度等级
        
        Args:
            leader_score: 龙头评分
            
        Returns:
            str: 置信度等级
        """
        if leader_score >= self.leader_thresholds['high_confidence']:
            return "high"
        elif leader_score >= self.leader_thresholds['medium_confidence']:
            return "medium"
        elif leader_score >= self.leader_thresholds['low_confidence']:
            return "low"
        else:
            return "none"
    
    def get_leader_ranking(self, theme_name: str = None, confidence_filter: str = None) -> List[Dict]:
        """
        获取龙头股排行榜
        
        Args:
            theme_name: 题材名称过滤
            confidence_filter: 置信度过滤 (high/medium/low)
            
        Returns:
            List[Dict]: 龙头股排行榜
        """
        try:
            all_leaders = []
            
            # 收集所有龙头股数据
            for t_name, leaders in self.leader_history.items():
                if theme_name and t_name != theme_name:
                    continue
                
                for leader in leaders:
                    if confidence_filter and leader.confidence_level != confidence_filter:
                        continue
                    
                    if not leader.is_leader:
                        continue
                    
                    leader_dict = {
                        'symbol': leader.symbol,
                        'name': leader.name,
                        'theme_name': leader.theme_name,
                        'leader_score': leader.leader_score,
                        'confidence_level': leader.confidence_level,
                        'return_rank': leader.return_rank,
                        'volume_rank': leader.volume_rank,
                        'continuous_boards': leader.continuous_boards,
                        'concept_purity': leader.concept_purity,
                        'market_cap': leader.market_cap,
                        'price': leader.price,
                        'change_pct': leader.change_pct,
                        'industry': leader.industry,
                        'last_updated': leader.last_updated.isoformat()
                    }
                    all_leaders.append(leader_dict)
            
            # 按龙头评分排序
            all_leaders.sort(key=lambda x: x['leader_score'], reverse=True)
            
            return all_leaders

        except Exception as e:
            logger.error(f"获取龙头股排行榜失败: {e}")
            return []

    def analyze_leader_stability(self, symbol: str, days: int = 5) -> Dict:
        """
        分析龙头股稳定性

        Args:
            symbol: 股票代码
            days: 分析天数

        Returns:
            Dict: 稳定性分析结果
        """
        try:
            # 查找该股票在各题材中的历史表现
            leader_records = []

            for theme_name, leaders in self.leader_history.items():
                for leader in leaders:
                    if leader.symbol == symbol:
                        leader_records.append(leader)

            if not leader_records:
                return {
                    "status": "no_data",
                    "message": f"未找到股票{symbol}的龙头记录"
                }

            # 计算稳定性指标
            scores = [record.leader_score for record in leader_records]
            avg_score = np.mean(scores)
            score_std = np.std(scores)

            # 置信度分布
            confidence_counts = {}
            for record in leader_records:
                conf = record.confidence_level
                confidence_counts[conf] = confidence_counts.get(conf, 0) + 1

            # 题材覆盖度
            themes_covered = len(set(record.theme_name for record in leader_records))

            # 稳定性评级
            if score_std < 0.1 and avg_score > 0.7:
                stability_rating = "excellent"
            elif score_std < 0.2 and avg_score > 0.6:
                stability_rating = "good"
            elif score_std < 0.3 and avg_score > 0.5:
                stability_rating = "fair"
            else:
                stability_rating = "poor"

            return {
                "status": "success",
                "symbol": symbol,
                "stability_analysis": {
                    "avg_leader_score": avg_score,
                    "score_volatility": score_std,
                    "stability_rating": stability_rating,
                    "themes_covered": themes_covered,
                    "total_records": len(leader_records),
                    "confidence_distribution": confidence_counts
                }
            }

        except Exception as e:
            logger.error(f"分析龙头股稳定性失败: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def compare_leaders(self, symbols: List[str]) -> Dict:
        """
        比较多只龙头股

        Args:
            symbols: 股票代码列表

        Returns:
            Dict: 比较结果
        """
        try:
            comparison_data = []

            for symbol in symbols:
                # 获取该股票的最新龙头数据
                latest_record = None
                for theme_name, leaders in self.leader_history.items():
                    for leader in leaders:
                        if leader.symbol == symbol:
                            if latest_record is None or leader.last_updated > latest_record.last_updated:
                                latest_record = leader

                if latest_record:
                    comparison_data.append({
                        'symbol': symbol,
                        'name': latest_record.name,
                        'leader_score': latest_record.leader_score,
                        'confidence_level': latest_record.confidence_level,
                        'theme_name': latest_record.theme_name,
                        'continuous_boards': latest_record.continuous_boards,
                        'concept_purity': latest_record.concept_purity,
                        'market_cap': latest_record.market_cap,
                        'change_pct': latest_record.change_pct
                    })
                else:
                    comparison_data.append({
                        'symbol': symbol,
                        'name': 'Unknown',
                        'leader_score': 0.0,
                        'confidence_level': 'none',
                        'theme_name': 'None',
                        'continuous_boards': 0,
                        'concept_purity': 0.0,
                        'market_cap': 0.0,
                        'change_pct': 0.0
                    })

            # 按龙头评分排序
            comparison_data.sort(key=lambda x: x['leader_score'], reverse=True)

            return {
                "status": "success",
                "comparison_data": comparison_data,
                "best_leader": comparison_data[0] if comparison_data else None
            }

        except Exception as e:
            logger.error(f"比较龙头股失败: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def get_theme_leader_summary(self, theme_name: str) -> Dict:
        """
        获取题材龙头股摘要

        Args:
            theme_name: 题材名称

        Returns:
            Dict: 题材龙头摘要
        """
        try:
            if theme_name not in self.leader_history:
                return {
                    "status": "no_data",
                    "message": f"未找到题材{theme_name}的龙头数据"
                }

            leaders = self.leader_history[theme_name]

            if not leaders:
                return {
                    "status": "no_data",
                    "message": f"题材{theme_name}暂无龙头股"
                }

            # 统计信息
            total_candidates = len(leaders)
            actual_leaders = [l for l in leaders if l.is_leader]
            high_confidence = [l for l in actual_leaders if l.confidence_level == 'high']
            medium_confidence = [l for l in actual_leaders if l.confidence_level == 'medium']
            low_confidence = [l for l in actual_leaders if l.confidence_level == 'low']

            # 最强龙头
            top_leader = leaders[0] if leaders else None

            # 平均指标
            avg_score = np.mean([l.leader_score for l in actual_leaders]) if actual_leaders else 0
            avg_boards = np.mean([l.continuous_boards for l in actual_leaders]) if actual_leaders else 0
            avg_purity = np.mean([l.concept_purity for l in actual_leaders]) if actual_leaders else 0

            return {
                "status": "success",
                "theme_name": theme_name,
                "summary": {
                    "total_candidates": total_candidates,
                    "leader_count": len(actual_leaders),
                    "confidence_distribution": {
                        "high": len(high_confidence),
                        "medium": len(medium_confidence),
                        "low": len(low_confidence)
                    },
                    "top_leader": {
                        "symbol": top_leader.symbol,
                        "name": top_leader.name,
                        "leader_score": top_leader.leader_score,
                        "confidence_level": top_leader.confidence_level
                    } if top_leader else None,
                    "average_metrics": {
                        "avg_leader_score": avg_score,
                        "avg_continuous_boards": avg_boards,
                        "avg_concept_purity": avg_purity
                    }
                }
            }

        except Exception as e:
            logger.error(f"获取题材龙头摘要失败: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    async def save_leader_data_to_file(self, theme_name: str, date_str: str = None):
        """保存龙头股数据到文件"""
        try:
            from backend.data.file_storage import file_storage

            if theme_name not in self.leader_history:
                return False

            if date_str is None:
                date_str = datetime.now().strftime("%Y%m%d")

            leaders = self.leader_history[theme_name]

            # 转换为DataFrame
            leader_records = []
            for leader in leaders:
                record = {
                    'symbol': leader.symbol,
                    'name': leader.name,
                    'theme_name': leader.theme_name,
                    'leader_score': leader.leader_score,
                    'return_rank': leader.return_rank,
                    'volume_rank': leader.volume_rank,
                    'continuous_boards': leader.continuous_boards,
                    'concept_purity': leader.concept_purity,
                    'market_cap': leader.market_cap,
                    'market_cap_score': leader.market_cap_score,
                    'price': leader.price,
                    'change_pct': leader.change_pct,
                    'volume': leader.volume,
                    'amount': leader.amount,
                    'pe_ratio': leader.pe_ratio,
                    'pb_ratio': leader.pb_ratio,
                    'industry': leader.industry,
                    'is_leader': leader.is_leader,
                    'confidence_level': leader.confidence_level,
                    'last_updated': leader.last_updated
                }
                leader_records.append(record)

            df = pd.DataFrame(leader_records)

            # 保存到文件
            filename = f"leaders_{theme_name}_{date_str}"
            success = await file_storage.save_dataframe(df, "signal_data", filename)

            if success:
                logger.info(f"龙头股数据已保存: {theme_name}, {date_str}")
            else:
                logger.error(f"保存龙头股数据失败: {theme_name}, {date_str}")

            return success

        except Exception as e:
            logger.error(f"保存龙头股数据到文件失败: {e}")
            return False


# 全局龙头股识别引擎实例
leader_engine = LeaderStockIdentification()
