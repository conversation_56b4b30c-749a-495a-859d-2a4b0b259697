"""
AIQuant7 主应用入口
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import uvicorn
from loguru import logger

from config.settings import settings
from backend.api.routes import api_router
from backend.core.cache import init_redis
from backend.data.file_storage import file_storage


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("🚀 启动 AIQuant7 系统...")
    
    try:
        # 初始化文件存储系统
        logger.info("✅ 文件存储系统初始化完成")

        # 初始化Redis缓存（可选）
        redis_success = await init_redis()
        if redis_success:
            logger.info("✅ Redis缓存初始化完成")
        else:
            logger.warning("⚠️ Redis缓存初始化失败，将使用内存缓存")

        logger.info("🎉 AIQuant7 系统启动成功!")

    except Exception as e:
        logger.error(f"❌ 系统启动失败: {e}")
        raise
    
    yield
    
    # 关闭时清理
    logger.info("🛑 正在关闭 AIQuant7 系统...")
    logger.info("👋 AIQuant7 系统已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    
    app = FastAPI(
        title=settings.app_name,
        version=settings.version,
        description=settings.description,
        lifespan=lifespan,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
    )
    
    # 添加中间件
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.api.cors_origins,
        allow_credentials=True,
        allow_methods=settings.api.cors_methods,
        allow_headers=settings.api.cors_headers,
    )
    
    # 注册路由
    app.include_router(api_router, prefix="/api/v1")
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {
            "status": "healthy",
            "app": settings.app_name,
            "version": settings.version,
            "environment": settings.environment
        }
    
    # 根路径
    @app.get("/")
    async def root():
        """根路径"""
        return {
            "message": f"欢迎使用 {settings.app_name}",
            "version": settings.version,
            "docs": "/docs",
            "health": "/health"
        }
    
    # 全局异常处理
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        """全局异常处理器"""
        logger.error(f"未处理的异常: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "内部服务器错误",
                "message": str(exc) if settings.debug else "服务暂时不可用"
            }
        )
    
    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    """直接运行时的入口"""
    uvicorn.run(
        "main:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.reload,
        log_level=settings.logging.level.lower(),
        access_log=True,
    )
