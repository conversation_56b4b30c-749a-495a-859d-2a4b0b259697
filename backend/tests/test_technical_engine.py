"""
技术指标分析引擎测试
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from backend.core.technical.technical_engine import TechnicalAnalysisEngine, BreakoutPattern, FundFlowData


class TestTechnicalAnalysisEngine:
    """技术分析引擎测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = TechnicalAnalysisEngine()
        
        # 创建测试数据
        dates = pd.date_range(start='2023-01-01', periods=50, freq='D')
        np.random.seed(42)  # 固定随机种子
        
        # 生成模拟价格数据
        base_price = 100
        price_changes = np.random.normal(0, 0.02, 50)  # 2%的日波动
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        self.test_df = pd.DataFrame({
            'date': dates,
            'open': [p * 0.99 for p in prices],
            'high': [p * 1.02 for p in prices],
            'low': [p * 0.98 for p in prices],
            'close': prices,
            'volume': np.random.randint(1000000, 5000000, 50),
            'amount': [p * v for p, v in zip(prices, np.random.randint(1000000, 5000000, 50))],
            'symbol': ['TEST001'] * 50
        })
    
    def test_calculate_technical_indicators(self):
        """测试技术指标计算"""
        indicators = self.engine.calculate_technical_indicators(self.test_df)
        
        assert isinstance(indicators, dict)
        
        # 检查基本指标是否存在
        expected_indicators = [
            'volume_ratio', 'ma5', 'ma10', 'ma20', 'ma60',
            'technical_score', 'signal_strength'
        ]
        
        for indicator in expected_indicators:
            assert indicator in indicators
        
        # 检查数值范围
        assert 0.0 <= indicators['technical_score'] <= 1.0
        assert 0.0 <= indicators['signal_strength'] <= 1.0
        assert indicators['volume_ratio'] > 0
    
    def test_calculate_moving_averages(self):
        """测试移动平均线计算"""
        close = self.test_df['close'].values
        mas = self.engine._calculate_moving_averages(close)
        
        assert isinstance(mas, dict)
        assert 'ma5' in mas
        assert 'ma10' in mas
        assert 'ma20' in mas
        assert 'ma60' in mas
        
        # 验证计算正确性
        expected_ma5 = np.mean(close[-5:])
        assert abs(mas['ma5'] - expected_ma5) < 1e-6
        
        expected_ma20 = np.mean(close[-20:])
        assert abs(mas['ma20'] - expected_ma20) < 1e-6
    
    def test_calculate_ema(self):
        """测试指数移动平均计算"""
        data = np.array([100, 102, 101, 103, 105])
        ema = self.engine._calculate_ema(data, 3)
        
        assert isinstance(ema, float)
        assert ema > 0
        
        # 测试边界情况
        short_data = np.array([100])
        ema_short = self.engine._calculate_ema(short_data, 5)
        assert ema_short == 100.0
    
    def test_calculate_technical_score(self):
        """测试技术评分计算"""
        # 创建测试指标
        indicators = {
            'macd_hist': 0.5,
            'macd': 1.0,
            'macd_signal': 0.8,
            'rsi': 60,
            'volume_ratio': 2.0
        }
        
        score = self.engine._calculate_technical_score(indicators)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score > 0  # 应该有正分
    
    def test_calculate_signal_strength(self):
        """测试信号强度计算"""
        indicators = {
            'macd_hist': 0.3,
            'rsi': 25,  # 超卖
            'volume_ratio': 2.5  # 放量
        }
        
        strength = self.engine._calculate_signal_strength(indicators)
        
        assert isinstance(strength, float)
        assert 0.0 <= strength <= 1.0
        assert strength > 0  # 应该有信号强度
    
    def test_check_breakout_pattern(self):
        """测试突破形态识别"""
        # 创建突破数据
        breakout_df = self.test_df.copy()
        
        # 模拟向上突破
        breakout_df.loc[breakout_df.index[-1], 'close'] = breakout_df['high'].iloc[-20:-1].max() * 1.02
        breakout_df.loc[breakout_df.index[-1], 'volume'] = breakout_df['volume'].mean() * 2
        
        pattern = self.engine.check_breakout_pattern(breakout_df)
        
        assert isinstance(pattern, BreakoutPattern)
        assert pattern.symbol == 'TEST001'
        assert pattern.pattern_type in ['breakout_up', 'breakout_down', 'consolidation']
        assert 0.0 <= pattern.confidence <= 1.0
        assert isinstance(pattern.price_breakout, bool)
        assert isinstance(pattern.volume_confirmation, bool)
        assert isinstance(pattern.ma_support, bool)
    
    def test_analyze_fund_flow(self):
        """测试资金流向分析"""
        fund_flow = self.engine.analyze_fund_flow(self.test_df)
        
        assert isinstance(fund_flow, FundFlowData)
        assert fund_flow.symbol == 'TEST001'
        assert isinstance(fund_flow.net_inflow, float)
        assert 0.0 <= fund_flow.big_order_ratio <= 1.0
        assert fund_flow.flow_trend in ['positive', 'negative', 'neutral']
        assert 0.0 <= fund_flow.flow_strength <= 1.0
    
    def test_empty_dataframe_handling(self):
        """测试空数据框处理"""
        empty_df = pd.DataFrame()
        
        # 技术指标计算
        indicators = self.engine.calculate_technical_indicators(empty_df)
        assert indicators == {}
        
        # 突破形态识别
        pattern = self.engine.check_breakout_pattern(empty_df)
        assert pattern.confidence == 0.0
        
        # 资金流向分析
        fund_flow = self.engine.analyze_fund_flow(empty_df)
        assert fund_flow.net_inflow == 0.0
    
    def test_insufficient_data_handling(self):
        """测试数据不足情况处理"""
        short_df = self.test_df.head(5)  # 只有5天数据
        
        indicators = self.engine.calculate_technical_indicators(short_df)
        assert indicators == {}  # 数据不足，返回空字典
    
    def test_missing_columns_handling(self):
        """测试缺失列处理"""
        incomplete_df = self.test_df[['date', 'close']].copy()  # 缺少必要列
        
        indicators = self.engine.calculate_technical_indicators(incomplete_df)
        assert indicators == {}  # 缺少必要列，返回空字典
    
    def test_indicator_weights_configuration(self):
        """测试指标权重配置"""
        weights = self.engine.indicator_weights
        
        assert isinstance(weights, dict)
        assert 'trend' in weights
        assert 'momentum' in weights
        assert 'volume' in weights
        assert 'pattern' in weights
        
        # 权重应该是合理的数值
        for weight in weights.values():
            assert 0.0 < weight <= 1.0
    
    def test_signal_thresholds_configuration(self):
        """测试信号阈值配置"""
        thresholds = self.engine.signal_thresholds
        
        assert isinstance(thresholds, dict)
        assert 'rsi_oversold' in thresholds
        assert 'rsi_overbought' in thresholds
        assert 'volume_surge' in thresholds
        
        # 阈值应该是合理的数值
        assert thresholds['rsi_oversold'] < thresholds['rsi_overbought']
        assert thresholds['volume_surge'] > 1.0


if __name__ == "__main__":
    # 运行简单测试
    engine = TechnicalAnalysisEngine()
    
    print("测试技术分析引擎...")
    
    # 创建测试数据
    dates = pd.date_range(start='2023-01-01', periods=30, freq='D')
    prices = [100 + i + np.random.normal(0, 2) for i in range(30)]
    
    test_df = pd.DataFrame({
        'date': dates,
        'open': [p * 0.99 for p in prices],
        'high': [p * 1.02 for p in prices],
        'low': [p * 0.98 for p in prices],
        'close': prices,
        'volume': [1000000 + np.random.randint(0, 500000) for _ in range(30)],
        'amount': [p * v for p, v in zip(prices, [1000000] * 30)],
        'symbol': ['TEST001'] * 30
    })
    
    # 测试技术指标计算
    indicators = engine.calculate_technical_indicators(test_df)
    print(f"技术指标: {list(indicators.keys())}")
    print(f"技术评分: {indicators.get('technical_score', 0):.3f}")
    print(f"信号强度: {indicators.get('signal_strength', 0):.3f}")
    
    # 测试突破形态
    pattern = engine.check_breakout_pattern(test_df)
    print(f"突破形态: {pattern.pattern_type}, 置信度: {pattern.confidence:.3f}")
    
    # 测试资金流向
    fund_flow = engine.analyze_fund_flow(test_df)
    print(f"资金流向: {fund_flow.flow_trend}, 强度: {fund_flow.flow_strength:.3f}")
    
    print("测试完成！")
