"""
市场情绪计算引擎测试
"""
import pytest
import asyncio
from datetime import datetime, timedelta
from backend.core.sentiment.sentiment_engine import MarketSentimentEngine, SentimentData


class TestMarketSentimentEngine:
    """市场情绪引擎测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = MarketSentimentEngine()
    
    def test_calculate_sentiment_score(self):
        """测试情绪评分计算"""
        # 测试数据
        market_data = {
            'limit_up_count': 50,
            'limit_down_count': 10,
            'max_continuous_boards': 3,
            'volume_factor': 1.2
        }
        
        score = self.engine.calculate_sentiment_score(market_data)
        
        # 验证计算结果
        expected_score = (50 * 2) + (10 * -3) + (3 * 5)
        expected_score *= 1.2 * 1.0  # volume_factor * volume_weight
        
        assert score == expected_score
        assert score > 0  # 涨停多于跌停，应该为正值
    
    def test_get_sentiment_phase(self):
        """测试情绪周期识别"""
        # 测试不同情绪评分对应的周期
        test_cases = [
            (10, "冰点期"),
            (30, "回暖期"),
            (60, "发酵期"),
            (100, "高潮期"),
            (150, "退潮期")
        ]
        
        for score, expected_phase in test_cases:
            phase = self.engine.get_sentiment_phase(score)
            assert phase == expected_phase
    
    def test_calculate_market_temperature(self):
        """测试市场温度计算"""
        historical_scores = [20, 30, 40, 50, 60, 70, 80, 90, 100]
        
        # 测试不同当前评分的温度
        test_cases = [
            (25, 25.0),  # 低分位
            (50, 50.0),  # 中位数
            (85, 75.0),  # 高分位
        ]
        
        for current_score, expected_temp in test_cases:
            temp = self.engine.calculate_market_temperature(current_score, historical_scores)
            assert temp == expected_temp
    
    def test_analyze_trend_direction(self):
        """测试趋势方向分析"""
        # 上升趋势
        upward_scores = [30, 35, 40, 45, 50]
        trend = self.engine.analyze_trend_direction(upward_scores)
        assert trend == "上升"
        
        # 下降趋势
        downward_scores = [50, 45, 40, 35, 30]
        trend = self.engine.analyze_trend_direction(downward_scores)
        assert trend == "下降"
        
        # 震荡趋势
        sideways_scores = [40, 42, 41, 43, 42]
        trend = self.engine.analyze_trend_direction(sideways_scores)
        assert trend == "震荡"
    
    def test_calculate_volatility_index(self):
        """测试波动率指数计算"""
        # 低波动
        low_vol_scores = [50, 51, 49, 52, 48]
        vol_index = self.engine.calculate_volatility_index(low_vol_scores)
        assert vol_index < 20
        
        # 高波动
        high_vol_scores = [30, 70, 20, 80, 10]
        vol_index = self.engine.calculate_volatility_index(high_vol_scores)
        assert vol_index > 50
    
    def test_process_market_data(self):
        """测试完整的市场数据处理"""
        market_data = {
            'timestamp': datetime.now(),
            'limit_up_count': 40,
            'limit_down_count': 5,
            'max_continuous_boards': 2,
            'volume_factor': 1.0
        }
        
        sentiment_data = self.engine.process_market_data(market_data)
        
        # 验证返回的数据结构
        assert isinstance(sentiment_data, SentimentData)
        assert sentiment_data.sentiment_score > 0
        assert sentiment_data.sentiment_phase in ["冰点期", "回暖期", "发酵期", "高潮期", "退潮期"]
        assert 0 <= sentiment_data.market_temperature <= 100
        assert sentiment_data.trend_direction in ["上升", "下降", "震荡"]
        assert sentiment_data.volatility_index >= 0
    
    def test_is_good_entry_timing(self):
        """测试入场时机判断"""
        # 创建一个好的入场时机场景
        good_market_data = {
            'timestamp': datetime.now(),
            'limit_up_count': 30,
            'limit_down_count': 5,
            'max_continuous_boards': 2
        }
        
        # 处理数据以建立历史
        self.engine.process_market_data(good_market_data)
        
        is_good, reason = self.engine.is_good_entry_timing()
        
        # 验证结果
        assert isinstance(is_good, bool)
        assert isinstance(reason, str)
        assert len(reason) > 0
    
    def test_get_recommended_position(self):
        """测试推荐仓位计算"""
        # 创建测试数据
        market_data = {
            'timestamp': datetime.now(),
            'limit_up_count': 25,
            'limit_down_count': 8,
            'max_continuous_boards': 1
        }
        
        # 处理数据
        self.engine.process_market_data(market_data)
        
        # 获取推荐仓位
        position = self.engine._get_recommended_position()
        
        # 验证仓位在合理范围内
        assert 0.0 <= position <= 1.0
    
    @pytest.mark.asyncio
    async def test_save_and_load_sentiment_data(self):
        """测试情绪数据的保存和加载"""
        # 创建测试数据
        market_data = {
            'timestamp': datetime.now(),
            'limit_up_count': 35,
            'limit_down_count': 12,
            'max_continuous_boards': 3
        }
        
        # 处理数据
        sentiment_data = self.engine.process_market_data(market_data)
        
        # 保存数据
        date_str = datetime.now().strftime("%Y%m%d")
        success = await self.engine.save_sentiment_data_to_file(sentiment_data, date_str)
        
        # 验证保存成功
        assert success is True
        
        # 清空历史数据
        self.engine.sentiment_history.clear()
        
        # 加载数据
        await self.engine.load_historical_sentiment_data(1)
        
        # 验证加载成功
        assert len(self.engine.sentiment_history) > 0
    
    def test_get_sentiment_summary(self):
        """测试情绪摘要获取"""
        # 创建一些历史数据
        for i in range(5):
            market_data = {
                'timestamp': datetime.now() - timedelta(days=i),
                'limit_up_count': 20 + i * 5,
                'limit_down_count': 10 - i,
                'max_continuous_boards': 1 + i
            }
            self.engine.process_market_data(market_data)
        
        # 获取摘要
        summary = self.engine.get_sentiment_summary()
        
        # 验证摘要结构
        assert summary["status"] == "success"
        assert "current" in summary
        assert "statistics" in summary
        assert "market_conditions" in summary
        
        # 验证当前数据
        current = summary["current"]
        assert "sentiment_score" in current
        assert "sentiment_phase" in current
        assert "market_temperature" in current
        assert "trend_direction" in current
        assert "volatility_index" in current


if __name__ == "__main__":
    # 运行简单测试
    engine = MarketSentimentEngine()
    
    # 测试基本功能
    test_data = {
        'limit_up_count': 45,
        'limit_down_count': 8,
        'max_continuous_boards': 2,
        'timestamp': datetime.now()
    }
    
    print("测试市场情绪计算引擎...")
    
    # 计算情绪评分
    score = engine.calculate_sentiment_score(test_data)
    print(f"情绪评分: {score}")
    
    # 识别情绪周期
    phase = engine.get_sentiment_phase(score)
    print(f"情绪周期: {phase}")
    
    # 处理完整数据
    sentiment_data = engine.process_market_data(test_data)
    print(f"完整分析: {sentiment_data}")
    
    # 获取摘要
    summary = engine.get_sentiment_summary()
    print(f"情绪摘要: {summary}")
    
    print("测试完成！")
