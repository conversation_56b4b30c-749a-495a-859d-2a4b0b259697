"""
仓位管理器测试
"""
import pytest
from datetime import datetime, timedelta
from backend.core.position.position_manager import PositionManager, Position, PositionStatus
from backend.core.signal.signal_engine import TradingSignal, SignalType, SignalStrength


class TestPositionManager:
    """仓位管理器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.manager = PositionManager(initial_capital=1000000.0)
        
        # 创建测试交易信号
        self.test_signal = TradingSignal(
            symbol="TEST001",
            signal_type=SignalType.BUY,
            signal_strength=SignalStrength.STRONG,
            confidence=0.8,
            price=100.0,
            target_price=110.0,
            stop_loss=95.0,
            sentiment_score=0.8,
            theme_score=0.7,
            leader_score=0.6,
            technical_score=0.5,
            composite_score=0.7,
            signal_sources=["test"],
            reason="测试信号",
            risk_level="medium",
            max_position_ratio=0.1,
            timestamp=datetime.now(),
            valid_until=datetime.now() + timedelta(hours=4),
            theme_name="测试题材",
            is_leader=True,
            market_sentiment_phase="发酵期"
        )
    
    def test_initialization(self):
        """测试初始化"""
        assert self.manager.initial_capital == 1000000.0
        assert self.manager.current_capital == 1000000.0
        assert self.manager.cash_balance == 1000000.0
        assert len(self.manager.positions) == 0
        assert len(self.manager.closed_positions) == 0
    
    def test_calculate_position_size(self):
        """测试仓位大小计算"""
        current_price = 100.0
        quantity = self.manager.calculate_position_size(self.test_signal, current_price)
        
        assert isinstance(quantity, float)
        assert quantity >= 0
        assert quantity % 100 == 0  # 应该是100的整数倍
        
        # 验证仓位价值在合理范围内
        position_value = quantity * current_price
        max_expected_value = self.manager.current_capital * self.test_signal.max_position_ratio
        assert position_value <= max_expected_value * 1.2  # 允许一定的调整范围
    
    def test_calculate_base_position_value(self):
        """测试基础仓位价值计算"""
        base_value = self.manager._calculate_base_position_value(self.test_signal)
        
        assert isinstance(base_value, float)
        assert base_value >= 0
        assert base_value <= self.manager.cash_balance
        
        # 验证基于信号参数的计算
        expected_ratio = self.test_signal.max_position_ratio * self.test_signal.confidence
        expected_value = self.manager.current_capital * expected_ratio
        assert base_value <= expected_value * 1.5  # 允许调整因子
    
    def test_apply_risk_adjustment(self):
        """测试风险调整"""
        base_value = 100000.0
        adjusted_value = self.manager._apply_risk_adjustment(base_value, self.test_signal)
        
        assert isinstance(adjusted_value, float)
        assert adjusted_value > 0
        
        # 中等风险应该有所调整
        assert adjusted_value <= base_value
    
    def test_apply_liquidity_adjustment(self):
        """测试流动性调整"""
        base_value = 100000.0
        
        # 测试主板股票
        adjusted_value_main = self.manager._apply_liquidity_adjustment(base_value, "600000")
        assert adjusted_value_main == base_value  # 主板股票不调整
        
        # 测试创业板股票
        adjusted_value_gem = self.manager._apply_liquidity_adjustment(base_value, "300001")
        assert adjusted_value_gem <= base_value  # 创业板有所调整
    
    def test_apply_correlation_adjustment(self):
        """测试相关性调整"""
        base_value = 100000.0
        
        # 无题材时不调整
        adjusted_value_none = self.manager._apply_correlation_adjustment(base_value, None)
        assert adjusted_value_none == base_value
        
        # 新题材时不调整
        adjusted_value_new = self.manager._apply_correlation_adjustment(base_value, "新题材")
        assert adjusted_value_new == base_value
    
    def test_open_position_success(self):
        """测试成功开仓"""
        current_price = 100.0
        initial_cash = self.manager.cash_balance
        
        success = self.manager.open_position(self.test_signal, current_price)
        
        assert success is True
        assert "TEST001" in self.manager.positions
        
        position = self.manager.positions["TEST001"]
        assert position.symbol == "TEST001"
        assert position.avg_price == current_price
        assert position.status == PositionStatus.ACTIVE
        assert position.quantity > 0
        
        # 验证资金变化
        assert self.manager.cash_balance < initial_cash
        
        # 验证交易记录
        assert len(self.manager.trade_history) == 1
        trade = self.manager.trade_history[0]
        assert trade['action'] == 'buy'
        assert trade['symbol'] == "TEST001"
    
    def test_open_position_insufficient_funds(self):
        """测试资金不足时开仓"""
        # 设置很高的价格，导致资金不足
        high_price_signal = self.test_signal
        high_price_signal.max_position_ratio = 2.0  # 超过100%仓位
        
        success = self.manager.open_position(high_price_signal, 100.0)
        
        # 应该失败或者调整到合理仓位
        if not success:
            assert "TEST001" not in self.manager.positions
        else:
            # 如果成功，验证仓位在合理范围内
            position = self.manager.positions["TEST001"]
            assert position.market_value <= self.manager.cash_balance * 0.95
    
    def test_open_position_duplicate(self):
        """测试重复开仓"""
        current_price = 100.0
        
        # 第一次开仓
        success1 = self.manager.open_position(self.test_signal, current_price)
        assert success1 is True
        
        # 第二次开仓同一股票
        success2 = self.manager.open_position(self.test_signal, current_price)
        assert success2 is False  # 应该失败
    
    def test_close_position_success(self):
        """测试成功平仓"""
        # 先开仓
        open_price = 100.0
        self.manager.open_position(self.test_signal, open_price)
        
        # 平仓
        close_price = 110.0
        initial_cash = self.manager.cash_balance
        
        success = self.manager.close_position("TEST001", close_price, "测试平仓")
        
        assert success is True
        assert "TEST001" not in self.manager.positions
        assert len(self.manager.closed_positions) == 1
        
        closed_position = self.manager.closed_positions[0]
        assert closed_position.status == PositionStatus.CLOSED
        assert closed_position.unrealized_pnl > 0  # 应该盈利
        
        # 验证资金增加
        assert self.manager.cash_balance > initial_cash
        
        # 验证交易记录
        assert len(self.manager.trade_history) == 2  # 买入和卖出
        sell_trade = self.manager.trade_history[1]
        assert sell_trade['action'] == 'sell'
    
    def test_close_position_not_found(self):
        """测试平仓不存在的持仓"""
        success = self.manager.close_position("NOTFOUND", 100.0, "测试")
        assert success is False
    
    def test_update_positions(self):
        """测试更新持仓"""
        # 先开仓
        self.manager.open_position(self.test_signal, 100.0)
        
        # 更新价格
        price_data = {"TEST001": 105.0}
        initial_capital = self.manager.current_capital
        
        self.manager.update_positions(price_data)
        
        position = self.manager.positions["TEST001"]
        assert position.current_price == 105.0
        assert position.unrealized_pnl > 0  # 应该盈利
        assert position.holding_days >= 0
        
        # 验证总资产更新
        assert self.manager.current_capital > initial_capital
    
    def test_check_risk_controls(self):
        """测试风险控制检查"""
        # 开仓
        self.manager.open_position(self.test_signal, 100.0)
        
        # 测试止损触发
        price_data = {"TEST001": 94.0}  # 低于止损价95.0
        risk_actions = self.manager.check_risk_controls(price_data)
        
        assert len(risk_actions) > 0
        stop_loss_action = next((action for action in risk_actions if action['action'] == 'stop_loss'), None)
        assert stop_loss_action is not None
        assert stop_loss_action['symbol'] == "TEST001"
        
        # 测试止盈触发
        price_data = {"TEST001": 111.0}  # 高于止盈价110.0
        risk_actions = self.manager.check_risk_controls(price_data)
        
        take_profit_action = next((action for action in risk_actions if action['action'] == 'take_profit'), None)
        assert take_profit_action is not None
    
    def test_execute_risk_controls(self):
        """测试执行风险控制"""
        # 开仓
        self.manager.open_position(self.test_signal, 100.0)
        
        # 触发止损
        price_data = {"TEST001": 94.0}
        executed_count = self.manager.execute_risk_controls(price_data)
        
        assert executed_count > 0
        assert "TEST001" not in self.manager.positions  # 应该被平仓
        assert len(self.manager.closed_positions) == 1
    
    def test_get_portfolio_metrics(self):
        """测试获取投资组合指标"""
        # 开仓
        self.manager.open_position(self.test_signal, 100.0)
        
        # 更新价格
        price_data = {"TEST001": 105.0}
        self.manager.update_positions(price_data)
        
        metrics = self.manager.get_portfolio_metrics()
        
        assert metrics.total_value > 0
        assert metrics.total_cost == self.manager.initial_capital
        assert metrics.position_count == 1
        assert "TEST001" in metrics.position_weights
        assert metrics.theme_weights.get("测试题材", 0) > 0
    
    def test_get_position_summary(self):
        """测试获取持仓摘要"""
        # 空仓时
        summary = self.manager.get_position_summary()
        assert summary["total_positions"] == 0
        assert summary["cash_ratio"] == 1.0
        
        # 开仓后
        self.manager.open_position(self.test_signal, 100.0)
        summary = self.manager.get_position_summary()
        
        assert summary["total_positions"] == 1
        assert summary["cash_ratio"] < 1.0
        assert summary["position_ratio"] > 0
        assert "测试题材" in summary["theme_summary"]


if __name__ == "__main__":
    # 运行简单测试
    manager = PositionManager(initial_capital=1000000.0)
    
    print("测试仓位管理器...")
    
    # 创建测试信号
    test_signal = TradingSignal(
        symbol="TEST001",
        signal_type=SignalType.BUY,
        signal_strength=SignalStrength.STRONG,
        confidence=0.8,
        price=100.0,
        target_price=110.0,
        stop_loss=95.0,
        sentiment_score=0.8,
        theme_score=0.7,
        leader_score=0.6,
        technical_score=0.5,
        composite_score=0.7,
        signal_sources=["test"],
        reason="测试信号",
        risk_level="medium",
        max_position_ratio=0.1,
        timestamp=datetime.now(),
        valid_until=datetime.now() + timedelta(hours=4),
        theme_name="测试题材",
        is_leader=True,
        market_sentiment_phase="发酵期"
    )
    
    # 测试开仓
    success = manager.open_position(test_signal, 100.0)
    print(f"开仓结果: {success}")
    
    if success:
        # 测试更新持仓
        price_data = {"TEST001": 105.0}
        manager.update_positions(price_data)
        
        # 获取持仓摘要
        summary = manager.get_position_summary()
        print(f"持仓摘要: {summary}")
        
        # 获取投资组合指标
        metrics = manager.get_portfolio_metrics()
        print(f"投资组合指标: 总价值={metrics.total_value:,.2f}, 盈亏={metrics.total_pnl:,.2f}")
        
        # 测试风险控制
        risk_price_data = {"TEST001": 94.0}  # 触发止损
        executed = manager.execute_risk_controls(risk_price_data)
        print(f"风险控制执行: {executed}个操作")
    
    print("测试完成！")
