"""
龙头股识别引擎测试
"""
import pytest
import asyncio
from datetime import datetime, timedelta
import pandas as pd
from backend.core.leader.leader_engine import LeaderStockIdentification, LeaderStockData


class TestLeaderStockIdentification:
    """龙头股识别引擎测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = LeaderStockIdentification()
    
    def test_calculate_leader_score(self):
        """测试龙头股评分计算"""
        stock_data = {
            'return_rank': 1,      # 涨幅第1名
            'volume_rank': 2,      # 成交额第2名
            'total_stocks': 100,   # 总共100只股票
            'continuous_boards': 3, # 3连板
            'concept_match_degree': 0.8,  # 概念匹配度80%
            'market_cap': 100.0    # 100亿市值
        }
        
        score = self.engine.calculate_leader_score(stock_data)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score > 0.5  # 各项指标都不错，应该有较高评分
    
    def test_normalize_rank(self):
        """测试排名归一化"""
        # 测试第1名
        score1 = self.engine._normalize_rank(1, 100)
        assert score1 == 1.0
        
        # 测试第50名
        score50 = self.engine._normalize_rank(50, 100)
        assert score50 == 0.51
        
        # 测试最后一名
        score100 = self.engine._normalize_rank(100, 100)
        assert score100 == 0.01
        
        # 测试边界情况
        score_zero = self.engine._normalize_rank(1, 0)
        assert score_zero == 0.0
    
    def test_calculate_market_cap_score(self):
        """测试市值得分计算"""
        # 最佳范围内
        score_optimal = self.engine._calculate_market_cap_score(100.0)
        assert score_optimal == 1.0
        
        # 市值过小
        score_small = self.engine._calculate_market_cap_score(25.0)
        assert 0.0 < score_small < 1.0
        
        # 市值过大
        score_large = self.engine._calculate_market_cap_score(500.0)
        assert 0.0 < score_large < 1.0
        assert score_large < score_optimal
    
    def test_calculate_concept_purity(self):
        """测试概念纯正度计算"""
        theme_keywords = ["新能源", "汽车", "锂电池"]
        
        # 高匹配度股票
        high_match_stock = {
            'name': '新能源汽车科技',
            'industry': '锂电池制造',
            'business_scope': '新能源汽车锂电池研发生产'
        }
        
        purity_high = self.engine.calculate_concept_purity(
            "test001", theme_keywords, high_match_stock
        )
        
        # 低匹配度股票
        low_match_stock = {
            'name': '传统钢铁',
            'industry': '钢铁冶炼',
            'business_scope': '钢铁产品生产销售'
        }
        
        purity_low = self.engine.calculate_concept_purity(
            "test002", theme_keywords, low_match_stock
        )
        
        assert 0.0 <= purity_high <= 1.0
        assert 0.0 <= purity_low <= 1.0
        assert purity_high > purity_low
    
    def test_identify_theme_leaders(self):
        """测试题材龙头股识别"""
        # 创建候选股票数据
        candidate_stocks = [
            {
                'symbol': '300750',
                'name': '宁德时代',
                'industry': '锂电池',
                'market_cap': 150.0,
                'price': 500.0,
                'change_pct': 8.5,
                'volume': 1000000,
                'amount': 500000000,
                'continuous_boards': 2
            },
            {
                'symbol': '002594',
                'name': '比亚迪',
                'industry': '新能源汽车',
                'market_cap': 200.0,
                'price': 250.0,
                'change_pct': 6.2,
                'volume': 800000,
                'amount': 200000000,
                'continuous_boards': 1
            },
            {
                'symbol': '000858',
                'name': '五粮液',
                'industry': '白酒',
                'market_cap': 300.0,
                'price': 180.0,
                'change_pct': 2.1,
                'volume': 500000,
                'amount': 90000000,
                'continuous_boards': 0
            }
        ]
        
        theme_keywords = ["新能源", "锂电池", "汽车"]
        
        leaders = self.engine.identify_theme_leaders(
            theme_name="新能源汽车",
            candidate_stocks=candidate_stocks,
            theme_keywords=theme_keywords,
            top_n=5
        )
        
        assert isinstance(leaders, list)
        assert len(leaders) <= 5
        
        if leaders:
            # 验证数据结构
            leader = leaders[0]
            assert isinstance(leader, LeaderStockData)
            assert leader.symbol in ['300750', '002594', '000858']
            assert leader.theme_name == "新能源汽车"
            assert 0.0 <= leader.leader_score <= 1.0
            
            # 验证排序（按评分降序）
            if len(leaders) > 1:
                assert leaders[0].leader_score >= leaders[1].leader_score
    
    def test_get_confidence_level(self):
        """测试置信度等级判断"""
        # 高置信度
        high_conf = self.engine._get_confidence_level(0.85)
        assert high_conf == "high"
        
        # 中等置信度
        medium_conf = self.engine._get_confidence_level(0.65)
        assert medium_conf == "medium"
        
        # 低置信度
        low_conf = self.engine._get_confidence_level(0.45)
        assert low_conf == "low"
        
        # 无置信度
        no_conf = self.engine._get_confidence_level(0.25)
        assert no_conf == "none"
    
    def test_get_leader_ranking(self):
        """测试龙头股排行榜"""
        # 先添加一些测试数据
        test_leaders = [
            LeaderStockData(
                symbol="300750",
                name="宁德时代",
                theme_name="新能源",
                leader_score=0.85,
                return_rank=1,
                volume_rank=1,
                continuous_boards=2,
                concept_purity=0.9,
                market_cap=150.0,
                market_cap_score=1.0,
                price=500.0,
                change_pct=8.5,
                volume=1000000,
                amount=500000000,
                pe_ratio=25.0,
                pb_ratio=3.5,
                industry="锂电池",
                is_leader=True,
                confidence_level="high",
                last_updated=datetime.now()
            ),
            LeaderStockData(
                symbol="002594",
                name="比亚迪",
                theme_name="新能源",
                leader_score=0.75,
                return_rank=2,
                volume_rank=2,
                continuous_boards=1,
                concept_purity=0.8,
                market_cap=200.0,
                market_cap_score=1.0,
                price=250.0,
                change_pct=6.2,
                volume=800000,
                amount=200000000,
                pe_ratio=30.0,
                pb_ratio=4.0,
                industry="新能源汽车",
                is_leader=True,
                confidence_level="medium",
                last_updated=datetime.now()
            )
        ]
        
        # 添加到引擎历史数据
        self.engine.leader_history["新能源"] = test_leaders
        
        # 测试获取排行榜
        ranking = self.engine.get_leader_ranking()
        
        assert isinstance(ranking, list)
        assert len(ranking) == 2
        
        # 验证排序
        assert ranking[0]['leader_score'] >= ranking[1]['leader_score']
        assert ranking[0]['symbol'] == "300750"
        
        # 测试主题过滤
        theme_ranking = self.engine.get_leader_ranking(theme_name="新能源")
        assert len(theme_ranking) == 2
        
        # 测试置信度过滤
        high_conf_ranking = self.engine.get_leader_ranking(confidence_filter="high")
        assert len(high_conf_ranking) == 1
        assert high_conf_ranking[0]['confidence_level'] == "high"
    
    def test_analyze_leader_stability(self):
        """测试龙头股稳定性分析"""
        # 添加测试数据
        test_leaders = [
            LeaderStockData(
                symbol="300750", name="宁德时代", theme_name="新能源",
                leader_score=0.85, return_rank=1, volume_rank=1,
                continuous_boards=2, concept_purity=0.9, market_cap=150.0,
                market_cap_score=1.0, price=500.0, change_pct=8.5,
                volume=1000000, amount=500000000, pe_ratio=25.0, pb_ratio=3.5,
                industry="锂电池", is_leader=True, confidence_level="high",
                last_updated=datetime.now()
            ),
            LeaderStockData(
                symbol="300750", name="宁德时代", theme_name="锂电池",
                leader_score=0.82, return_rank=1, volume_rank=2,
                continuous_boards=1, concept_purity=0.95, market_cap=150.0,
                market_cap_score=1.0, price=505.0, change_pct=7.8,
                volume=900000, amount=450000000, pe_ratio=24.0, pb_ratio=3.4,
                industry="锂电池", is_leader=True, confidence_level="high",
                last_updated=datetime.now()
            )
        ]
        
        self.engine.leader_history["新能源"] = [test_leaders[0]]
        self.engine.leader_history["锂电池"] = [test_leaders[1]]
        
        # 分析稳定性
        stability = self.engine.analyze_leader_stability("300750")
        
        assert stability["status"] == "success"
        assert "stability_analysis" in stability
        
        analysis = stability["stability_analysis"]
        assert "avg_leader_score" in analysis
        assert "score_volatility" in analysis
        assert "stability_rating" in analysis
        assert "themes_covered" in analysis
        assert analysis["themes_covered"] == 2
    
    def test_compare_leaders(self):
        """测试龙头股比较"""
        # 添加测试数据
        test_leaders = [
            LeaderStockData(
                symbol="300750", name="宁德时代", theme_name="新能源",
                leader_score=0.85, return_rank=1, volume_rank=1,
                continuous_boards=2, concept_purity=0.9, market_cap=150.0,
                market_cap_score=1.0, price=500.0, change_pct=8.5,
                volume=1000000, amount=500000000, pe_ratio=25.0, pb_ratio=3.5,
                industry="锂电池", is_leader=True, confidence_level="high",
                last_updated=datetime.now()
            ),
            LeaderStockData(
                symbol="002594", name="比亚迪", theme_name="新能源",
                leader_score=0.75, return_rank=2, volume_rank=2,
                continuous_boards=1, concept_purity=0.8, market_cap=200.0,
                market_cap_score=1.0, price=250.0, change_pct=6.2,
                volume=800000, amount=200000000, pe_ratio=30.0, pb_ratio=4.0,
                industry="新能源汽车", is_leader=True, confidence_level="medium",
                last_updated=datetime.now()
            )
        ]
        
        self.engine.leader_history["新能源"] = test_leaders
        
        # 比较龙头股
        comparison = self.engine.compare_leaders(["300750", "002594", "000001"])
        
        assert comparison["status"] == "success"
        assert "comparison_data" in comparison
        assert "best_leader" in comparison
        
        comparison_data = comparison["comparison_data"]
        assert len(comparison_data) == 3
        
        # 验证最佳龙头
        best_leader = comparison["best_leader"]
        assert best_leader["symbol"] == "300750"  # 评分最高的
    
    def test_get_theme_leader_summary(self):
        """测试题材龙头摘要"""
        # 添加测试数据
        test_leaders = [
            LeaderStockData(
                symbol="300750", name="宁德时代", theme_name="新能源",
                leader_score=0.85, return_rank=1, volume_rank=1,
                continuous_boards=2, concept_purity=0.9, market_cap=150.0,
                market_cap_score=1.0, price=500.0, change_pct=8.5,
                volume=1000000, amount=500000000, pe_ratio=25.0, pb_ratio=3.5,
                industry="锂电池", is_leader=True, confidence_level="high",
                last_updated=datetime.now()
            ),
            LeaderStockData(
                symbol="002594", name="比亚迪", theme_name="新能源",
                leader_score=0.75, return_rank=2, volume_rank=2,
                continuous_boards=1, concept_purity=0.8, market_cap=200.0,
                market_cap_score=1.0, price=250.0, change_pct=6.2,
                volume=800000, amount=200000000, pe_ratio=30.0, pb_ratio=4.0,
                industry="新能源汽车", is_leader=True, confidence_level="medium",
                last_updated=datetime.now()
            ),
            LeaderStockData(
                symbol="000858", name="五粮液", theme_name="新能源",
                leader_score=0.25, return_rank=50, volume_rank=30,
                continuous_boards=0, concept_purity=0.1, market_cap=300.0,
                market_cap_score=0.67, price=180.0, change_pct=2.1,
                volume=500000, amount=90000000, pe_ratio=35.0, pb_ratio=5.0,
                industry="白酒", is_leader=False, confidence_level="none",
                last_updated=datetime.now()
            )
        ]
        
        self.engine.leader_history["新能源"] = test_leaders
        
        # 获取摘要
        summary = self.engine.get_theme_leader_summary("新能源")
        
        assert summary["status"] == "success"
        assert "summary" in summary
        
        summary_data = summary["summary"]
        assert summary_data["total_candidates"] == 3
        assert summary_data["leader_count"] == 2  # 只有2只是龙头
        assert summary_data["confidence_distribution"]["high"] == 1
        assert summary_data["confidence_distribution"]["medium"] == 1
        assert summary_data["top_leader"]["symbol"] == "300750"


if __name__ == "__main__":
    # 运行简单测试
    engine = LeaderStockIdentification()
    
    print("测试龙头股识别引擎...")
    
    # 测试评分计算
    test_data = {
        'return_rank': 1,
        'volume_rank': 2,
        'total_stocks': 100,
        'continuous_boards': 3,
        'concept_match_degree': 0.8,
        'market_cap': 120.0
    }
    
    score = engine.calculate_leader_score(test_data)
    print(f"龙头评分: {score:.3f}")
    
    # 测试市值得分
    market_cap_scores = [
        engine._calculate_market_cap_score(cap) 
        for cap in [30, 80, 150, 300, 500]
    ]
    print(f"市值得分: {market_cap_scores}")
    
    print("测试完成！")
