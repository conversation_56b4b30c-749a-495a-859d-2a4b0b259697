"""
交易信号生成引擎测试
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from backend.core.signal.signal_engine import TradingSignalEngine, TradingSignal, SignalType, SignalStrength


class TestTradingSignalEngine:
    """交易信号引擎测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = TradingSignalEngine()
        
        # 创建测试股票数据
        dates = pd.date_range(start='2023-01-01', periods=30, freq='D')
        np.random.seed(42)
        
        base_price = 100
        prices = [base_price]
        for _ in range(29):
            change = np.random.normal(0, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1.0))
        
        self.test_stock_data = pd.DataFrame({
            'date': dates,
            'open': [p * 0.99 for p in prices],
            'high': [p * 1.02 for p in prices],
            'low': [p * 0.98 for p in prices],
            'close': prices,
            'volume': np.random.randint(1000000, 5000000, 30),
            'amount': [p * v for p, v in zip(prices, np.random.randint(1000000, 5000000, 30))],
            'symbol': ['TEST001'] * 30
        })
        
        # 创建测试市场数据
        self.test_market_data = {
            'timestamp': datetime.now(),
            'limit_up_count': 30,
            'limit_down_count': 5,
            'max_continuous_boards': 2
        }
    
    def test_normalize_sentiment_score(self):
        """测试情绪评分标准化"""
        from backend.core.sentiment.sentiment_engine import SentimentData
        
        # 创建测试情绪数据
        sentiment_data = SentimentData(
            timestamp=datetime.now(),
            limit_up_count=30,
            limit_down_count=5,
            max_continuous_boards=2,
            sentiment_score=80.0,
            sentiment_phase="发酵期",
            market_temperature=70.0,
            trend_direction="上升",
            volatility_index=30.0
        )
        
        score = self.engine._normalize_sentiment_score(sentiment_data)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
        assert score > 0.5  # 发酵期应该有较高评分
    
    def test_analyze_theme_relevance(self):
        """测试题材相关性分析"""
        theme_score, theme_name = self.engine._analyze_theme_relevance("TEST001")
        
        assert isinstance(theme_score, float)
        assert 0.0 <= theme_score <= 1.0
        assert theme_name is None or isinstance(theme_name, str)
    
    def test_analyze_leader_status(self):
        """测试龙头地位分析"""
        leader_score, is_leader = self.engine._analyze_leader_status("TEST001", "新能源")
        
        assert isinstance(leader_score, float)
        assert 0.0 <= leader_score <= 1.0
        assert isinstance(is_leader, bool)
    
    def test_calculate_composite_score(self):
        """测试综合评分计算"""
        sentiment_score = 0.8
        theme_score = 0.7
        leader_score = 0.6
        technical_score = 0.5
        
        composite_score = self.engine._calculate_composite_score(
            sentiment_score, theme_score, leader_score, technical_score
        )
        
        assert isinstance(composite_score, float)
        assert 0.0 <= composite_score <= 1.0
        
        # 验证加权计算
        expected_score = (
            sentiment_score * self.engine.module_weights['sentiment'] +
            theme_score * self.engine.module_weights['theme'] +
            leader_score * self.engine.module_weights['leader'] +
            technical_score * self.engine.module_weights['technical']
        )
        
        assert abs(composite_score - expected_score) < 1e-6
    
    def test_determine_signal_type_and_strength(self):
        """测试信号类型和强度确定"""
        from backend.core.sentiment.sentiment_engine import SentimentData
        
        sentiment_data = SentimentData(
            timestamp=datetime.now(),
            limit_up_count=30,
            limit_down_count=5,
            max_continuous_boards=2,
            sentiment_score=80.0,
            sentiment_phase="发酵期",
            market_temperature=70.0,
            trend_direction="上升",
            volatility_index=30.0
        )
        
        technical_indicators = {
            'technical_score': 0.7,
            'rsi': 60,
            'macd_hist': 0.5
        }
        
        # 测试强买入信号
        signal_type, strength = self.engine._determine_signal_type_and_strength(
            0.85, sentiment_data, technical_indicators
        )
        
        assert signal_type == SignalType.BUY
        assert strength in [SignalStrength.STRONG, SignalStrength.VERY_STRONG]
        
        # 测试卖出信号
        signal_type, strength = self.engine._determine_signal_type_and_strength(
            0.25, sentiment_data, technical_indicators
        )
        
        assert signal_type == SignalType.SELL
        assert strength == SignalStrength.WEAK
        
        # 测试持有信号
        signal_type, strength = self.engine._determine_signal_type_and_strength(
            0.45, sentiment_data, technical_indicators
        )
        
        assert signal_type == SignalType.HOLD
    
    def test_calculate_price_targets(self):
        """测试价格目标计算"""
        current_price = 100.0
        technical_indicators = {
            'bb_upper': 105.0,
            'bb_lower': 95.0,
            'ma20': 98.0
        }
        
        # 测试买入信号目标价
        target_price, stop_loss = self.engine._calculate_price_targets(
            current_price, SignalType.BUY, technical_indicators, 0.8
        )
        
        assert target_price is not None
        assert stop_loss is not None
        assert target_price > current_price
        assert stop_loss < current_price
        
        # 测试卖出信号目标价
        target_price, stop_loss = self.engine._calculate_price_targets(
            current_price, SignalType.SELL, technical_indicators, 0.8
        )
        
        assert target_price is not None
        assert stop_loss is not None
        assert target_price < current_price
        assert stop_loss > current_price
        
        # 测试持有信号
        target_price, stop_loss = self.engine._calculate_price_targets(
            current_price, SignalType.HOLD, technical_indicators, 0.5
        )
        
        assert target_price is None
        assert stop_loss is None
    
    def test_assess_risk_and_position(self):
        """测试风险评估和仓位计算"""
        from backend.core.sentiment.sentiment_engine import SentimentData
        
        sentiment_data = SentimentData(
            timestamp=datetime.now(),
            limit_up_count=30,
            limit_down_count=5,
            max_continuous_boards=2,
            sentiment_score=80.0,
            sentiment_phase="发酵期",
            market_temperature=70.0,
            trend_direction="上升",
            volatility_index=30.0
        )
        
        risk_level, max_position = self.engine._assess_risk_and_position(
            sentiment_data, 0.7, 0.8, 0.6
        )
        
        assert risk_level in ["low", "medium", "high"]
        assert isinstance(max_position, float)
        assert 0.0 < max_position <= 0.15  # 应该在合理范围内
    
    def test_generate_signal_explanation(self):
        """测试信号说明生成"""
        from backend.core.sentiment.sentiment_engine import SentimentData
        
        sentiment_data = SentimentData(
            timestamp=datetime.now(),
            limit_up_count=30,
            limit_down_count=5,
            max_continuous_boards=2,
            sentiment_score=80.0,
            sentiment_phase="发酵期",
            market_temperature=70.0,
            trend_direction="上升",
            volatility_index=30.0
        )
        
        sources, reason = self.engine._generate_signal_explanation(
            0.8, 0.7, 0.9, 0.6, sentiment_data, "新能源", True
        )
        
        assert isinstance(sources, list)
        assert isinstance(reason, str)
        assert len(reason) > 0
        
        # 应该包含相关的信号来源
        expected_sources = ["market_sentiment", "theme_hotspot", "leader_stock", "technical_analysis"]
        for source in sources:
            assert source in expected_sources
    
    def test_batch_generate_signals(self):
        """测试批量信号生成"""
        symbol_list = ["TEST001", "TEST002", "TEST003"]
        
        signals = self.engine.batch_generate_signals(symbol_list, self.test_market_data)
        
        assert isinstance(signals, list)
        assert len(signals) <= len(symbol_list)  # 可能有些股票无法生成信号
        
        # 验证信号对象
        for signal in signals:
            assert isinstance(signal, TradingSignal)
            assert signal.symbol in symbol_list
            assert isinstance(signal.signal_type, SignalType)
            assert isinstance(signal.signal_strength, SignalStrength)
            assert 0.0 <= signal.confidence <= 1.0
    
    def test_filter_signals_by_criteria(self):
        """测试信号过滤"""
        # 创建测试信号
        test_signals = []
        for i, symbol in enumerate(["TEST001", "TEST002", "TEST003"]):
            signal = TradingSignal(
                symbol=symbol,
                signal_type=SignalType.BUY if i % 2 == 0 else SignalType.SELL,
                signal_strength=SignalStrength.STRONG,
                confidence=0.7 + i * 0.1,
                price=100.0,
                target_price=110.0,
                stop_loss=95.0,
                sentiment_score=0.8,
                theme_score=0.7,
                leader_score=0.6,
                technical_score=0.5,
                composite_score=0.7 + i * 0.1,
                signal_sources=["test"],
                reason="测试信号",
                risk_level="low" if i == 0 else "medium",
                max_position_ratio=0.1,
                timestamp=datetime.now(),
                valid_until=datetime.now() + timedelta(hours=4),
                theme_name="测试题材",
                is_leader=True,
                market_sentiment_phase="发酵期"
            )
            test_signals.append(signal)
        
        # 测试置信度过滤
        filtered = self.engine.filter_signals_by_criteria(test_signals, min_confidence=0.75)
        assert len(filtered) <= len(test_signals)
        for signal in filtered:
            assert signal.confidence >= 0.75
        
        # 测试信号类型过滤
        buy_signals = self.engine.filter_signals_by_criteria(
            test_signals, signal_types=[SignalType.BUY]
        )
        for signal in buy_signals:
            assert signal.signal_type == SignalType.BUY
    
    def test_get_signal_summary(self):
        """测试信号摘要统计"""
        # 添加一些测试信号到历史记录
        test_signal = TradingSignal(
            symbol="TEST001",
            signal_type=SignalType.BUY,
            signal_strength=SignalStrength.STRONG,
            confidence=0.8,
            price=100.0,
            target_price=110.0,
            stop_loss=95.0,
            sentiment_score=0.8,
            theme_score=0.7,
            leader_score=0.6,
            technical_score=0.5,
            composite_score=0.7,
            signal_sources=["test"],
            reason="测试信号",
            risk_level="low",
            max_position_ratio=0.1,
            timestamp=datetime.now(),
            valid_until=datetime.now() + timedelta(hours=4),
            theme_name="测试题材",
            is_leader=True,
            market_sentiment_phase="发酵期"
        )
        
        self.engine.signal_history.append(test_signal)
        
        summary = self.engine.get_signal_summary()
        
        assert isinstance(summary, dict)
        assert "total_signals" in summary
        assert "signal_distribution" in summary
        assert "average_confidence" in summary
        assert "risk_distribution" in summary
        
        assert summary["total_signals"] >= 1
        assert isinstance(summary["average_confidence"], float)


if __name__ == "__main__":
    # 运行简单测试
    engine = TradingSignalEngine()
    
    print("测试交易信号生成引擎...")
    
    # 创建测试数据
    dates = pd.date_range(start='2023-01-01', periods=30, freq='D')
    prices = [100 + i + np.random.normal(0, 2) for i in range(30)]
    
    test_stock_data = pd.DataFrame({
        'date': dates,
        'open': [p * 0.99 for p in prices],
        'high': [p * 1.02 for p in prices],
        'low': [p * 0.98 for p in prices],
        'close': prices,
        'volume': [1000000 + np.random.randint(0, 500000) for _ in range(30)],
        'amount': [p * v for p, v in zip(prices, [1000000] * 30)],
        'symbol': ['TEST001'] * 30
    })
    
    test_market_data = {
        'timestamp': datetime.now(),
        'limit_up_count': 25,
        'limit_down_count': 8,
        'max_continuous_boards': 2
    }
    
    # 测试综合信号生成
    signal = engine.generate_comprehensive_signal("TEST001", test_market_data, test_stock_data)
    
    if signal:
        print(f"信号类型: {signal.signal_type.value}")
        print(f"信号强度: {signal.signal_strength.value}")
        print(f"置信度: {signal.confidence:.3f}")
        print(f"综合评分: {signal.composite_score:.3f}")
        print(f"风险等级: {signal.risk_level}")
        print(f"信号原因: {signal.reason}")
    else:
        print("未生成有效信号")
    
    # 测试批量信号生成
    symbols = ["TEST001", "TEST002", "TEST003"]
    batch_signals = engine.batch_generate_signals(symbols, test_market_data)
    print(f"批量生成信号: {len(batch_signals)}个")
    
    # 测试信号摘要
    summary = engine.get_signal_summary()
    print(f"信号摘要: {summary}")
    
    print("测试完成！")
