"""
题材识别引擎测试
"""
import pytest
import asyncio
from datetime import datetime, timedelta
import pandas as pd
from backend.core.theme.theme_engine import ThemeIdentificationEngine, ThemeData


class TestThemeIdentificationEngine:
    """题材识别引擎测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.engine = ThemeIdentificationEngine()
    
    def test_extract_keywords_from_text(self):
        """测试关键词提取"""
        text = "新能源汽车行业迎来政策利好，锂电池概念股大涨"
        keywords = self.engine.extract_keywords_from_text(text, top_k=5)
        
        assert isinstance(keywords, list)
        assert len(keywords) > 0
        # 应该包含一些相关关键词
        relevant_keywords = ["新能源", "汽车", "锂电池", "政策"]
        has_relevant = any(kw in keywords for kw in relevant_keywords)
        assert has_relevant
    
    def test_classify_theme_type(self):
        """测试题材类型分类"""
        keywords = ["新能源", "政策", "锂电池", "汽车"]
        text = "国家发布新能源汽车支持政策，锂电池行业受益"
        
        type_scores = self.engine.classify_theme_type(keywords, text)
        
        assert isinstance(type_scores, dict)
        assert "policy" in type_scores
        assert "industry" in type_scores
        assert "event" in type_scores
        assert "concept" in type_scores
        
        # 政策相关得分应该较高
        assert type_scores["policy"] > 0
    
    def test_calculate_theme_score(self):
        """测试题材评分计算"""
        theme_data = {
            'policy_factor': 0.8,
            'industry_factor': 0.6,
            'event_factor': 0.3,
            'concept_factor': 0.2,
            'news_count': 10
        }
        
        score = self.engine.calculate_theme_score(theme_data)
        
        assert isinstance(score, float)
        assert score > 0
        
        # 验证计算公式
        expected_base = (0.8 * 4) + (0.6 * 3) + (0.3 * 2) + (0.2 * 1)
        expected_bonus = min(10 * 0.1, 2.0)
        expected_score = expected_base + expected_bonus
        
        assert abs(score - expected_score) < 0.01
    
    def test_identify_themes_from_news(self):
        """测试从新闻识别题材"""
        news_list = [
            {
                'title': '新能源汽车政策利好发布',
                'content': '国家发改委发布新能源汽车产业支持政策，锂电池行业迎来发展机遇',
                'publish_time': datetime.now().isoformat()
            },
            {
                'title': '锂电池技术突破',
                'content': '某公司在锂电池技术方面取得重大突破，新能源汽车续航里程大幅提升',
                'publish_time': datetime.now().isoformat()
            },
            {
                'title': '芯片行业复苏',
                'content': '半导体芯片行业出现复苏迹象，多家公司业绩超预期',
                'publish_time': datetime.now().isoformat()
            }
        ]
        
        themes = self.engine.identify_themes_from_news(news_list)
        
        assert isinstance(themes, list)
        # 应该识别出一些题材
        if themes:
            theme = themes[0]
            assert 'theme_id' in theme
            assert 'theme_name' in theme
            assert 'theme_type' in theme
            assert 'theme_score' in theme
            assert 'keywords' in theme
    
    def test_track_theme_lifecycle(self):
        """测试题材生命周期跟踪"""
        # 创建模拟历史数据
        dates = pd.date_range(start='2023-01-01', periods=10, freq='D')
        historical_data = pd.DataFrame({
            'date': dates,
            'close': [100, 102, 105, 108, 110, 112, 115, 118, 120, 122],
            'volume': [1000, 1200, 1500, 1800, 2000, 2200, 2500, 2800, 3000, 3200]
        })
        
        # 计算收益率
        historical_data['return'] = historical_data['close'].pct_change()
        
        lifecycle = self.engine.track_theme_lifecycle("test_theme", historical_data)
        
        assert isinstance(lifecycle, str)
        assert lifecycle in ["萌芽期", "发酵期", "高潮期", "分化期", "衰退期"]
    
    def test_find_related_stocks(self):
        """测试相关股票查找"""
        theme_keywords = ["新能源", "锂电池", "汽车"]
        stock_pool = [
            {'symbol': '300750', 'name': '宁德时代', 'industry': '锂电池'},
            {'symbol': '002594', 'name': '比亚迪', 'industry': '新能源汽车'},
            {'symbol': '000858', 'name': '五粮液', 'industry': '白酒'},
            {'symbol': '300014', 'name': '亿纬锂能', 'industry': '锂电池'}
        ]
        
        related_stocks = self.engine.find_related_stocks(theme_keywords, stock_pool)
        
        assert isinstance(related_stocks, list)
        # 应该找到相关股票
        if related_stocks:
            # 相关股票应该包含新能源或锂电池相关的
            assert '300750' in related_stocks or '002594' in related_stocks or '300014' in related_stocks
            # 不应该包含不相关的股票
            assert '000858' not in related_stocks
    
    def test_update_theme_data(self):
        """测试题材数据更新"""
        # 初始题材数据
        theme = {
            'theme_id': 'test_theme',
            'theme_name': '新能源',
            'policy_factor': 0.5,
            'industry_factor': 0.6,
            'event_factor': 0.3,
            'concept_factor': 0.2,
            'news_count': 5,
            'keywords': ['新能源', '汽车']
        }
        
        # 新新闻数据
        new_news = [
            {
                'title': '新能源政策再加码',
                'content': '政府出台新的新能源支持政策，锂电池行业受益'
            }
        ]
        
        updated_theme = self.engine.update_theme_data(theme, new_news)
        
        assert updated_theme['news_count'] == 6  # 5 + 1
        assert 'last_updated' in updated_theme
        assert updated_theme['theme_score'] >= theme.get('theme_score', 0)
    
    def test_get_theme_ranking(self):
        """测试题材排行榜"""
        # 添加一些测试题材到历史数据
        test_themes = [
            ThemeData(
                theme_id="theme1",
                theme_name="新能源",
                theme_type="policy",
                policy_factor=0.8,
                industry_factor=0.6,
                event_factor=0.3,
                concept_factor=0.2,
                theme_score=5.0,
                lifecycle_stage="发酵期",
                related_stocks=["300750", "002594"],
                news_count=10,
                keywords=["新能源", "汽车"],
                first_appeared=datetime.now(),
                last_updated=datetime.now(),
                heat_trend="rising"
            ),
            ThemeData(
                theme_id="theme2",
                theme_name="芯片",
                theme_type="industry",
                policy_factor=0.3,
                industry_factor=0.9,
                event_factor=0.2,
                concept_factor=0.1,
                theme_score=3.5,
                lifecycle_stage="萌芽期",
                related_stocks=["000063"],
                news_count=5,
                keywords=["芯片", "半导体"],
                first_appeared=datetime.now(),
                last_updated=datetime.now(),
                heat_trend="stable"
            )
        ]
        
        # 添加到引擎历史数据
        for theme in test_themes:
            self.engine.theme_history[theme.theme_id] = theme
        
        ranking = self.engine.get_theme_ranking(10)
        
        assert isinstance(ranking, list)
        assert len(ranking) == 2
        
        # 应该按评分排序
        assert ranking[0]['theme_score'] >= ranking[1]['theme_score']
        assert ranking[0]['theme_name'] == "新能源"  # 评分更高
    
    def test_analyze_theme_heat_trend(self):
        """测试题材热度趋势分析"""
        # 添加测试题材
        theme = ThemeData(
            theme_id="test_theme",
            theme_name="测试题材",
            theme_type="concept",
            policy_factor=0.5,
            industry_factor=0.5,
            event_factor=0.5,
            concept_factor=0.5,
            theme_score=3.0,
            lifecycle_stage="发酵期",
            related_stocks=[],
            news_count=5,
            keywords=["测试"],
            first_appeared=datetime.now(),
            last_updated=datetime.now() - timedelta(hours=2),  # 2小时前更新
            heat_trend="stable"
        )
        
        self.engine.theme_history["test_theme"] = theme
        
        trend = self.engine.analyze_theme_heat_trend("test_theme")
        
        assert isinstance(trend, str)
        assert trend in ["rising", "stable", "declining"]
    
    @pytest.mark.asyncio
    async def test_save_and_load_theme_data(self):
        """测试题材数据保存和加载"""
        themes = [
            {
                'theme_id': 'test_theme_1',
                'theme_name': '测试题材1',
                'theme_type': 'policy',
                'theme_score': 4.5,
                'news_count': 8,
                'keywords': ['测试', '题材']
            }
        ]
        
        # 保存数据
        date_str = datetime.now().strftime("%Y%m%d")
        success = await self.engine.save_theme_data_to_file(themes, date_str)
        
        # 验证保存成功
        assert success is True
        
        # 加载数据
        loaded_themes = await self.engine.load_theme_data_from_file(date_str)
        
        # 验证加载成功
        assert isinstance(loaded_themes, list)
        if loaded_themes:
            assert loaded_themes[0]['theme_name'] == '测试题材1'


if __name__ == "__main__":
    # 运行简单测试
    engine = ThemeIdentificationEngine()
    
    print("测试题材识别引擎...")
    
    # 测试关键词提取
    text = "人工智能技术在医疗领域的应用取得突破，相关概念股受到市场关注"
    keywords = engine.extract_keywords_from_text(text)
    print(f"关键词提取: {keywords}")
    
    # 测试题材类型分类
    type_scores = engine.classify_theme_type(keywords, text)
    print(f"题材类型分类: {type_scores}")
    
    # 测试题材评分
    theme_data = {
        'policy_factor': 0.3,
        'industry_factor': 0.7,
        'event_factor': 0.2,
        'concept_factor': 0.8,
        'news_count': 6
    }
    score = engine.calculate_theme_score(theme_data)
    print(f"题材评分: {score}")
    
    print("测试完成！")
