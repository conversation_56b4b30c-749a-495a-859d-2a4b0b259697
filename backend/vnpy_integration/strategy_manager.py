"""
VNPY策略管理器
负责策略的创建、配置、启动和监控
"""
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from loguru import logger

try:
    from vnpy.app.cta_strategy import CtaEngine
    from vnpy.event import EventEngine
    from vnpy.trader.engine import MainEngine
    from vnpy.trader.constant import Exchange
    from vnpy.gateway.ctp import CtpGateway
    VNPY_AVAILABLE = True
except ImportError:
    VNPY_AVAILABLE = False
    logger.warning("VNPY未安装，使用模拟策略管理器")
    
    # 模拟类
    class CtaEngine:
        def __init__(self, main_engine, event_engine):
            self.strategies = {}
            
        def add_strategy(self, class_name, strategy_name, vt_symbol, setting):
            pass
            
        def init_strategy(self, strategy_name):
            pass
            
        def start_strategy(self, strategy_name):
            pass
            
        def stop_strategy(self, strategy_name):
            pass
    
    class MainEngine:
        def __init__(self, event_engine):
            pass
    
    class EventEngine:
        pass

from backend.vnpy_integration.aiquant_strategy import AIQuantStrategy
from config.settings import settings


class VNPYStrategyManager:
    """VNPY策略管理器"""
    
    def __init__(self):
        self.event_engine = None
        self.main_engine = None
        self.cta_engine = None
        
        # 策略配置
        self.strategy_configs = {}
        self.active_strategies = {}
        
        # 配置文件路径
        self.config_path = Path("config/vnpy_strategies.json")
        
        # 初始化状态
        self.initialized = False
        
        logger.info("VNPY策略管理器初始化")
    
    def initialize(self) -> bool:
        """初始化VNPY引擎"""
        try:
            if not VNPY_AVAILABLE:
                logger.warning("VNPY不可用，使用模拟模式")
                self._init_mock_engines()
                return True
            
            # 创建事件引擎
            self.event_engine = EventEngine()
            
            # 创建主引擎
            self.main_engine = MainEngine(self.event_engine)
            
            # 添加网关（这里以CTP为例）
            self.main_engine.add_gateway(CtpGateway)
            
            # 创建CTA策略引擎
            self.cta_engine = CtaEngine(self.main_engine, self.event_engine)
            
            # 加载策略配置
            self.load_strategy_configs()
            
            self.initialized = True
            logger.info("VNPY引擎初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"VNPY引擎初始化失败: {e}")
            return False
    
    def _init_mock_engines(self):
        """初始化模拟引擎"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        self.cta_engine = CtaEngine(self.main_engine, self.event_engine)
        self.initialized = True
    
    def load_strategy_configs(self):
        """加载策略配置"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.strategy_configs = json.load(f)
                logger.info(f"加载策略配置: {len(self.strategy_configs)}个策略")
            else:
                # 创建默认配置
                self.strategy_configs = self._create_default_configs()
                self.save_strategy_configs()
                logger.info("创建默认策略配置")
                
        except Exception as e:
            logger.error(f"加载策略配置失败: {e}")
            self.strategy_configs = {}
    
    def save_strategy_configs(self):
        """保存策略配置"""
        try:
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.strategy_configs, f, indent=2, ensure_ascii=False)
            logger.info("策略配置已保存")
            
        except Exception as e:
            logger.error(f"保存策略配置失败: {e}")
    
    def _create_default_configs(self) -> Dict:
        """创建默认策略配置"""
        return {
            "aiquant_demo": {
                "class_name": "AIQuantStrategy",
                "vt_symbol": "000001.SZSE",
                "setting": {
                    "sentiment_threshold": 0.6,
                    "theme_threshold": 0.7,
                    "leader_threshold": 0.8,
                    "technical_threshold": 0.6,
                    "composite_threshold": 0.7,
                    "max_position_size": 1000,
                    "stop_loss_ratio": 0.05,
                    "take_profit_ratio": 0.10
                }
            }
        }
    
    def create_strategy(self, strategy_name: str, vt_symbol: str, 
                       strategy_config: Dict) -> bool:
        """创建策略"""
        try:
            if not self.initialized:
                logger.error("策略管理器未初始化")
                return False
            
            # 添加策略到CTA引擎
            self.cta_engine.add_strategy(
                class_name="AIQuantStrategy",
                strategy_name=strategy_name,
                vt_symbol=vt_symbol,
                setting=strategy_config
            )
            
            # 保存配置
            self.strategy_configs[strategy_name] = {
                "class_name": "AIQuantStrategy",
                "vt_symbol": vt_symbol,
                "setting": strategy_config
            }
            
            self.save_strategy_configs()
            
            logger.info(f"策略创建成功: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"创建策略失败: {e}")
            return False
    
    def init_strategy(self, strategy_name: str) -> bool:
        """初始化策略"""
        try:
            if strategy_name not in self.strategy_configs:
                logger.error(f"策略配置不存在: {strategy_name}")
                return False
            
            self.cta_engine.init_strategy(strategy_name)
            logger.info(f"策略初始化成功: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"初始化策略失败: {e}")
            return False
    
    def start_strategy(self, strategy_name: str) -> bool:
        """启动策略"""
        try:
            if strategy_name not in self.strategy_configs:
                logger.error(f"策略配置不存在: {strategy_name}")
                return False
            
            self.cta_engine.start_strategy(strategy_name)
            self.active_strategies[strategy_name] = {
                "start_time": datetime.now(),
                "status": "running"
            }
            
            logger.info(f"策略启动成功: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"启动策略失败: {e}")
            return False
    
    def stop_strategy(self, strategy_name: str) -> bool:
        """停止策略"""
        try:
            if strategy_name not in self.active_strategies:
                logger.warning(f"策略未运行: {strategy_name}")
                return True
            
            self.cta_engine.stop_strategy(strategy_name)
            
            if strategy_name in self.active_strategies:
                self.active_strategies[strategy_name]["status"] = "stopped"
                self.active_strategies[strategy_name]["stop_time"] = datetime.now()
            
            logger.info(f"策略停止成功: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"停止策略失败: {e}")
            return False
    
    def get_strategy_status(self, strategy_name: str) -> Dict:
        """获取策略状态"""
        try:
            if strategy_name not in self.strategy_configs:
                return {"status": "not_found", "message": "策略不存在"}
            
            if strategy_name in self.active_strategies:
                return {
                    "status": "active",
                    "config": self.strategy_configs[strategy_name],
                    "runtime": self.active_strategies[strategy_name]
                }
            else:
                return {
                    "status": "inactive",
                    "config": self.strategy_configs[strategy_name]
                }
                
        except Exception as e:
            logger.error(f"获取策略状态失败: {e}")
            return {"status": "error", "message": str(e)}
    
    def list_strategies(self) -> Dict:
        """列出所有策略"""
        try:
            strategies = {}
            
            for name, config in self.strategy_configs.items():
                status_info = self.get_strategy_status(name)
                strategies[name] = {
                    "vt_symbol": config["vt_symbol"],
                    "status": status_info["status"],
                    "config": config["setting"]
                }
                
                if name in self.active_strategies:
                    strategies[name]["runtime"] = self.active_strategies[name]
            
            return {
                "total_strategies": len(strategies),
                "active_strategies": len(self.active_strategies),
                "strategies": strategies
            }
            
        except Exception as e:
            logger.error(f"列出策略失败: {e}")
            return {"error": str(e)}
    
    def update_strategy_config(self, strategy_name: str, new_config: Dict) -> bool:
        """更新策略配置"""
        try:
            if strategy_name not in self.strategy_configs:
                logger.error(f"策略不存在: {strategy_name}")
                return False
            
            # 如果策略正在运行，需要先停止
            if strategy_name in self.active_strategies:
                logger.warning(f"策略正在运行，需要先停止: {strategy_name}")
                return False
            
            # 更新配置
            self.strategy_configs[strategy_name]["setting"].update(new_config)
            self.save_strategy_configs()
            
            logger.info(f"策略配置更新成功: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"更新策略配置失败: {e}")
            return False
    
    def delete_strategy(self, strategy_name: str) -> bool:
        """删除策略"""
        try:
            # 先停止策略
            if strategy_name in self.active_strategies:
                self.stop_strategy(strategy_name)
            
            # 删除配置
            if strategy_name in self.strategy_configs:
                del self.strategy_configs[strategy_name]
                self.save_strategy_configs()
            
            # 删除运行时信息
            if strategy_name in self.active_strategies:
                del self.active_strategies[strategy_name]
            
            logger.info(f"策略删除成功: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"删除策略失败: {e}")
            return False
    
    def get_strategy_performance(self, strategy_name: str) -> Dict:
        """获取策略绩效"""
        try:
            if strategy_name not in self.active_strategies:
                return {"status": "not_running", "message": "策略未运行"}
            
            # 这里应该从VNPY获取实际的绩效数据
            # 简化实现：返回模拟数据
            runtime = self.active_strategies[strategy_name]
            start_time = runtime.get("start_time", datetime.now())
            running_time = (datetime.now() - start_time).total_seconds() / 3600  # 小时
            
            return {
                "strategy_name": strategy_name,
                "running_time_hours": running_time,
                "total_trades": 0,  # 应该从VNPY获取
                "win_rate": 0.0,    # 应该从VNPY获取
                "total_pnl": 0.0,   # 应该从VNPY获取
                "max_drawdown": 0.0, # 应该从VNPY获取
                "sharpe_ratio": 0.0,  # 应该从VNPY获取
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取策略绩效失败: {e}")
            return {"status": "error", "message": str(e)}
    
    def batch_operation(self, operation: str, strategy_names: List[str]) -> Dict:
        """批量操作策略"""
        try:
            results = {}
            
            for strategy_name in strategy_names:
                if operation == "start":
                    results[strategy_name] = self.start_strategy(strategy_name)
                elif operation == "stop":
                    results[strategy_name] = self.stop_strategy(strategy_name)
                elif operation == "init":
                    results[strategy_name] = self.init_strategy(strategy_name)
                else:
                    results[strategy_name] = False
            
            success_count = sum(1 for success in results.values() if success)
            
            return {
                "operation": operation,
                "total": len(strategy_names),
                "success": success_count,
                "failed": len(strategy_names) - success_count,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"批量操作失败: {e}")
            return {"error": str(e)}
    
    def shutdown(self):
        """关闭策略管理器"""
        try:
            # 停止所有活跃策略
            for strategy_name in list(self.active_strategies.keys()):
                self.stop_strategy(strategy_name)
            
            # 关闭引擎
            if self.main_engine:
                self.main_engine.close()
            
            logger.info("策略管理器已关闭")
            
        except Exception as e:
            logger.error(f"关闭策略管理器失败: {e}")


# 全局策略管理器实例
vnpy_strategy_manager = VNPYStrategyManager()
