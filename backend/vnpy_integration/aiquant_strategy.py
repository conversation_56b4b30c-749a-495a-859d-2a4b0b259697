"""
AIQuant7 VNPY策略模板
集成自研算法模块的VNPY策略实现
"""
from datetime import datetime, time
from typing import Dict, List, Optional
import pandas as pd
from loguru import logger

try:
    from vnpy.app.cta_strategy import (
        CtaTemplate,
        StopOrder,
        TickData,
        BarData,
        TradeData,
        OrderData,
        BarGenerator,
        ArrayManager,
    )
    from vnpy.trader.constant import Direction, Offset, Status
    from vnpy.trader.object import OrderRequest
    VNPY_AVAILABLE = True
except ImportError:
    VNPY_AVAILABLE = False
    logger.warning("VNPY未安装，策略模板将使用模拟实现")
    
    # 模拟VNPY类
    class CtaTemplate:
        def __init__(self):
            self.pos = 0
            self.trading = False
            
        def write_log(self, msg: str):
            logger.info(msg)
            
        def send_order(self, direction, offset, price, volume, stop=False, lock=False, net=False):
            pass
            
        def cancel_order(self, vt_orderid: str):
            pass
    
    class Direction:
        LONG = "LONG"
        SHORT = "SHORT"
    
    class Offset:
        OPEN = "OPEN"
        CLOSE = "CLOSE"
    
    class BarData:
        def __init__(self):
            self.datetime = datetime.now()
            self.open_price = 0.0
            self.high_price = 0.0
            self.low_price = 0.0
            self.close_price = 0.0
            self.volume = 0.0

# 导入自研模块
from backend.core.sentiment.sentiment_engine import sentiment_engine
from backend.core.theme.theme_engine import theme_engine
from backend.core.leader.leader_engine import leader_engine
from backend.core.signal.signal_engine import signal_engine, SignalType
from backend.core.position.position_manager import position_manager
from backend.data.data_manager import data_manager


class AIQuantStrategy(CtaTemplate):
    """AIQuant7 VNPY策略"""
    
    # 策略参数
    author = "AIQuant7"
    
    # 策略参数
    sentiment_threshold = 0.6      # 情绪阈值
    theme_threshold = 0.7          # 题材阈值
    leader_threshold = 0.8         # 龙头阈值
    technical_threshold = 0.6      # 技术阈值
    composite_threshold = 0.7      # 综合信号阈值
    
    max_position_size = 1000       # 最大持仓数量
    stop_loss_ratio = 0.05         # 止损比例
    take_profit_ratio = 0.10       # 止盈比例
    
    # 时间控制
    trade_start_time = time(9, 30)   # 交易开始时间
    trade_end_time = time(14, 50)    # 交易结束时间
    
    # 策略变量
    signal_data = {}
    last_signal_time = None
    position_symbols = []
    
    # 参数列表
    parameters = [
        "sentiment_threshold",
        "theme_threshold", 
        "leader_threshold",
        "technical_threshold",
        "composite_threshold",
        "max_position_size",
        "stop_loss_ratio",
        "take_profit_ratio"
    ]
    
    # 变量列表
    variables = [
        "signal_data",
        "last_signal_time",
        "position_symbols"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """初始化策略"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 初始化数据管理器
        if VNPY_AVAILABLE:
            self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
            self.am = ArrayManager()
        
        # 策略状态
        self.inited = False
        self.trading = False
        
        # 信号缓存
        self.latest_signals = []
        self.signal_update_time = None
        
        self.write_log("AIQuant7策略初始化完成")
    
    def on_init(self):
        """策略初始化回调"""
        self.write_log("策略初始化中...")
        
        # 加载历史数据
        self.load_bar(10)  # 加载10天历史数据
        
        self.inited = True
        self.write_log("策略初始化完成")
    
    def on_start(self):
        """策略启动回调"""
        self.write_log("策略启动")
        self.trading = True
    
    def on_stop(self):
        """策略停止回调"""
        self.write_log("策略停止")
        self.trading = False
    
    def on_tick(self, tick):
        """Tick数据回调"""
        if VNPY_AVAILABLE:
            self.bg.update_tick(tick)
    
    def on_bar(self, bar: BarData):
        """Bar数据回调"""
        if VNPY_AVAILABLE:
            self.bg.update_bar(bar)
    
    def on_1min_bar(self, bar: BarData):
        """1分钟Bar数据回调"""
        if not self.trading:
            return
        
        # 检查交易时间
        if not self._is_trading_time(bar.datetime.time()):
            return
        
        # 更新数组管理器
        if VNPY_AVAILABLE:
            self.am.update_bar(bar)
            if not self.am.inited:
                return
        
        # 执行策略逻辑
        self._execute_strategy_logic(bar)
    
    def _is_trading_time(self, current_time: time) -> bool:
        """检查是否在交易时间内"""
        return self.trade_start_time <= current_time <= self.trade_end_time
    
    def _execute_strategy_logic(self, bar: BarData):
        """执行策略逻辑"""
        try:
            # 1. 获取市场数据
            market_data = self._get_market_data()
            
            # 2. 生成交易信号
            signals = self._generate_trading_signals(market_data, bar)
            
            # 3. 执行交易决策
            self._execute_trading_decisions(signals, bar)
            
            # 4. 风险控制
            self._execute_risk_management(bar)
            
        except Exception as e:
            self.write_log(f"策略执行异常: {e}")
    
    def _get_market_data(self) -> Dict:
        """获取市场数据"""
        try:
            # 这里应该调用数据管理器获取实时市场数据
            # 简化实现：返回模拟数据
            return {
                'timestamp': datetime.now(),
                'limit_up_count': 25,
                'limit_down_count': 8,
                'max_continuous_boards': 2
            }
        except Exception as e:
            self.write_log(f"获取市场数据失败: {e}")
            return {}
    
    def _generate_trading_signals(self, market_data: Dict, bar: BarData) -> List:
        """生成交易信号"""
        try:
            # 检查信号更新频率（避免过于频繁）
            now = datetime.now()
            if (self.signal_update_time and 
                (now - self.signal_update_time).seconds < 60):  # 1分钟内不重复生成
                return self.latest_signals
            
            # 创建股票数据DataFrame
            stock_data = self._create_stock_dataframe(bar)
            
            # 生成综合信号
            signal = signal_engine.generate_comprehensive_signal(
                self.vt_symbol.split('.')[0],  # 提取股票代码
                market_data,
                stock_data
            )
            
            signals = [signal] if signal else []
            
            # 更新缓存
            self.latest_signals = signals
            self.signal_update_time = now
            
            if signals:
                self.write_log(f"生成交易信号: {len(signals)}个")
            
            return signals
            
        except Exception as e:
            self.write_log(f"生成交易信号失败: {e}")
            return []
    
    def _create_stock_dataframe(self, bar: BarData) -> pd.DataFrame:
        """创建股票数据DataFrame"""
        try:
            if VNPY_AVAILABLE and self.am.inited:
                # 使用ArrayManager的数据
                data = {
                    'datetime': [bar.datetime] * len(self.am.close),
                    'open': self.am.open,
                    'high': self.am.high,
                    'low': self.am.low,
                    'close': self.am.close,
                    'volume': self.am.volume,
                    'symbol': [self.vt_symbol.split('.')[0]] * len(self.am.close)
                }
                return pd.DataFrame(data)
            else:
                # 简化实现：单条数据
                return pd.DataFrame([{
                    'datetime': bar.datetime,
                    'open': bar.open_price,
                    'high': bar.high_price,
                    'low': bar.low_price,
                    'close': bar.close_price,
                    'volume': bar.volume,
                    'symbol': self.vt_symbol.split('.')[0]
                }])
                
        except Exception as e:
            self.write_log(f"创建股票数据失败: {e}")
            return pd.DataFrame()
    
    def _execute_trading_decisions(self, signals: List, bar: BarData):
        """执行交易决策"""
        try:
            for signal in signals:
                if signal.signal_type == SignalType.BUY:
                    self._execute_buy_signal(signal, bar)
                elif signal.signal_type == SignalType.SELL:
                    self._execute_sell_signal(signal, bar)
                    
        except Exception as e:
            self.write_log(f"执行交易决策失败: {e}")
    
    def _execute_buy_signal(self, signal, bar: BarData):
        """执行买入信号"""
        try:
            # 检查信号质量
            if signal.confidence < self.composite_threshold:
                self.write_log(f"信号置信度不足: {signal.confidence:.3f} < {self.composite_threshold}")
                return
            
            # 检查当前持仓
            if self.pos >= self.max_position_size:
                self.write_log("已达最大持仓，跳过买入信号")
                return
            
            # 计算买入数量
            buy_volume = min(
                self.max_position_size - self.pos,
                int(signal.max_position_ratio * self.max_position_size)
            )
            
            if buy_volume <= 0:
                return
            
            # 发送买入订单
            buy_price = bar.close_price * 1.01  # 稍高于当前价格确保成交
            
            if VNPY_AVAILABLE:
                self.send_order(Direction.LONG, Offset.OPEN, buy_price, buy_volume)
            
            self.write_log(f"发送买入订单: 价格={buy_price:.2f}, 数量={buy_volume}, "
                          f"信号置信度={signal.confidence:.3f}")
            
        except Exception as e:
            self.write_log(f"执行买入信号失败: {e}")
    
    def _execute_sell_signal(self, signal, bar: BarData):
        """执行卖出信号"""
        try:
            # 检查当前持仓
            if self.pos <= 0:
                return
            
            # 检查信号质量
            if signal.confidence < self.composite_threshold:
                return
            
            # 计算卖出数量
            sell_volume = min(self.pos, abs(self.pos))
            
            # 发送卖出订单
            sell_price = bar.close_price * 0.99  # 稍低于当前价格确保成交
            
            if VNPY_AVAILABLE:
                self.send_order(Direction.SHORT, Offset.CLOSE, sell_price, sell_volume)
            
            self.write_log(f"发送卖出订单: 价格={sell_price:.2f}, 数量={sell_volume}, "
                          f"信号置信度={signal.confidence:.3f}")
            
        except Exception as e:
            self.write_log(f"执行卖出信号失败: {e}")
    
    def _execute_risk_management(self, bar: BarData):
        """执行风险管理"""
        try:
            if self.pos == 0:
                return
            
            current_price = bar.close_price
            
            # 这里应该获取持仓成本价，简化实现使用固定值
            cost_price = getattr(self, 'cost_price', current_price)
            
            # 止损检查
            if self.pos > 0:  # 多头持仓
                loss_ratio = (cost_price - current_price) / cost_price
                if loss_ratio > self.stop_loss_ratio:
                    self._execute_stop_loss(current_price, "多头止损")
                    
                # 止盈检查
                profit_ratio = (current_price - cost_price) / cost_price
                if profit_ratio > self.take_profit_ratio:
                    self._execute_take_profit(current_price, "多头止盈")
            
            elif self.pos < 0:  # 空头持仓
                loss_ratio = (current_price - cost_price) / cost_price
                if loss_ratio > self.stop_loss_ratio:
                    self._execute_stop_loss(current_price, "空头止损")
                    
                # 止盈检查
                profit_ratio = (cost_price - current_price) / cost_price
                if profit_ratio > self.take_profit_ratio:
                    self._execute_take_profit(current_price, "空头止盈")
                    
        except Exception as e:
            self.write_log(f"风险管理执行失败: {e}")
    
    def _execute_stop_loss(self, current_price: float, reason: str):
        """执行止损"""
        try:
            if self.pos > 0:
                sell_price = current_price * 0.99
                if VNPY_AVAILABLE:
                    self.send_order(Direction.SHORT, Offset.CLOSE, sell_price, self.pos)
            elif self.pos < 0:
                buy_price = current_price * 1.01
                if VNPY_AVAILABLE:
                    self.send_order(Direction.LONG, Offset.CLOSE, buy_price, abs(self.pos))
            
            self.write_log(f"执行止损: {reason}, 价格={current_price:.2f}")
            
        except Exception as e:
            self.write_log(f"执行止损失败: {e}")
    
    def _execute_take_profit(self, current_price: float, reason: str):
        """执行止盈"""
        try:
            if self.pos > 0:
                sell_price = current_price * 0.99
                if VNPY_AVAILABLE:
                    self.send_order(Direction.SHORT, Offset.CLOSE, sell_price, self.pos)
            elif self.pos < 0:
                buy_price = current_price * 1.01
                if VNPY_AVAILABLE:
                    self.send_order(Direction.LONG, Offset.CLOSE, buy_price, abs(self.pos))
            
            self.write_log(f"执行止盈: {reason}, 价格={current_price:.2f}")
            
        except Exception as e:
            self.write_log(f"执行止盈失败: {e}")
    
    def on_order(self, order: OrderData):
        """订单状态更新回调"""
        self.write_log(f"订单状态更新: {order.vt_orderid}, 状态: {order.status}")
    
    def on_trade(self, trade: TradeData):
        """成交回调"""
        # 更新持仓成本价
        if hasattr(self, 'cost_price'):
            if self.pos != 0:
                total_cost = self.cost_price * abs(self.pos) + trade.price * trade.volume
                total_volume = abs(self.pos) + trade.volume
                self.cost_price = total_cost / total_volume
        else:
            self.cost_price = trade.price
        
        self.write_log(f"成交回报: {trade.vt_tradeid}, 价格: {trade.price}, 数量: {trade.volume}")
    
    def on_stop_order(self, stop_order: StopOrder):
        """停止单状态更新回调"""
        self.write_log(f"停止单状态更新: {stop_order.vt_orderid}")


# 策略工厂函数
def create_aiquant_strategy(cta_engine, strategy_name: str, vt_symbol: str, setting: Dict):
    """创建AIQuant策略实例"""
    return AIQuantStrategy(cta_engine, strategy_name, vt_symbol, setting)
