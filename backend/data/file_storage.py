"""
基于文件的数据存储管理器
支持CSV和Feather格式
"""
import os
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, date
from typing import Dict, List, Optional, Union, Any
from loguru import logger
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
import json

from config.settings import settings


class FileStorageManager:
    """文件存储管理器"""
    
    def __init__(self):
        self.data_root = Path(settings.data_storage.get_data_path)
        self.file_format = settings.data_storage.file_format
        self.compression = settings.data_storage.compression
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 创建数据目录
        self._create_directories()
    
    def _create_directories(self):
        """创建数据存储目录"""
        directories = [
            self.data_root / settings.data_storage.market_data_dir,
            self.data_root / settings.data_storage.sentiment_data_dir,
            self.data_root / settings.data_storage.theme_data_dir,
            self.data_root / settings.data_storage.signal_data_dir,
            self.data_root / settings.data_storage.position_data_dir,
            self.data_root / settings.data_storage.stock_basic_dir,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"数据存储目录已创建: {self.data_root}")
    
    def _get_file_path(self, data_type: str, filename: str) -> Path:
        """获取文件完整路径"""
        type_mapping = {
            "market_data": settings.data_storage.market_data_dir,
            "sentiment_data": settings.data_storage.sentiment_data_dir,
            "theme_data": settings.data_storage.theme_data_dir,
            "signal_data": settings.data_storage.signal_data_dir,
            "position_data": settings.data_storage.position_data_dir,
            "stock_basic": settings.data_storage.stock_basic_dir,
        }
        
        subdir = type_mapping.get(data_type, data_type)
        return self.data_root / subdir / f"{filename}.{self.file_format}"
    
    async def save_dataframe(self, df: pd.DataFrame, data_type: str, filename: str) -> bool:
        """异步保存DataFrame到文件"""
        try:
            file_path = self._get_file_path(data_type, filename)
            
            # 在线程池中执行文件操作
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.executor, 
                self._save_dataframe_sync, 
                df, file_path
            )
            
            logger.debug(f"数据已保存: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存数据失败 {data_type}/{filename}: {e}")
            return False
    
    def _save_dataframe_sync(self, df: pd.DataFrame, file_path: Path):
        """同步保存DataFrame"""
        if self.file_format == "feather":
            df.to_feather(file_path, compression=self.compression)
        elif self.file_format == "csv":
            df.to_csv(file_path, index=False, encoding='utf-8')
        else:
            raise ValueError(f"不支持的文件格式: {self.file_format}")
    
    async def load_dataframe(self, data_type: str, filename: str) -> Optional[pd.DataFrame]:
        """异步加载DataFrame从文件"""
        try:
            file_path = self._get_file_path(data_type, filename)
            
            if not file_path.exists():
                logger.warning(f"文件不存在: {file_path}")
                return None
            
            # 在线程池中执行文件操作
            loop = asyncio.get_event_loop()
            df = await loop.run_in_executor(
                self.executor,
                self._load_dataframe_sync,
                file_path
            )
            
            logger.debug(f"数据已加载: {file_path}, 形状: {df.shape}")
            return df
            
        except Exception as e:
            logger.error(f"加载数据失败 {data_type}/{filename}: {e}")
            return None
    
    def _load_dataframe_sync(self, file_path: Path) -> pd.DataFrame:
        """同步加载DataFrame"""
        if self.file_format == "feather":
            return pd.read_feather(file_path)
        elif self.file_format == "csv":
            return pd.read_csv(file_path, encoding='utf-8')
        else:
            raise ValueError(f"不支持的文件格式: {self.file_format}")
    
    async def append_dataframe(self, df: pd.DataFrame, data_type: str, filename: str) -> bool:
        """追加数据到现有文件"""
        try:
            existing_df = await self.load_dataframe(data_type, filename)
            
            if existing_df is not None:
                # 合并数据并去重
                combined_df = pd.concat([existing_df, df], ignore_index=True)
                combined_df = combined_df.drop_duplicates()
            else:
                combined_df = df
            
            return await self.save_dataframe(combined_df, data_type, filename)
            
        except Exception as e:
            logger.error(f"追加数据失败 {data_type}/{filename}: {e}")
            return False
    
    async def save_market_data(self, symbol: str, df: pd.DataFrame, date_str: str = None) -> bool:
        """保存市场数据"""
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")
        
        filename = f"{symbol}_{date_str}"
        return await self.save_dataframe(df, "market_data", filename)
    
    async def load_market_data(self, symbol: str, date_str: str = None) -> Optional[pd.DataFrame]:
        """加载市场数据"""
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")
        
        filename = f"{symbol}_{date_str}"
        return await self.load_dataframe("market_data", filename)
    
    async def save_sentiment_data(self, df: pd.DataFrame, date_str: str = None) -> bool:
        """保存情绪数据"""
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")
        
        filename = f"sentiment_{date_str}"
        return await self.save_dataframe(df, "sentiment_data", filename)
    
    async def load_sentiment_data(self, date_str: str = None) -> Optional[pd.DataFrame]:
        """加载情绪数据"""
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")
        
        filename = f"sentiment_{date_str}"
        return await self.load_dataframe("sentiment_data", filename)
    
    async def save_theme_data(self, df: pd.DataFrame, date_str: str = None) -> bool:
        """保存题材数据"""
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")
        
        filename = f"themes_{date_str}"
        return await self.save_dataframe(df, "theme_data", filename)
    
    async def load_theme_data(self, date_str: str = None) -> Optional[pd.DataFrame]:
        """加载题材数据"""
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")
        
        filename = f"themes_{date_str}"
        return await self.load_dataframe("theme_data", filename)
    
    async def save_signal_data(self, df: pd.DataFrame, date_str: str = None) -> bool:
        """保存交易信号数据"""
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")
        
        filename = f"signals_{date_str}"
        return await self.save_dataframe(df, "signal_data", filename)
    
    async def load_signal_data(self, date_str: str = None) -> Optional[pd.DataFrame]:
        """加载交易信号数据"""
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")
        
        filename = f"signals_{date_str}"
        return await self.load_dataframe("signal_data", filename)
    
    async def save_position_data(self, df: pd.DataFrame) -> bool:
        """保存持仓数据"""
        filename = "current_positions"
        return await self.save_dataframe(df, "position_data", filename)
    
    async def load_position_data(self) -> Optional[pd.DataFrame]:
        """加载持仓数据"""
        filename = "current_positions"
        return await self.load_dataframe("position_data", filename)
    
    async def save_stock_basic_data(self, df: pd.DataFrame) -> bool:
        """保存股票基础数据"""
        filename = "stock_basic"
        return await self.save_dataframe(df, "stock_basic", filename)
    
    async def load_stock_basic_data(self) -> Optional[pd.DataFrame]:
        """加载股票基础数据"""
        filename = "stock_basic"
        return await self.load_dataframe("stock_basic", filename)
    
    def list_files(self, data_type: str) -> List[str]:
        """列出指定类型的所有文件"""
        type_mapping = {
            "market_data": settings.data_storage.market_data_dir,
            "sentiment_data": settings.data_storage.sentiment_data_dir,
            "theme_data": settings.data_storage.theme_data_dir,
            "signal_data": settings.data_storage.signal_data_dir,
            "position_data": settings.data_storage.position_data_dir,
            "stock_basic": settings.data_storage.stock_basic_dir,
        }
        
        subdir = type_mapping.get(data_type, data_type)
        directory = self.data_root / subdir
        
        if not directory.exists():
            return []
        
        files = []
        for file_path in directory.glob(f"*.{self.file_format}"):
            files.append(file_path.stem)  # 不包含扩展名
        
        return sorted(files)
    
    async def cleanup_old_files(self):
        """清理过期文件"""
        try:
            current_date = datetime.now()
            
            # 清理市场数据
            market_files = self.list_files("market_data")
            for filename in market_files:
                if "_" in filename:
                    date_str = filename.split("_")[-1]
                    try:
                        file_date = datetime.strptime(date_str, "%Y%m%d")
                        days_old = (current_date - file_date).days
                        
                        if days_old > settings.data_storage.market_data_retention_days:
                            file_path = self._get_file_path("market_data", filename)
                            file_path.unlink()
                            logger.info(f"已删除过期文件: {file_path}")
                    except ValueError:
                        continue
            
            # 清理信号数据
            signal_files = self.list_files("signal_data")
            for filename in signal_files:
                if "_" in filename:
                    date_str = filename.split("_")[-1]
                    try:
                        file_date = datetime.strptime(date_str, "%Y%m%d")
                        days_old = (current_date - file_date).days
                        
                        if days_old > settings.data_storage.signal_data_retention_days:
                            file_path = self._get_file_path("signal_data", filename)
                            file_path.unlink()
                            logger.info(f"已删除过期文件: {file_path}")
                    except ValueError:
                        continue
                        
        except Exception as e:
            logger.error(f"清理过期文件失败: {e}")


# 全局文件存储管理器实例
file_storage = FileStorageManager()
