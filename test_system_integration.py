#!/usr/bin/env python3
"""
系统集成测试
测试前端、后端、数据管理、策略引擎的完整集成
"""
import asyncio
import sys
import os
import time
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class SystemIntegrationTester:
    """系统集成测试器"""
    
    def __init__(self):
        self.backend_url = "http://localhost:8080"
        self.frontend_url = "http://localhost:3000"
        self.test_results = []
    
    def test_backend_health(self):
        """测试后端健康状态"""
        print("🏥 测试后端健康状态...")
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ 后端健康检查通过")
                print(f"   📊 状态: {data.get('status', 'unknown')}")
                print(f"   🕐 时间: {data.get('timestamp', 'unknown')}")
                return True
            else:
                print(f"   ❌ 后端健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ 后端连接失败: {e}")
            return False
    
    def test_frontend_accessibility(self):
        """测试前端可访问性"""
        print("🌐 测试前端可访问性...")
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                print(f"   ✅ 前端页面可访问")
                print(f"   📄 内容长度: {len(response.text)} 字符")
                return True
            else:
                print(f"   ❌ 前端访问失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ 前端连接失败: {e}")
            return False
    
    def test_api_endpoints(self):
        """测试API端点"""
        print("🔌 测试API端点...")
        
        endpoints = [
            ("/api/v1/market/realtime", "实时市场数据"),
            ("/api/v1/sentiment/current", "市场情绪"),
            ("/api/v1/themes/ranking", "题材排行"),
            ("/api/v1/leaders/ranking", "龙头排行"),
            ("/api/v1/signals/latest", "交易信号"),
            ("/api/v1/positions/current", "当前持仓"),
            ("/api/v1/market/news", "市场新闻")
        ]
        
        success_count = 0
        for endpoint, description in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ {description}: 成功")
                    success_count += 1
                else:
                    print(f"   ❌ {description}: HTTP {response.status_code}")
            except Exception as e:
                print(f"   ❌ {description}: {e}")
        
        print(f"   📊 API测试结果: {success_count}/{len(endpoints)} 成功")
        return success_count == len(endpoints)
    
    def test_data_flow(self):
        """测试数据流"""
        print("🔄 测试数据流...")
        
        try:
            # 测试市场数据获取
            response = requests.get(f"{self.backend_url}/api/v1/market/realtime", timeout=10)
            if response.status_code == 200:
                market_data = response.json()
                print(f"   ✅ 市场数据获取成功")
                
                # 检查数据结构
                if 'data' in market_data:
                    data = market_data['data']
                    print(f"   📊 涨停股数: {data.get('limit_up_count', 0)}")
                    print(f"   📊 跌停股数: {data.get('limit_down_count', 0)}")
                    print(f"   📊 最高连板: {data.get('max_continuous_boards', 0)}")
                    
                    # 测试情绪分析
                    response = requests.get(f"{self.backend_url}/api/v1/sentiment/current", timeout=10)
                    if response.status_code == 200:
                        sentiment_data = response.json()
                        print(f"   ✅ 情绪分析成功")
                        if 'data' in sentiment_data:
                            sentiment = sentiment_data['data']
                            print(f"   🌡️  情绪指数: {sentiment.get('sentiment_score', 0):.1f}")
                            print(f"   🌡️  情绪阶段: {sentiment.get('sentiment_phase', 'unknown')}")
                        return True
                    else:
                        print(f"   ❌ 情绪分析失败: HTTP {response.status_code}")
                        return False
                else:
                    print(f"   ❌ 市场数据格式错误")
                    return False
            else:
                print(f"   ❌ 市场数据获取失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ 数据流测试失败: {e}")
            return False
    
    def test_strategy_engine(self):
        """测试策略引擎"""
        print("🧠 测试策略引擎...")
        
        try:
            # 测试题材分析
            response = requests.get(f"{self.backend_url}/api/v1/themes/ranking", timeout=15)
            if response.status_code == 200:
                themes_data = response.json()
                print(f"   ✅ 题材分析成功")
                if 'data' in themes_data and themes_data['data']:
                    themes = themes_data['data']
                    print(f"   🔥 识别题材: {len(themes)}个")
                    if themes:
                        top_theme = themes[0]
                        print(f"   🔥 顶级题材: {top_theme.get('theme_name', 'unknown')}")
            
            # 测试龙头识别
            response = requests.get(f"{self.backend_url}/api/v1/leaders/ranking", timeout=15)
            if response.status_code == 200:
                leaders_data = response.json()
                print(f"   ✅ 龙头识别成功")
                if 'data' in leaders_data and leaders_data['data']:
                    leaders = leaders_data['data']
                    print(f"   👑 识别龙头: {len(leaders)}只")
                    if leaders:
                        top_leader = leaders[0]
                        print(f"   👑 顶级龙头: {top_leader.get('name', 'unknown')}")
            
            # 测试信号生成
            response = requests.get(f"{self.backend_url}/api/v1/signals/latest", timeout=15)
            if response.status_code == 200:
                signals_data = response.json()
                print(f"   ✅ 信号生成成功")
                if 'data' in signals_data and signals_data['data']:
                    signals = signals_data['data']
                    print(f"   📡 生成信号: {len(signals)}个")
                    buy_signals = [s for s in signals if s.get('signal_type') == 'buy']
                    print(f"   📡 买入信号: {len(buy_signals)}个")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 策略引擎测试失败: {e}")
            return False
    
    def test_performance(self):
        """测试系统性能"""
        print("⚡ 测试系统性能...")
        
        try:
            # 测试API响应时间
            start_time = time.time()
            response = requests.get(f"{self.backend_url}/api/v1/market/realtime", timeout=10)
            end_time = time.time()
            
            if response.status_code == 200:
                response_time = (end_time - start_time) * 1000
                print(f"   ⏱️  API响应时间: {response_time:.0f}ms")
                
                if response_time < 1000:
                    print(f"   ✅ 响应时间优秀 (<1s)")
                elif response_time < 3000:
                    print(f"   ⚠️  响应时间良好 (<3s)")
                else:
                    print(f"   ❌ 响应时间较慢 (>3s)")
                
                return response_time < 5000  # 5秒内算通过
            else:
                print(f"   ❌ 性能测试失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ 性能测试失败: {e}")
            return False
    
    def test_error_handling(self):
        """测试错误处理"""
        print("🛡️  测试错误处理...")
        
        try:
            # 测试不存在的端点
            response = requests.get(f"{self.backend_url}/api/v1/nonexistent", timeout=5)
            if response.status_code == 404:
                print(f"   ✅ 404错误处理正确")
            else:
                print(f"   ⚠️  404错误处理异常: HTTP {response.status_code}")
            
            # 测试无效参数
            response = requests.get(f"{self.backend_url}/api/v1/market/stock/INVALID/realtime", timeout=5)
            if response.status_code in [400, 404, 422]:
                print(f"   ✅ 无效参数错误处理正确")
            else:
                print(f"   ⚠️  无效参数错误处理异常: HTTP {response.status_code}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 错误处理测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始系统集成测试")
        print("=" * 60)
        
        tests = [
            ("后端健康检查", self.test_backend_health),
            ("前端可访问性", self.test_frontend_accessibility),
            ("API端点测试", self.test_api_endpoints),
            ("数据流测试", self.test_data_flow),
            ("策略引擎测试", self.test_strategy_engine),
            ("性能测试", self.test_performance),
            ("错误处理测试", self.test_error_handling)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 {test_name}:")
            try:
                result = test_func()
                if result:
                    passed_tests += 1
                    self.test_results.append((test_name, "PASS"))
                else:
                    self.test_results.append((test_name, "FAIL"))
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
                self.test_results.append((test_name, "ERROR"))
        
        # 输出测试总结
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        
        for test_name, result in self.test_results:
            status_icon = "✅" if result == "PASS" else "❌" if result == "FAIL" else "⚠️"
            print(f"{status_icon} {test_name}: {result}")
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"\n📈 测试通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("🎉 系统集成测试通过！")
            print("\n✨ 系统功能完整，可以正常使用")
        elif success_rate >= 60:
            print("⚠️  系统集成测试部分通过")
            print("\n💡 系统基本可用，但存在一些问题需要修复")
        else:
            print("❌ 系统集成测试失败")
            print("\n🔧 系统存在严重问题，需要修复后再测试")
        
        print("\n🔗 系统访问地址:")
        print(f"   🌐 前端界面: {self.frontend_url}")
        print(f"   📖 API文档: {self.backend_url}/docs")
        print(f"   🏥 健康检查: {self.backend_url}/health")
        
        return success_rate >= 80


def main():
    """主函数"""
    print("🧪 AIQuant7 系统集成测试")
    print("测试前端、后端、数据管理、策略引擎的完整集成")
    print()
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(3)
    
    # 运行测试
    tester = SystemIntegrationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎊 恭喜！AIQuant7系统集成测试全部通过！")
        print("系统已准备就绪，可以开始使用。")
    else:
        print("\n🔧 系统需要进一步调试和优化。")
    
    return success


if __name__ == "__main__":
    main()
