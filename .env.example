# 应用配置
APP_NAME=AIQuant7
VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true

# API配置
API_HOST=0.0.0.0
API_PORT=8080
SECRET_KEY=your-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 数据库配置
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=aiquant7

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-influxdb-token
INFLUXDB_ORG=aiquant
INFLUXDB_BUCKET=market_data

# Kafka配置
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_GROUP_ID=aiquant7

# 数据源配置
TUSHARE_TOKEN=your-tushare-token
JQDATA_USERNAME=your-jqdata-username
JQDATA_PASSWORD=your-jqdata-password
RQDATA_USERNAME=your-rqdata-username
RQDATA_PASSWORD=your-rqdata-password
IFIND_USERNAME=your-ifind-username
IFIND_PASSWORD=your-ifind-password
XUEQIU_TOKEN=your-xueqiu-token

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs

# 监控配置
PROMETHEUS_PORT=8000
