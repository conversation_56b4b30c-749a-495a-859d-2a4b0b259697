version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: trading_postgres
    environment:
      POSTGRES_DB: trading_system
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - trading_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: trading_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - trading_network

  # InfluxDB时序数据库
  influxdb:
    image: influxdb:2.7
    container_name: trading_influxdb
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: password
      DOCKER_INFLUXDB_INIT_ORG: trading
      DOCKER_INFLUXDB_INIT_BUCKET: market_data
      DOCKER_INFLUXDB_INIT_ADMIN_TOKEN: my-super-secret-auth-token
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
    networks:
      - trading_network

  # Zookeeper (Kafka依赖)
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: trading_zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - trading_network

  # Kafka消息队列
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: trading_kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - trading_network

  # Grafana监控面板
  grafana:
    image: grafana/grafana:10.0.0
    container_name: trading_grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - trading_network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: trading_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - trading_network

  # 数据采集服务
  data_collector:
    build:
      context: .
      dockerfile: Dockerfile.data_collector
    container_name: trading_data_collector
    depends_on:
      - postgres
      - redis
      - influxdb
      - kafka
    environment:
      - POSTGRES_HOST=postgres
      - REDIS_HOST=redis
      - INFLUXDB_HOST=influxdb
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    networks:
      - trading_network
    restart: unless-stopped

  # 策略计算服务
  strategy_engine:
    build:
      context: .
      dockerfile: Dockerfile.strategy_engine
    container_name: trading_strategy_engine
    depends_on:
      - postgres
      - redis
      - kafka
    environment:
      - POSTGRES_HOST=postgres
      - REDIS_HOST=redis
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
      - ./strategies:/app/strategies
    networks:
      - trading_network
    restart: unless-stopped

  # 交易执行服务
  trade_executor:
    build:
      context: .
      dockerfile: Dockerfile.trade_executor
    container_name: trading_trade_executor
    depends_on:
      - postgres
      - redis
      - kafka
    environment:
      - POSTGRES_HOST=postgres
      - REDIS_HOST=redis
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    networks:
      - trading_network
    restart: unless-stopped

  # Web API服务
  web_api:
    build:
      context: .
      dockerfile: Dockerfile.web_api
    container_name: trading_web_api
    depends_on:
      - postgres
      - redis
    ports:
      - "8080:8080"
    environment:
      - POSTGRES_HOST=postgres
      - REDIS_HOST=redis
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    networks:
      - trading_network
    restart: unless-stopped

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: trading_frontend
    ports:
      - "3001:80"
    depends_on:
      - web_api
    networks:
      - trading_network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: trading_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - web_api
      - frontend
    networks:
      - trading_network

volumes:
  postgres_data:
  redis_data:
  influxdb_data:
  kafka_data:
  grafana_data:
  prometheus_data:

networks:
  trading_network:
    driver: bridge 