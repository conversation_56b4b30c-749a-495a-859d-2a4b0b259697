"""
游资超短线交易策略系统配置文件
"""
from pydantic_settings import BaseSettings
from typing import Dict, List
import os

class Settings(BaseSettings):
    """系统配置类"""
    
    # 应用基础配置
    app_name: str = "SuperShortTermTradingSystem"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 数据库配置
    postgres_host: str = "localhost"
    postgres_port: int = 5432
    postgres_db: str = "trading_system"
    postgres_user: str = "postgres"
    postgres_password: str = "password"
    
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    
    influxdb_host: str = "localhost"
    influxdb_port: int = 8086
    influxdb_token: str = ""
    influxdb_org: str = "trading"
    influxdb_bucket: str = "market_data"
    
    # Kafka配置
    kafka_bootstrap_servers: List[str] = ["localhost:9092"]
    kafka_topics: Dict[str, str] = {
        "market_data": "market_data_topic",
        "sentiment": "sentiment_topic", 
        "signals": "trading_signals_topic",
        "positions": "positions_topic"
    }
    
    # 数据源配置
    tushare_token: str = ""
    wind_username: str = ""
    wind_password: str = ""
    
    # 交易接口配置
    broker_name: str = ""
    broker_account: str = ""
    broker_password: str = ""
    
    # 策略参数
    class StrategyConfig:
        # 市场情绪参数
        sentiment_weights: Dict[str, float] = {
            "limit_up_weight": 2.0,
            "limit_down_weight": -3.0,
            "continuous_board_weight": 5.0
        }
        
        sentiment_thresholds: Dict[str, float] = {
            "ice_point": 20.0,      # 冰点期
            "warming": 50.0,        # 回暖期  
            "climax": 80.0          # 高潮期
        }
        
        # 题材评分权重
        theme_weights: Dict[str, float] = {
            "policy": 4.0,          # 政策驱动
            "industry": 3.0,        # 行业拐点
            "event": 2.0,           # 事件催化
            "concept": 1.0          # 主题炒作
        }
        
        # 龙头股识别权重
        leader_weights: Dict[str, float] = {
            "return_rank": 0.3,     # 涨幅排名
            "volume_rank": 0.25,    # 成交额排名
            "board_height": 0.2,    # 连板高度
            "concept_purity": 0.15, # 概念纯正度
            "market_cap": 0.1       # 流通市值适中度
        }
        
        # 仓位管理参数
        position_limits: Dict[str, float] = {
            "ice_point": 0.30,      # 冰点期最大仓位
            "warming": 0.70,        # 回暖期最大仓位
            "fermentation": 0.60,   # 发酵期最大仓位
            "climax": 0.40,         # 高潮期最大仓位
            "ebb": 0.20,            # 退潮期最大仓位
            "chaos": 0.00           # 混沌期最大仓位
        }
        
        max_single_position: float = 0.15  # 单票最大仓位
        max_theme_position: float = 0.40   # 同一题材最大仓位
        
        # 风控参数
        stop_loss_ratio: float = 0.05      # 止损比例
        take_profit_ratio: float = 0.15    # 止盈比例
        max_holding_days: int = 5          # 最大持仓天数
        
        # 技术指标参数
        technical_params: Dict[str, int] = {
            "rsi_period": 14,
            "macd_fast": 12,
            "macd_slow": 26,
            "macd_signal": 9,
            "bb_period": 20,
            "bb_std": 2,
            "ma_period": 20
        }
    
    strategy: StrategyConfig = StrategyConfig()
    
    # 系统监控配置
    class MonitoringConfig:
        enable_prometheus: bool = True
        prometheus_port: int = 8000
        
        enable_logging: bool = True
        log_level: str = "INFO"
        log_file: str = "logs/trading_system.log"
        
        alert_email: str = ""
        alert_phone: str = ""
        
        # 风险监控阈值
        max_drawdown_alert: float = 0.10   # 最大回撤预警
        daily_loss_alert: float = 0.05     # 日亏损预警
        position_concentration_alert: float = 0.30  # 仓位集中度预警
    
    monitoring: MonitoringConfig = MonitoringConfig()
    
    # API配置
    api_host: str = "0.0.0.0"
    api_port: int = 8080
    api_workers: int = 4
    
    # 安全配置
    secret_key: str = "your-secret-key-here"
    access_token_expire_minutes: int = 30
    
    class Config:
        env_file = ".env"
        env_file_encoding = 'utf-8'

# 全局配置实例
settings = Settings()

# 市场时间配置
MARKET_HOURS = {
    "morning_start": "09:30:00",
    "morning_end": "11:30:00", 
    "afternoon_start": "13:00:00",
    "afternoon_end": "15:00:00"
}

# 交易日历配置
TRADING_CALENDAR = {
    "weekdays": [0, 1, 2, 3, 4],  # 周一到周五
    "holidays": [  # 节假日列表（需要定期更新）
        "2024-01-01",  # 元旦
        "2024-02-10",  # 春节
        # ... 其他节假日
    ]
}

# 数据更新频率配置
UPDATE_INTERVALS = {
    "real_time_data": 1,        # 实时数据更新间隔（秒）
    "sentiment_calculation": 60, # 情绪计算间隔（秒）
    "theme_analysis": 300,      # 题材分析间隔（秒）
    "risk_monitoring": 30,      # 风险监控间隔（秒）
    "position_update": 10       # 仓位更新间隔（秒）
}

# 数据存储配置
DATA_RETENTION = {
    "tick_data": 7,             # tick数据保留天数
    "minute_data": 90,          # 分钟数据保留天数
    "daily_data": 3650,         # 日线数据保留天数
    "sentiment_data": 365,      # 情绪数据保留天数
    "signal_data": 180,         # 信号数据保留天数
    "trade_data": 1095          # 交易数据保留天数
} 