#!/usr/bin/env python3
"""
测试akshare最新API - 涨停跌停股票数据
"""
import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import akshare as ak
    print(f"✅ akshare版本: {ak.__version__}")
except ImportError:
    print("❌ akshare未安装")
    sys.exit(1)


def test_limit_up_api():
    """测试涨停股票API"""
    print("\n🧪 测试涨停股票API...")
    
    current_date = datetime.now().strftime('%Y%m%d')
    print(f"测试日期: {current_date}")
    
    # 测试不同的API调用方式
    api_tests = [
        ("stock_zt_pool_em()", lambda: ak.stock_zt_pool_em()),
        ("stock_zt_pool_em(date=current_date)", lambda: ak.stock_zt_pool_em(date=current_date)),
        ("stock_zt_pool_strong_em()", lambda: ak.stock_zt_pool_strong_em()),
        ("stock_zt_pool_sub_new_em()", lambda: ak.stock_zt_pool_sub_new_em()),
    ]
    
    for api_name, api_func in api_tests:
        try:
            print(f"\n📊 测试 {api_name}:")
            data = api_func()
            
            if data is not None and not data.empty:
                print(f"   ✅ 成功获取数据: {len(data)}条记录")
                print(f"   📋 列名: {list(data.columns)}")
                print(f"   📄 前3条数据:")
                print(data.head(3).to_string(index=False))
                
                # 检查是否有连板相关信息
                board_columns = ['连板天数', '几天几板', '连板', '板数', '涨停天数', '连续涨停天数']
                found_columns = [col for col in board_columns if col in data.columns]
                if found_columns:
                    print(f"   🎯 发现连板相关列: {found_columns}")
                    for col in found_columns:
                        try:
                            max_val = data[col].max()
                            print(f"      {col}最大值: {max_val}")
                        except:
                            print(f"      {col}无法计算最大值")
                else:
                    print("   ⚠️  未发现连板相关列")
                    
            else:
                print(f"   ⚠️  获取数据为空")
                
        except Exception as e:
            print(f"   ❌ API调用失败: {e}")


def test_limit_down_api():
    """测试跌停股票API"""
    print("\n🧪 测试跌停股票API...")
    
    current_date = datetime.now().strftime('%Y%m%d')
    
    # 测试可能的跌停API
    api_tests = [
        ("stock_dt_pool_em()", lambda: ak.stock_dt_pool_em()),
        ("stock_dt_pool_em(date=current_date)", lambda: ak.stock_dt_pool_em(date=current_date)),
    ]
    
    for api_name, api_func in api_tests:
        try:
            print(f"\n📊 测试 {api_name}:")
            data = api_func()
            
            if data is not None and not data.empty:
                print(f"   ✅ 成功获取数据: {len(data)}条记录")
                print(f"   📋 列名: {list(data.columns)}")
                print(f"   📄 前3条数据:")
                print(data.head(3).to_string(index=False))
            else:
                print(f"   ⚠️  获取数据为空")
                
        except Exception as e:
            print(f"   ❌ API调用失败: {e}")


def test_stock_basic_info_api():
    """测试股票基础信息API"""
    print("\n🧪 测试股票基础信息API...")
    
    api_tests = [
        ("stock_info_a_code_name()", lambda: ak.stock_info_a_code_name()),
        ("stock_zh_a_spot_em()", lambda: ak.stock_zh_a_spot_em()),
    ]
    
    for api_name, api_func in api_tests:
        try:
            print(f"\n📊 测试 {api_name}:")
            data = api_func()
            
            if data is not None and not data.empty:
                print(f"   ✅ 成功获取数据: {len(data)}条记录")
                print(f"   📋 列名: {list(data.columns)}")
                print(f"   📄 前5条数据:")
                print(data.head(5).to_string(index=False))
            else:
                print(f"   ⚠️  获取数据为空")
                
        except Exception as e:
            print(f"   ❌ API调用失败: {e}")


def test_stock_hist_api():
    """测试股票历史数据API"""
    print("\n🧪 测试股票历史数据API...")
    
    symbol = "000001"
    start_date = "20241201"
    end_date = "20241217"
    
    api_tests = [
        ("stock_zh_a_hist()", lambda: ak.stock_zh_a_hist(
            symbol=symbol, 
            period="daily", 
            start_date=start_date, 
            end_date=end_date, 
            adjust=""
        )),
    ]
    
    for api_name, api_func in api_tests:
        try:
            print(f"\n📊 测试 {api_name} (股票: {symbol}):")
            data = api_func()
            
            if data is not None and not data.empty:
                print(f"   ✅ 成功获取数据: {len(data)}条记录")
                print(f"   📋 列名: {list(data.columns)}")
                print(f"   📄 最近3天数据:")
                print(data.tail(3).to_string(index=False))
            else:
                print(f"   ⚠️  获取数据为空")
                
        except Exception as e:
            print(f"   ❌ API调用失败: {e}")


def test_news_api():
    """测试新闻数据API"""
    print("\n🧪 测试新闻数据API...")
    
    api_tests = [
        ("stock_news_em()", lambda: ak.stock_news_em()),
    ]
    
    for api_name, api_func in api_tests:
        try:
            print(f"\n📊 测试 {api_name}:")
            data = api_func()
            
            if data is not None and not data.empty:
                print(f"   ✅ 成功获取数据: {len(data)}条记录")
                print(f"   📋 列名: {list(data.columns)}")
                print(f"   📄 前3条新闻标题:")
                for i, row in data.head(3).iterrows():
                    title = row.get('新闻标题', row.get('标题', '无标题'))
                    print(f"      {i+1}. {title}")
            else:
                print(f"   ⚠️  获取数据为空")
                
        except Exception as e:
            print(f"   ❌ API调用失败: {e}")


def check_akshare_functions():
    """检查akshare可用的函数"""
    print("\n🔍 检查akshare可用函数...")
    
    # 查找涨停跌停相关函数
    zt_functions = [attr for attr in dir(ak) if 'zt' in attr.lower()]
    dt_functions = [attr for attr in dir(ak) if 'dt' in attr.lower()]
    pool_functions = [attr for attr in dir(ak) if 'pool' in attr.lower()]
    
    print(f"📋 涨停相关函数 (zt): {zt_functions}")
    print(f"📋 跌停相关函数 (dt): {dt_functions}")
    print(f"📋 股票池相关函数 (pool): {pool_functions}")
    
    # 查找股票基础信息相关函数
    stock_info_functions = [attr for attr in dir(ak) if 'stock_info' in attr.lower()]
    print(f"📋 股票信息相关函数: {stock_info_functions}")


def main():
    """主测试函数"""
    print("🚀 开始测试akshare最新API")
    print("=" * 60)
    
    # 检查可用函数
    check_akshare_functions()
    
    # 测试各种API
    test_limit_up_api()
    test_limit_down_api()
    test_stock_basic_info_api()
    test_stock_hist_api()
    test_news_api()
    
    print("\n" + "=" * 60)
    print("🎉 akshare API测试完成！")
    
    print("\n💡 测试总结:")
    print("1. 如果涨停API成功，说明可以获取实时涨停数据")
    print("2. 如果跌停API失败，可能需要使用其他方式获取跌停数据")
    print("3. 如果股票基础信息API成功，说明可以获取股票列表")
    print("4. 如果历史数据API成功，说明可以获取K线数据")
    print("5. 如果新闻API成功，说明可以获取新闻数据")
    
    print("\n🔧 下一步建议:")
    print("1. 根据成功的API更新data_manager.py")
    print("2. 为失败的API实现fallback机制")
    print("3. 添加数据缓存以提高稳定性")
    print("4. 配置数据源账号以获取更稳定的数据")


if __name__ == "__main__":
    main()
