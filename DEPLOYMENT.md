# AIQuant7 系统部署文档

## 📋 目录
- [系统概述](#系统概述)
- [环境要求](#环境要求)
- [快速部署](#快速部署)
- [详细部署步骤](#详细部署步骤)
- [配置说明](#配置说明)
- [系统验证](#系统验证)
- [常见问题](#常见问题)
- [维护指南](#维护指南)

## 🎯 系统概述

AIQuant7 是一个基于人工智能的量化交易系统，包含以下核心组件：

- **前端界面**: React + Ant Design 构建的现代化Web界面
- **后端API**: FastAPI 构建的高性能RESTful API服务
- **数据管理**: 基于CSV/Feather文件的轻量级数据存储
- **策略引擎**: 集成情绪分析、题材识别、龙头股识别、信号生成等功能
- **实时数据**: 支持东方财富等数据源的实时市场数据获取

## 🔧 环境要求

### 系统要求
- **操作系统**: macOS 10.15+, Ubuntu 18.04+, Windows 10+
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 2GB 可用空间
- **网络**: 稳定的互联网连接（用于获取实时数据）

### 软件依赖
- **Python**: 3.8+ (推荐 3.9+)
- **Node.js**: 16+ (推荐 18+)
- **npm**: 8+ (随Node.js安装)

## 🚀 快速部署

### 一键部署脚本

```bash
# 1. 克隆项目
git clone <repository-url>
cd AIQuant7

# 2. 运行部署脚本
chmod +x deploy.sh
./deploy.sh

# 3. 启动系统
./start.sh
```

### 手动部署（推荐）

```bash
# 1. 安装Python依赖
pip install -r requirements.txt

# 2. 安装Node.js依赖
cd frontend
npm install
cd ..

# 3. 启动后端服务
python -m uvicorn backend.main:app --host 0.0.0.0 --port 8080 --reload

# 4. 启动前端服务（新终端）
cd frontend
npm start
```

## 📝 详细部署步骤

### 步骤1: 环境准备

#### 安装Python
```bash
# macOS (使用Homebrew)
brew install python@3.9

# Ubuntu
sudo apt update
sudo apt install python3.9 python3.9-pip

# Windows
# 从 https://python.org 下载并安装
```

#### 安装Node.js
```bash
# macOS (使用Homebrew)
brew install node

# Ubuntu
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Windows
# 从 https://nodejs.org 下载并安装
```

### 步骤2: 项目配置

#### 创建虚拟环境（推荐）
```bash
python -m venv venv
source venv/bin/activate  # macOS/Linux
# 或
venv\Scripts\activate     # Windows
```

#### 安装Python依赖
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

#### 安装前端依赖
```bash
cd frontend
npm install
cd ..
```

### 步骤3: 配置文件设置

#### 创建配置文件
```bash
cp config/settings.example.py config/settings.py
```

#### 编辑配置文件
```python
# config/settings.py
class Settings:
    # 数据存储配置
    DATA_DIR = "data"
    
    # API配置
    API_HOST = "0.0.0.0"
    API_PORT = 8080
    
    # 前端配置
    FRONTEND_PORT = 3000
    
    # 数据源配置
    ENABLE_TUSHARE = False  # 如需要，设置为True并配置token
    TUSHARE_TOKEN = ""      # 在此填入你的Tushare token
    
    # 缓存配置
    ENABLE_REDIS = False    # 如需要，设置为True
    REDIS_URL = "redis://localhost:6379"
```

### 步骤4: 启动服务

#### 启动后端服务
```bash
# 开发模式（推荐）
python -m uvicorn backend.main:app --host 0.0.0.0 --port 8080 --reload

# 生产模式
python -m uvicorn backend.main:app --host 0.0.0.0 --port 8080 --workers 4
```

#### 启动前端服务
```bash
cd frontend
npm start
```

## ⚙️ 配置说明

### 数据源配置

#### 东方财富数据（默认启用）
- 无需额外配置
- 自动获取实时涨停、跌停数据
- 支持新闻数据获取

#### Tushare数据（可选）
```python
# 在 config/settings.py 中配置
ENABLE_TUSHARE = True
TUSHARE_TOKEN = "your_tushare_token_here"
```

### 缓存配置

#### 内存缓存（默认）
- 无需额外配置
- 适合单机部署

#### Redis缓存（可选）
```bash
# 安装Redis
brew install redis  # macOS
sudo apt install redis-server  # Ubuntu

# 启动Redis
redis-server

# 在配置文件中启用
ENABLE_REDIS = True
REDIS_URL = "redis://localhost:6379"
```

### 日志配置
```python
# 日志级别配置
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR

# 日志文件配置
LOG_FILE = "logs/aiquant7.log"
```

## ✅ 系统验证

### 运行集成测试
```bash
python test_system_integration.py
```

### 手动验证

#### 1. 检查后端健康状态
```bash
curl http://localhost:8080/health
```

#### 2. 检查前端访问
打开浏览器访问: http://localhost:3000

#### 3. 检查API文档
打开浏览器访问: http://localhost:8080/docs

#### 4. 测试核心功能
- 实时市场数据: http://localhost:8080/api/v1/market/realtime
- 市场情绪分析: http://localhost:8080/api/v1/sentiment/current
- 题材排行: http://localhost:8080/api/v1/themes/ranking
- 龙头股排行: http://localhost:8080/api/v1/leaders/ranking
- 交易信号: http://localhost:8080/api/v1/signals/latest

## 🔧 常见问题

### Q1: 端口被占用
```bash
# 查找占用端口的进程
lsof -i :8080
lsof -i :3000

# 杀死进程
kill -9 <PID>

# 或修改配置文件中的端口
```

### Q2: Python依赖安装失败
```bash
# 升级pip
pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 如果是M1 Mac，可能需要
arch -arm64 pip install -r requirements.txt
```

### Q3: Node.js依赖安装失败
```bash
# 清理npm缓存
npm cache clean --force

# 使用国内镜像
npm install --registry https://registry.npm.taobao.org

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### Q4: 数据获取失败
- 检查网络连接
- 确认防火墙设置
- 检查数据源API是否可用

### Q5: 前端无法连接后端
- 确认后端服务已启动
- 检查CORS配置
- 确认代理配置正确

## 🛠 维护指南

### 日常维护

#### 1. 日志监控
```bash
# 查看实时日志
tail -f logs/aiquant7.log

# 查看错误日志
grep ERROR logs/aiquant7.log
```

#### 2. 数据清理
```bash
# 清理过期数据（保留最近30天）
python scripts/cleanup_data.py --days 30

# 清理缓存
python scripts/clear_cache.py
```

#### 3. 性能监控
```bash
# 检查系统资源使用
htop
df -h

# 检查API响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8080/api/v1/market/realtime
```

### 备份策略

#### 1. 数据备份
```bash
# 备份数据目录
tar -czf backup_$(date +%Y%m%d).tar.gz data/

# 定期备份脚本
0 2 * * * /path/to/backup_script.sh
```

#### 2. 配置备份
```bash
# 备份配置文件
cp config/settings.py config/settings.backup.py
```

### 更新升级

#### 1. 代码更新
```bash
git pull origin main
pip install -r requirements.txt
cd frontend && npm install && cd ..
```

#### 2. 重启服务
```bash
# 重启后端
pkill -f uvicorn
python -m uvicorn backend.main:app --host 0.0.0.0 --port 8080 --reload

# 重启前端
cd frontend
npm start
```

## 📞 技术支持

如遇到部署问题，请：

1. 查看日志文件获取详细错误信息
2. 运行系统集成测试确认问题范围
3. 检查配置文件是否正确
4. 确认所有依赖已正确安装

---

**部署完成后，您的AIQuant7系统将在以下地址可用：**
- 🌐 前端界面: http://localhost:3000
- 📖 API文档: http://localhost:8080/docs
- 🏥 健康检查: http://localhost:8080/health

祝您使用愉快！ 🎉
