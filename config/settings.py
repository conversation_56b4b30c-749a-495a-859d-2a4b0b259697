"""
系统配置文件
"""
from pydantic_settings import BaseSettings
from typing import Dict, Any, List
import os


class DataStorageSettings(BaseSettings):
    """数据存储配置"""
    # 数据存储根目录
    data_root_dir: str = "data"

    # 各类数据子目录
    market_data_dir: str = "market_data"
    sentiment_data_dir: str = "sentiment_data"
    theme_data_dir: str = "theme_data"
    signal_data_dir: str = "signal_data"
    position_data_dir: str = "position_data"
    stock_basic_dir: str = "stock_basic"

    # 文件格式配置
    file_format: str = "feather"  # feather 或 csv
    compression: str = "lz4"      # feather压缩格式: lz4, zstd, uncompressed

    # Redis (仅用于缓存)
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_password: str = ""
    redis_db: int = 0

    # 数据保留策略
    market_data_retention_days: int = 365  # 市场数据保留天数
    signal_data_retention_days: int = 90   # 信号数据保留天数

    @property
    def get_data_path(self) -> str:
        """获取数据存储路径"""
        import os
        return os.path.abspath(self.data_root_dir)


class DataSourceSettings(BaseSettings):
    """数据源配置"""
    # Tushare
    tushare_token: str = ""
    
    # 聚宽
    jqdata_username: str = ""
    jqdata_password: str = ""
    
    # 米筐
    rqdata_username: str = ""
    rqdata_password: str = ""
    
    # 同花顺iFinD
    ifind_username: str = ""
    ifind_password: str = ""
    
    # 雪球
    xueqiu_token: str = ""


class KafkaSettings(BaseSettings):
    """Kafka配置"""
    bootstrap_servers: List[str] = ["localhost:9092"]
    group_id: str = "aiquant7"
    auto_offset_reset: str = "latest"
    enable_auto_commit: bool = True
    
    # 主题配置
    topics: Dict[str, str] = {
        "market_data": "market_data",
        "sentiment": "sentiment_analysis", 
        "themes": "theme_analysis",
        "signals": "trading_signals",
        "positions": "position_updates"
    }


class StrategySettings(BaseSettings):
    """策略配置"""
    # 市场情绪权重
    sentiment_weights: Dict[str, float] = {
        "limit_up_weight": 2.0,
        "limit_down_weight": -3.0,
        "continuous_board_weight": 5.0,
        "volume_weight": 1.0
    }
    
    # 题材评分权重
    theme_weights: Dict[str, float] = {
        "policy": 4.0,      # 政策驱动
        "industry": 3.0,    # 行业拐点
        "event": 2.0,       # 事件催化
        "concept": 1.0      # 主题炒作
    }
    
    # 龙头股识别权重
    leader_weights: Dict[str, float] = {
        "return_rank": 0.3,      # 涨幅排名
        "volume_rank": 0.25,     # 成交额排名  
        "board_height": 0.2,     # 连板高度
        "concept_purity": 0.15,  # 概念纯正度
        "market_cap": 0.1        # 流通市值适中度
    }
    
    # 仓位管理配置
    position_limits: Dict[str, float] = {
        "max_single_position": 0.15,    # 单票最大仓位15%
        "max_theme_position": 0.40,     # 同题材最大仓位40%
        "max_total_position": 0.80,     # 最大总仓位80%
    }
    
    # 基于市场情绪的仓位控制
    sentiment_position_limits: Dict[str, float] = {
        "冰点期": 0.30,
        "回暖期": 0.70,
        "发酵期": 0.60,
        "高潮期": 0.40,
        "退潮期": 0.20,
        "混沌期": 0.00
    }
    
    # 风控参数
    risk_params: Dict[str, float] = {
        "stop_loss_ratio": 0.05,        # 止损比例5%
        "take_profit_ratio": 0.15,      # 止盈比例15%
        "max_holding_days": 5,          # 最大持仓天数
        "max_drawdown": 0.10,           # 最大回撤10%
    }


class APISettings(BaseSettings):
    """API配置"""
    host: str = "0.0.0.0"
    port: int = 8080
    debug: bool = False
    reload: bool = False
    
    # JWT配置
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # CORS配置
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:3001"]
    cors_methods: List[str] = ["GET", "POST", "PUT", "DELETE"]
    cors_headers: List[str] = ["*"]


class LoggingSettings(BaseSettings):
    """日志配置"""
    level: str = "INFO"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    rotation: str = "1 day"
    retention: str = "30 days"
    compression: str = "zip"
    
    # 日志文件路径
    log_dir: str = "logs"
    app_log_file: str = "app.log"
    trading_log_file: str = "trading.log"
    error_log_file: str = "error.log"


class MonitoringSettings(BaseSettings):
    """监控配置"""
    # Prometheus
    prometheus_port: int = 8000
    
    # 告警阈值
    alert_thresholds: Dict[str, float] = {
        "high_latency_seconds": 2.0,
        "high_error_rate": 0.05,
        "high_drawdown": 0.10,
        "low_accuracy": 0.60
    }


class Settings(BaseSettings):
    """主配置类"""
    app_name: str = "AIQuant7"
    version: str = "1.0.0"
    description: str = "游资超短线交易策略系统"
    
    # 环境配置
    environment: str = "development"
    debug: bool = True
    
    # 子配置
    data_storage: DataStorageSettings = DataStorageSettings()
    data_source: DataSourceSettings = DataSourceSettings()
    kafka: KafkaSettings = KafkaSettings()
    strategy: StrategySettings = StrategySettings()
    api: APISettings = APISettings()
    logging: LoggingSettings = LoggingSettings()
    monitoring: MonitoringSettings = MonitoringSettings()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局设置实例
settings = Settings()
